# Default Theme

```json
{
  "_id": "APPSTHEM679683c3cc7905d32706e355",
  "themeName": "Default Theme",
  "layout": {
    "$typeName": "tnt.theme_service.v1.Layout",
    "gridTemplateAreas": "\"header header header\"\"content content content\"\"footer footer footer\"",
    "gridTemplateColumns": "auto 1fr 300px",
    "gridTemplateRows": "auto 1fr auto"
  },
  "overrides": [],
  "palette": {
    "mode": "light",
    "primary": {
      "main": "#4A4A4A",
      "light": "#6E6E6E",
      "dark": "#222222",
      "contrastText": "#FFFFFF"
    },
    "secondary": {
      "main": "#1976D2",
      "light": "#63A4FF",
      "dark": "#004BA0",
      "contrastText": "#FFFFFF"
    },
    "background": {
      "default": "#FFFFFF",
      "paper": "#FDFDFD"
    },
    "text": {
      "primary": "#111111",
      "secondary": "#333333",
      "disabled": "#BDBDBD"
    },
    "grey": {
      "100": "#F5F5F5",
      "300": "#E0E0E0",
      "500": "#B0B0B0",
      "700": "#616161",
      "900": "#222222"
    },
    "success": {
      "main": "#388E3C",
      "contrastText": "#FFFFFF"
    },
    "error": {
      "main": "#D32F2F",
      "contrastText": "#FFFFFF"
    },
    "warning": {
      "main": "#FBC02D",
      "contrastText": "#222222"
    },
    "info": {
      "main": "#1976D2",
      "contrastText": "#FFFFFF"
    }
  },
  "typography": {
    "fontFamily": "Inter, sans-serif",
    "h1": {
      "fontSize": "2rem",
      "fontWeight": 600
    },
    "h2": {
      "fontSize": "1.5rem",
      "fontWeight": 600
    },
    "body1": {
      "fontSize": "1rem"
    },
    "body2": {
      "fontSize": "0.875rem"
    },
    "button": {
      "fontWeight": 600
    }
  },
  "zones": {
    "header": {
      "visible": true,
      "fill": "#DDDDDD"
    },
    "aside": {
      "visible": true,
      "fill": "transparent",
      "resizable": true
    },
    "content": {
      "visible": true,
      "fill": "transparent",
      "scrollable": true
    },
    "tools": {
      "visible": true,
      "fill": "transparent",
      "collapsible": true
    },
    "flow": {
      "visible": true,
      "fill": "transparent",
      "fullscreen": true
    },
    "footer": {
      "visible": true,
      "fill": "#F5F5F5"
    }
  },
  "_updatedAt": {
    "$date": "2025-07-09T12:25:28.005Z"
  },
  "spacing": 8,
  "version": 34
}
```
