import type { Rule } from 'eslint';

export const warnAsConstRule: Rule.RuleModule = {
  meta: {
    type: 'suggestion',
    docs: {
      description: 'warn when as const type assertions are used',
      category: 'Best Practices',
      recommended: false,
    },
    schema: [], // No options for this rule
  },
  create(context: Rule.RuleContext): Rule.RuleListener {
    return {
      // Handle TypeScript "as const" expressions
      TSAsExpression(node: any): void {
        if (
          node.typeAnnotation?.type === 'TSTypeReference' &&
          node.typeAnnotation.typeName?.type === 'Identifier' &&
          node.typeAnnotation.typeName.name === 'const'
        ) {
          context.report({
            node: node,
            message:
              'Consider avoiding "as const" assertions. Use specific typing or satisfies operator when possible.',
          });
        }
      },
      // Handle legacy "<const>" style assertions (less common but still valid)
      TSTypeAssertion(node: any): void {
        if (
          node.typeAnnotation?.type === 'TSTypeReference' &&
          node.typeAnnotation.typeName?.type === 'Identifier' &&
          node.typeAnnotation.typeName.name === 'const'
        ) {
          context.report({
            node: node,
            message:
              'Consider avoiding "as const" assertions. Use specific typing or satisfies operator when possible.',
          });
        }
      },
    };
  },
};
