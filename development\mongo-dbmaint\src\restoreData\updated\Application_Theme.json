{"dbName": "Application", "collectionName": "Theme", "schemaName": "ThemeSchema", "availableActions": ["delete-insert", "append", "delete"], "action": "delete-insert", "indexes": [], "data": [{"_id": "APPSTHEM679683c3cc7905d32706e355", "themeName": "Main", "layout": {"$typeName": "tnt.theme_service.v1.Layout", "gridTemplateAreas": "\"header header header\"\"aside content tools\"\"footer footer footer\"", "gridTemplateColumns": "auto 1fr auto", "gridTemplateRows": "auto 1fr auto"}, "overrides": [], "palette": {"primary": {"main": "#ffffff", "light": "#63a4ff", "dark": "#004ba0", "contrastText": "#111111"}, "secondary": {"main": "#336699", "light": "#ff5983", "dark": "#9a0036", "contrastText": "#ffffff"}}, "typography": {"fontFamily": "Roboto, sans-serif", "fontSize": 14}, "zones": {"header": {"visible": true, "fill": "#666666"}}}]}