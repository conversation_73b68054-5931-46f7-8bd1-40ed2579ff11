{"dbName": "ResourceObjects", "collectionName": "Contacts", "schemaName": "ContactSchema", "availableActions": ["delete-insert", "append", "delete"], "action": "delete-insert", "indexes": [], "data": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organization": "ROBJENTI", "department": "ROBJGROU", "team": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR"], "phone": [{"number": "************", "type": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organization": "ROBJENTI", "department": "ROBJGROU", "team": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR"], "phone": [{"number": "************", "type": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organization": "ROBJENTI", "department": "ROBJGROU", "team": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "address": ["ROBJADDR"], "phone": [{"number": "************", "type": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}]}