{"dbName": "ResourceObjects", "collectionName": "Documents", "schemaName": "DocumentSchema", "availableActions": ["delete-insert", "append", "delete"], "action": "delete-insert", "indexes": [], "data": [{"_id": "ROBJDOCU", "isActive": true, "name": "myAccountNumber", "description": "a great book", "tags": ["goodBook", "scifi"], "createdBy": "ROBJCONT", "createdOn": "2024-11-20", "documentVersions": [{"_id": "", "versionId": "5551212", "versionName": "latest", "versionDescription": "includes new chapters", "filePathURL": "https://fileserver.org/myDocuments/_id", "fileType": "ROBJITYP", "tags": ["best version"], "createdBy": "ROBJCONT", "createdOn": "", "validFrom": "", "validTo": "2028-01-01"}]}]}