{"dbName": "ResourceObjects", "collectionName": "Entities", "schemaName": "EntitySchema", "availableActions": ["delete-insert", "append", "delete"], "action": "delete-insert", "indexes": [], "data": [{"_id": "ROBJENTI673e14ecf46700d0d946dec3", "isActive": true, "accountNum": "myAcct883284598_newdata", "name": "MachAET", "description": "best company in the world", "legal_designation": "LLC", "typeId": "ROBJTYPE", "parentId": "ROBJENTI", "addressId": "ROBJADDR", "phone": [{"number": "************", "typeId": "ROBJTYPE", "description": "use only after 5pm", "validFrom": "", "validTo": ""}, {"number": "************", "typeId": "ROBJTYPE", "description": "use only before 5pm", "validFrom": "", "validTo": ""}], "webURL": "https://www.machaet.com", "createdBy": "ROBJCONT", "createdOn": ""}, {"_id": "ROBJENTI673e14ecf46700d0d946dec9", "isActive": true, "accountNum": "myAcct55124598", "name": "BryBo Coding", "description": "senior crust programming", "legal_designation": "LLC", "typeId": "ROBJTYPE", "parentId": "ROBJENTI", "addressId": "ROBJADDR", "phone": [{"number": "************", "typeId": "ROBJTYPE", "description": "use only after 5pm", "validFrom": "", "validTo": ""}, {"number": "************", "typeId": "ROBJTYPE", "description": "use only in dire emergencies", "validFrom": "", "validTo": ""}], "webURL": "https://www.brybothebest.com", "createdBy": "ROBJCONT", "createdOn": ""}]}