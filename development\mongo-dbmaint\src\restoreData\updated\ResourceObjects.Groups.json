{"dbName": "ResourceObjects", "collectionName": "Groups", "schemaName": "GroupSchema", "availableActions": ["delete-insert", "append", "delete"], "action": "delete-insert", "indexes": [], "data": [{"_id": "ROBJGROU6732728445da61a0c3ae49c0", "isActive": true, "accountNum": "551223", "name": "IAMWorkEntryDataGroup", "tagIds": ["ROBJITYP"], "description": "data group for IAM of ResourceObjects.Documents"}, {"_id": "ROBJGROU6732728445da61a0c3ae49c1", "isActive": true, "accountNum": "551223", "name": "IAMCheckListDataGroup", "tagIds": ["ROBJITYP"], "description": "data group for IAM of ResourceObjects.Documents"}, {"_id": "ROBJGROU6732728445da61a0c3ae49c2", "isActive": true, "accountNum": "551223", "name": "Executives", "tagIds": ["ROBJITYP"], "description": ""}, {"_id": "ROBJGROU6732728445da61a0c3ae49c3", "isActive": true, "accountNum": "551223", "name": "HR Senior", "tagIds": ["ROBJITYP"], "description": ""}, {"_id": "ROBJGROU6732728445da61a0c3ae49c4", "isActive": true, "accountNum": "551223", "name": "HR Mid-level", "tagIds": ["ROBJITYP"], "description": ""}, {"_id": "ROBJGROU6732728445da61a0c3ae49c5", "isActive": true, "accountNum": "551223", "name": "HR Entry-level", "tagIds": ["ROBJITYP"], "description": ""}, {"_id": "ROBJGROU6732728445da61a0c3ae49c6", "isActive": true, "accountNum": "551223", "name": "Management Mid-level", "tagIds": ["ROBJITYP"], "description": ""}, {"_id": "ROBJGROU6732728445da61a0c3ae49c7", "isActive": true, "accountNum": "551223", "name": "Team Lead", "tagIds": ["ROBJITYP"], "description": ""}, {"_id": "ROBJGROU6732728445da61a0c3ae49c8", "isActive": true, "accountNum": "551223", "name": "Team Member", "tagIds": ["ROBJITYP"], "description": "lowest level of access"}, {"_id": "ROBJGROU6732728445da61a0c3ae49c9", "isActive": true, "accountNum": "551223", "name": "Contractor", "tagIds": ["ROBJITYP"], "description": "should have no access"}, {"_id": "ROBJGROU6732728445da61a0c3ae49ca", "isActive": true, "accountNum": "551223", "name": "DryWall Equipment", "tagIds": ["ROBJITYP"], "description": "testing for access to drywall equipment"}, {"_id": "ROBJGROU6732728445da61a0c3ae49cb", "isActive": true, "accountNum": "551223", "name": "Vehicles", "tagIds": ["ROBJITYP"], "description": "group of vehicles(individual equipment)"}, {"_id": "ROBJGROU6732728445da61a0c3ae49cc", "isActive": true, "accountNum": "551223", "name": "Computers", "tagIds": ["ROBJITYP"], "description": "testing for access to computers"}, {"_id": "ROBJGROU6732728445da61a0c3ae49cd", "isActive": true, "accountNum": "551223", "name": "Drivers", "tagIds": ["ROBJITYP"], "description": "testing for access to driving vehicles"}]}