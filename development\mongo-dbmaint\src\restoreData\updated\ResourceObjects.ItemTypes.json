{"dbName": "ResourceObjects", "collectionName": "ItemTypes", "schemaName": "ItemTypeSchema", "availableActions": ["delete-insert", "append", "delete"], "action": "delete-insert", "indexes": [], "data": [{"_id": "ROBJITYP681d59b1e8b957a65632bf12", "itemGroup": "WorkEntryStatus", "itemName": "Complete-Fail", "itemDescription": "Item completed and failed", "itemValueNumber": -1, "itemValueString": "Complete-Fail"}, {"_id": "ROBJITYP681d59b169ad839a000fb374", "itemGroup": "WorkEntryStatus", "itemName": "Not-Started", "itemDescription": "Item not started", "itemValueNumber": 0, "itemValueString": "Not-Started"}, {"_id": "ROBJITYP681d59b1e9334fbe3f5e178a", "itemGroup": "WorkEntryStatus", "itemName": "In-Process", "itemDescription": "Item in process", "itemValueNumber": 1, "itemValueString": "In-Process"}, {"_id": "ROBJITYP681d59b12837f8315eae8a37", "itemGroup": "WorkEntryStatus", "itemName": "Complete-Success", "itemDescription": "Item fully completed with no issues", "itemValueNumber": 2, "itemValueString": "Complete-Success"}, {"_id": "ROBJITYP681d59b176ef1a26a26c87dc", "itemGroup": "WorkEntryStatus", "itemName": "Partial-Success", "itemDescription": "Item partially completed with no issues", "itemValueNumber": 3, "itemValueString": "Partial-Success"}, {"_id": "ROBJITYP681d59b17f642faaabfdf8f5", "itemGroup": "WorkEntryStatus", "itemName": "Partial-Issues", "itemDescription": "Item partially completed with issues", "itemValueNumber": 4, "itemValueString": "Partial-Issues"}, {"_id": "ROBJITYP000000000000000000000020", "itemGroup": "Telephone", "itemName": "mobile", "itemDescription": "cellular phone, satellite phone, radio phone", "itemValueNumber": 1, "itemValueString": "Level 1"}, {"_id": "ROBJITYP000000000000000000000021", "itemGroup": "Telephone", "itemName": "office", "itemDescription": "individual office line", "itemValueNumber": 2, "itemValueString": "Level 1"}, {"_id": "ROBJITYP000000000000000000000022", "itemGroup": "Telephone", "itemName": "organization", "itemDescription": "main organization line", "itemValueNumber": 3, "itemValueString": "Level 1"}, {"_id": "ROBJITYP000000000000000000000030", "itemGroup": "Address", "itemName": "Local Office", "itemDescription": "Local Office", "itemValueNumber": 3, "itemValueString": "Level 1"}, {"_id": "ROBJITYP000000000000000000000031", "itemGroup": "Address", "itemName": "Home", "itemDescription": "Home", "itemValueNumber": 3, "itemValueString": "Level 1"}, {"_id": "ROBJITYP000000000000000000000040", "itemGroup": "Entity-External", "itemName": "Company", "itemDescription": "default", "itemValueNumber": 3, "itemValueString": "Level 1"}, {"_id": "ROBJITYP000000000000000000000050", "itemGroup": "Entity-Internal", "itemName": "Department", "itemDescription": "", "itemValueNumber": 3, "itemValueString": "Level 1"}, {"_id": "ROBJITYP000000000000000000000051", "itemGroup": "Entity-Internal", "itemName": "Site", "itemDescription": "", "itemValueNumber": 3, "itemValueString": "Level 1"}, {"_id": "ROBJITYP000000000000000000000052", "itemGroup": "Entity-Internal", "itemName": "Branch", "itemDescription": "", "itemValueNumber": 3, "itemValueString": "Level 1"}, {"_id": "ROBJITYP000000000000000000000053", "itemGroup": "InventoryLocationDescriptor", "itemName": "levelId", "itemDescription": "", "itemValueNumber": 0, "itemValueString": "Level 1"}, {"_id": "ROBJITYP000000000000000000000054", "itemGroup": "InventoryLocationDescriptor", "itemName": "levelId", "itemDescription": "", "itemValueNumber": 0, "itemValueString": "Level 2"}, {"_id": "ROBJITYP000000000000000000000055", "itemGroup": "InventoryLocationDescriptor", "itemName": "aisleId", "itemDescription": "", "itemValueNumber": 0, "itemValueString": "Aisle 1"}, {"_id": "ROBJITYP000000000000000000000056", "itemGroup": "InventoryLocationDescriptor", "itemName": "aisleId", "itemDescription": "", "itemValueNumber": 0, "itemValueString": "Aisle 2"}, {"_id": "ROBJITYP000000000000000000000057", "itemGroup": "InventoryLocationDescriptor", "itemName": "rackId", "itemDescription": "", "itemValueNumber": 0, "itemValueString": "Rack 1"}, {"_id": "ROBJITYP000000000000000000000058", "itemGroup": "InventoryLocationDescriptor", "itemName": "rackId", "itemDescription": "", "itemValueNumber": 0, "itemValueString": "Rack 2"}, {"_id": "ROBJITYP000000000000000000000059", "itemGroup": "InventoryLocationDescriptor", "itemName": "shelfId", "itemDescription": "", "itemValueNumber": 0, "itemValueString": "Shelf 1"}, {"_id": "ROBJITYP000000000000000000000060", "itemGroup": "InventoryLocationDescriptor", "itemName": "shelfId", "itemDescription": "", "itemValueNumber": 0, "itemValueString": "Shelf 2"}, {"_id": "ROBJITYP000000000000000000000061", "itemGroup": "InventoryLocationDescriptor", "itemName": "binId", "itemDescription": "", "itemValueNumber": 0, "itemValueString": "Bin 1"}, {"_id": "ROBJITYP000000000000000000000062", "itemGroup": "InventoryLocationDescriptor", "itemName": "binId", "itemDescription": "", "itemValueNumber": 0, "itemValueString": "Bin 2"}, {"_id": "ROBJITYP000000000000000000000063", "itemGroup": "color", "itemName": "blue", "itemDescription": "", "itemValueNumber": 0, "itemValueString": "0x4555"}, {"_id": "ROBJITYP000000000000000000000064", "itemGroup": "color", "itemName": "red", "itemDescription": "", "itemValueNumber": 0, "itemValueString": "0x4552"}, {"_id": "ROBJITYP000000000000000000000065", "itemGroup": "color", "itemName": "yellow", "itemDescription": "", "itemValueNumber": 0, "itemValueString": "0x4550"}, {"_id": "ROBJITYP681d59b1816ac7be5ee4a7c0", "itemGroup": "Agile", "itemName": "epic", "itemDescription": "", "itemValueNumber": 0, "itemValueString": "0x4550"}, {"_id": "ROBJITYP681d59b1816ac7be5ee4a7c1", "itemGroup": "Agile", "itemName": "story", "itemDescription": "", "itemValueNumber": 0, "itemValueString": "0x4550"}]}