{"dbName": "ResourceObjects", "collectionName": "StructureSchemes", "schemaName": "StructureObjSchemeItemSchema", "availableActions": ["delete-insert", "append", "delete"], "action": "delete-insert", "indexes": [], "data": [{"_id": "ROBJSOSI", "scheme": "TNT", "name": "Job", "category": "Waterfall", "tags": ["mytag1", "mytag2"], "shape": "roundSquare", "image": "http:job.jpg", "thumbnail": "http://job_tb.jpg", "contentAllowed": true, "contentPathTypesAllowed": ["linear", "loop"], "resourceLogsAllowed": false, "checkLogsAllowed": false, "workDataAllowed": false, "documentsAllowed": true, "requirePrecedence": true}, {"_id": "ROBJSOSI", "scheme": "TNT", "name": "Project", "category": "Waterfall", "tags": ["mytag1", "mytag2"], "shape": "oblong", "image": "http:job.jpg", "thumbnail": "http://job_tb.jpg", "contentAllowed": true, "contentPathTypesAllowed": ["linear", "loop"], "resourceLogsAllowed": false, "checkLogsAllowed": false, "workDataAllowed": false, "documentsAllowed": true, "requirePrecedence": true}, {"_id": "ROBJSOSI", "scheme": "TNT", "name": "Task", "category": "Waterfall", "tags": ["mytag1", "mytag2"], "shape": "roundSquare", "image": "http:job.jpg", "thumbnail": "http://job_tb.jpg", "contentAllowed": true, "contentPathTypesAllowed": ["linear", "loop"], "resourceLogsAllowed": false, "checkLogsAllowed": false, "workDataAllowed": false, "documentsAllowed": true, "requirePrecedence": true}, {"_id": "ROBJSOSI", "scheme": "TNT", "name": "Ticket", "category": "Waterfall", "tags": ["mytag1", "mytag2"], "shape": "roundSquare", "image": "http:job.jpg", "thumbnail": "http://job_tb.jpg", "contentAllowed": true, "contentPathTypesAllowed": ["linear", "loop"], "resourceLogsAllowed": true, "checkLogsAllowed": true, "workDataAllowed": true, "documentsAllowed": true, "requirePrecedence": true}, {"_id": "ROBJSOSI", "scheme": "SCRUM", "name": "Sprint", "category": "Agile", "tags": ["mytag1", "mytag2"], "shape": "square", "image": "http:job.jpg", "thumbnail": "http://job_tb.jpg", "contentAllowed": true, "contentPathTypesAllowed": ["linear", "loop"], "resourceLogsAllowed": false, "checkLogsAllowed": false, "workDataAllowed": false, "documentsAllowed": true, "requirePrecedence": true}]}