{"dbName": "ResourceObjects", "collectionName": "StructureObjectTemplates", "schemaName": "StructureObjectTemplateSchema", "availableActions": ["delete-insert", "append", "delete"], "action": "delete-insert", "indexes": [], "data": [{"structureObjectType": "structureObjectStartTemplate", "_id": "ROBJSOBT", "structureRootReference": {"containedById": "ROBJSOBT", "containedByType": "structureObjectStart"}, "structureReference": {"containedById": "ROBJSOBT", "containedByType": "structureObjectStart"}, "name": "Start StructureObject", "displayName": "My SO Display Name", "description": "testing StructureObjects", "organizationalReferenceId": "myOrgID", "schemeItemId": "ROBJSSCH", "screenLocation": "some reference for GUI interface", "statusId": "ROBJITYP", "tags": ["best project", "first", "search tag2"], "isCompleted": false, "completedTimeStamp": "", "isSchedulingCurrent": false, "schedule": [{"_id": "", "validFrom": "2023-01-1", "validTo": "2028-01-1", "createdBy": {"_id": "ROBJCONT672bd5074a2abc656568ce65", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, "dates": {"startDateEstimated": "2024-11-10T03:58:34", "startDateActual": "2021-11-01T00:01:00.000+00:00", "completedDateActual": "2024-10-01", "dueDate": "2024-10-01", "duration": 10, "earlyStart": 3, "earlyFinish": 2, "lateStart": 5, "lateFinish": 8, "lead": 1, "lag": 2}}], "sourceTemplate": "ROBJSOBT", "isTrackingSourceTemplate": false, "isHold": false, "holdOwners": [], "RACI": "ROBJRACI", "RACIHistorical": {"responsible": [{"name": "group name", "description": "group description", "accountNum": "group account number", "tags": ["group tags1", "group tags2"], "members": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}]}], "accountable": {"title": "Scrum Master", "contact": {"_id": "ROBJCONT672bd5074a2abc656568ce5e", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}}, "consulted": [{"name": "group name", "description": "group description", "accountNum": "group account number", "tags": ["group tags1", "group tags2"], "members": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}]}], "informed": [{"name": "group name", "description": "group description", "accountNum": "group account number", "tags": ["group tags1", "group tags2"], "members": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}]}]}, "agileTaskName": "ROBJITYP", "agileTaskDescription": "this is an agile user story", "agileTaskPoints": 5, "agileTaskSize": "BigMac", "workGroup": ["ROBJGROU"], "checkLists": ["ROBJCHLS"], "checkSheets": ["ROBJCHSH"], "isExternal": false, "apiPreExecutionCallDefinition": "ROBJAPIB", "apiPostExecutionCallDefinition": "ROBJAPIA", "projectSponsor": [{"validFrom": "2023-01-1", "validTo": "2028-01-1", "title": "ROBJITYP", "contact": {"_id": "ROBJCONT672bd5074a2abc656568ce65", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}}], "projectLead": [{"validFrom": "2023-01-1", "validTo": "2028-01-1", "title": "ROBJITYP", "contact": {"_id": "ROBJCONT672bd5074a2abc656568ce65", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}}]}, {"structureObjectType": "structureObjectBaseTemplate", "_id": "ROBJSOBT", "structureRootReference": {"containedById": "ROBJSOBT", "containedByType": "structureObjectStart"}, "structureReference": {"containedById": "ROBJSOBT", "containedByType": "structureObjectStart"}, "name": "Base Structure Object", "displayName": "My SO Display Name", "description": "testing StructureObjects", "organizationalReferenceId": "myOrgID", "schemeItemId": "ROBJSSCH", "screenLocation": "some reference for GUI interface", "statusId": "ROBJITYP", "tags": ["best project", "first", "search tag2"], "isCompleted": false, "completedTimeStamp": "", "isSchedulingCurrent": false, "schedule": [{"_id": "", "validFrom": "2023-01-1", "validTo": "2028-01-1", "createdBy": {"_id": "ROBJCONT672bd5074a2abc656568ce65", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, "dates": {"startDateEstimated": "2024-11-10T03:58:34", "startDateActual": "2021-11-01T00:01:00.000+00:00", "completedDateActual": "2024-10-01", "dueDate": "2024-10-01", "duration": 10, "earlyStart": 3, "earlyFinish": 2, "lateStart": 5, "lateFinish": 8, "lead": 1, "lag": 2}}], "sourceTemplate": "ROBJSOBT", "isTrackingSourceTemplate": false, "isHold": false, "holdOwners": [], "RACI": "ROBJRACI", "RACIHistorical": {"responsible": [{"name": "group name", "description": "group description", "accountNum": "group account number", "tags": ["group tags1", "group tags2"], "members": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}]}], "accountable": {"title": "Scrum Master", "contact": {"_id": "ROBJCONT672bd5074a2abc656568ce5e", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}}, "consulted": [{"name": "group name", "description": "group description", "accountNum": "group account number", "tags": ["group tags1", "group tags2"], "members": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}]}], "informed": [{"name": "group name", "description": "group description", "accountNum": "group account number", "tags": ["group tags1", "group tags2"], "members": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}]}]}, "agileTaskName": "ROBJITYP", "agileTaskDescription": "this is an agile user story", "agileTaskPoints": 5, "agileTaskSize": "BigMac", "workGroup": ["ROBJGROU"], "checkLists": ["ROBJCHLS"], "checkSheets": ["ROBJCHSH"], "isExternal": false, "apiPreExecutionCallDefinition": "ROBJAPIB", "apiPostExecutionCallDefinition": "ROBJAPIA"}, {"structureObjectType": "structureObjectWorkTemplate", "_id": "ROBJSOBT", "structureRootReference": {"containedById": "ROBJSOBT", "containedByType": "structureObjectStart"}, "structureReference": {"containedById": "ROBJSOBT", "containedByType": "structureObjectStart"}, "name": "Work Structure Object", "displayName": "My SO Display Name", "description": "testing StructureObjects", "organizationalReferenceId": "myOrgID", "schemeItemId": "ROBJSSCH", "screenLocation": "some reference for GUI interface", "statusId": "ROBJITYP", "tags": ["best project", "first", "search tag2"], "isCompleted": false, "completedTimeStamp": "", "isSchedulingCurrent": false, "schedule": [{"_id": "", "validFrom": "2023-01-1", "validTo": "2028-01-1", "createdBy": {"_id": "ROBJCONT672bd5074a2abc656568ce65", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, "dates": {"startDateEstimated": "2024-11-10T03:58:34", "startDateActual": "2021-11-01T00:01:00.000+00:00", "completedDateActual": "2024-10-01", "dueDate": "2024-10-01", "duration": 10, "earlyStart": 3, "earlyFinish": 2, "lateStart": 5, "lateFinish": 8, "lead": 1, "lag": 2}}], "sourceTemplate": "ROBJSOBT", "isTrackingSourceTemplate": false, "isHold": false, "holdOwners": [], "RACI": "ROBJRACI", "RACIHistorical": {"responsible": [{"name": "group name", "description": "group description", "accountNum": "group account number", "tags": ["group tags1", "group tags2"], "members": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}]}], "accountable": {"title": "Scrum Master", "contact": {"_id": "ROBJCONT672bd5074a2abc656568ce5e", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}}, "consulted": [{"name": "group name", "description": "group description", "accountNum": "group account number", "tags": ["group tags1", "group tags2"], "members": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}]}], "informed": [{"name": "group name", "description": "group description", "accountNum": "group account number", "tags": ["group tags1", "group tags2"], "members": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}]}]}, "agileTaskName": "ROBJITYP", "agileTaskDescription": "this is an agile user story", "agileTaskPoints": 5, "agileTaskSize": "BigMac", "workGroup": ["ROBJGROU"], "checkLists": ["ROBJCHLS"], "checkSheets": ["ROBJCHSH"], "isExternal": false, "apiPreExecutionCallDefinition": "ROBJAPIB", "apiPostExecutionCallDefinition": "ROBJAPIA"}, {"structureObjectType": "structureObjectEndTemplate", "_id": "ROBJSOBT", "structureRootReference": {"containedById": "ROBJSOBT", "containedByType": "structureObjectStart"}, "structureReference": {"containedById": "ROBJSOBT", "containedByType": "structureObjectStart"}, "name": "End Structure Object", "displayName": "My SO Display Name", "description": "testing StructureObjects", "organizationalReferenceId": "myOrgID", "schemeItemId": "ROBJSSCH", "screenLocation": "some reference for GUI interface", "statusId": "ROBJITYP", "tags": ["best project", "first", "search tag2"], "isCompleted": false, "completedTimeStamp": "", "isSchedulingCurrent": false, "schedule": [{"_id": "", "validFrom": "2023-01-1", "validTo": "2028-01-1", "createdBy": {"_id": "ROBJCONT672bd5074a2abc656568ce65", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, "dates": {"startDateEstimated": "2024-11-10T03:58:34", "startDateActual": "2021-11-01T00:01:00.000+00:00", "completedDateActual": "2024-10-01", "dueDate": "2024-10-01", "duration": 10, "earlyStart": 3, "earlyFinish": 2, "lateStart": 5, "lateFinish": 8, "lead": 1, "lag": 2}}], "sourceTemplate": "ROBJSOBT", "isTrackingSourceTemplate": false, "isHold": false, "holdOwners": [], "RACI": "ROBJRACI", "RACIHistorical": {"responsible": [{"name": "group name", "description": "group description", "accountNum": "group account number", "tags": ["group tags1", "group tags2"], "members": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}]}], "accountable": {"title": "Scrum Master", "contact": {"_id": "ROBJCONT672bd5074a2abc656568ce5e", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}}, "consulted": [{"name": "group name", "description": "group description", "accountNum": "group account number", "tags": ["group tags1", "group tags2"], "members": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}]}], "informed": [{"name": "group name", "description": "group description", "accountNum": "group account number", "tags": ["group tags1", "group tags2"], "members": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "validFrom": "2024-01-01", "validTo": "2026-01-01", "accountNum": "myAccountNumber", "organizationId": "ROBJENTI", "departmentId": "ROBJGROU", "teamId": "ROBJGROU", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "addressIds": ["ROBJADDR"], "phone": [{"number": "************", "typeId": "ROBJITYP", "description": "use only after 5pm", "validFrom": "", "validTo": ""}], "email": [{"email": "<EMAIL>", "validFrom": "", "validTo": ""}]}]}]}, "agileTaskName": "ROBJITYP", "agileTaskDescription": "this is an agile user story", "agileTaskPoints": 5, "agileTaskSize": "BigMac", "workGroup": ["ROBJGROU"], "checkLists": ["ROBJCHLS"], "checkSheets": ["ROBJCHSH"], "isExternal": false, "apiPreExecutionCallDefinition": "ROBJAPIB", "apiPostExecutionCallDefinition": "ROBJAPIA"}]}