{"dbName": "WorkData", "collectionName": "StructureLinks", "schemaName": "StructureLinkSchema", "availableActions": ["delete-insert", "append", "delete"], "action": "delete-insert", "indexes": [], "data": [{"_id": "WDATSLNK681d59b1d2c5e833b2fc428a", "structureRootReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "sourceName": "Start StructureObject", "sourceId": "WDATSOBJ681d59b1d2c5e833b2fc428a", "sourceHandle": null, "targetName": "Base Structure Object", "targetId": "WDATSOBJ681d59b15da6b3c04321848b", "targetHandle": null}, {"_id": "WDATSLNK681d59b1d2c5e833b2fc428b", "structureRootReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "sourceName": "Base Structure Object", "sourceId": "WDATSOBJ681d59b15da6b3c04321848b", "sourceHandle": null, "targetName": "Work Structure Object", "targetId": "WDATSOBJ681d59b1909e170a067fee7e", "targetHandle": null}, {"_id": "WDATSLNK681d59b1d2c5e833b2fc428c", "structureRootReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "sourceName": "Work Structure Object", "sourceId": "WDATSOBJ681d59b1909e170a067fee7e", "sourceHandle": null, "targetName": "End Structure Object", "targetId": "WDATSOBJ681d59b1eb9e17c1ef941a93", "targetHandle": null}, {"_id": "WDATSLNK681d59b1d2c5e833b2fc428d", "structureRootReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "sourceName": "Work Structure Object", "sourceId": "WDATSOBJ681d59b15da6b3c04321848b", "sourceHandle": null, "targetName": "Reference Node", "targetId": "WDATSOBJ681d59b1eb9e17c1ef941a96", "targetHandle": null}, {"_id": "WDATSLNK682f71a1b3c4d5e6f7g8h9c0", "structureRootReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "sourceName": "Child Container 1", "sourceId": "WDATSOBJ682f71a1b3c4d5e6f7g8h9b0", "sourceHandle": null, "targetName": "Child Work Item 1", "targetId": "WDATSOBJ682f71a2b3c4d5e6f7g8h9b1", "targetHandle": null}, {"_id": "WDATSLNK682f71a2b3c4d5e6f7g8h9c1", "structureRootReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "sourceName": "Child Work Item 1", "sourceId": "WDATSOBJ682f71a2b3c4d5e6f7g8h9b1", "sourceHandle": null, "targetName": "Child Decision Point", "targetId": "WDATSOBJ682f71a3b3c4d5e6f7g8h9b2", "targetHandle": null}, {"_id": "WDATSLNK682f71a2b3c4d5e6f7g8h9c3", "structureRootReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "sourceName": "Reference Node", "sourceId": "WDATSOBJ682f71a2b3c4d5e6f7g8h9b4", "sourceHandle": null, "targetName": "Child Decision Point", "targetId": "WDATSOBJ682f71a2b3c4d5e6f7g8h9b1", "targetHandle": null}, {"_id": "WDATSLNK682f71a3b3c4d5e6f7g8h9c2", "structureRootReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "sourceName": "Child Container 1", "sourceId": "WDATSOBJ682f71a1b3c4d5e6f7g8h9b2", "sourceHandle": null, "targetName": "Nested Work Item", "targetId": "WDATSOBJ682f71a4b3c4d5e6f7g8h9b3", "targetHandle": null}, {"_id": "WDATSLNK682f71a3b3c4d5e6f7g8h9c3", "structureRootReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "sourceName": "Child Container 1", "sourceId": "WDATSOBJ681d59b1eb9e17c1ef941a96", "sourceHandle": null, "targetName": "Nested Work Item", "targetId": "WDATSOBJ682f71a2b3c4d5e6f7g8h9b4", "targetHandle": null}, {"_id": "WDATSLNK682f71a1b3c4d5e6f7g8h9a0", "structureRootReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "sourceName": "Child Container 1", "sourceId": "WDATSOBJ682f71a1b3c4d5e6f7g8h9i0", "sourceHandle": null, "targetName": "Child Work Item 1", "targetId": "WDATSOBJ682f71a2b3c4d5e6f7g8h9i1", "targetHandle": null}, {"_id": "WDATSLNK682f71a2b3c4d5e6f7g8h9a1", "structureRootReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "sourceName": "Child Work Item 1", "sourceId": "WDATSOBJ682f71a2b3c4d5e6f7g8h9i1", "sourceHandle": null, "targetName": "Child Decision Point", "targetId": "WDATSOBJ682f71a3b3c4d5e6f7g8h9i2", "targetHandle": null}]}