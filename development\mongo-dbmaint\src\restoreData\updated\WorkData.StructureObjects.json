{"dbName": "WorkData", "collectionName": "StructureObjects", "schemaName": "StructureObjectSchema", "availableActions": ["delete-insert", "append", "delete"], "action": "delete-insert", "indexes": [], "data": [{"_id": "WDATSOBJ681d59b1d2c5e833b2fc428a", "structureObjectType": "structureObjectStart", "structureRootReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "structureReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "name": "Start StructureObject", "display": {"label": "Start StructureObject", "description": "testing StructureObjects", "type": "label", "hidden": false, "required": true, "readOnly": true, "position": {"x": 0, "y": 0}, "schemeItem": "ROBJSSCH681d59b189405066a24af188"}, "organizationalReferenceId": "myOrgID", "status": "ROBJITYP681d59b176ef1a26a26c87dc", "tags": ["best project", "first", "search tag2"], "isCompleted": false, "completedTimeStamp": "2025-05-09T01:26:09.000", "isSchedulingCurrent": false, "progress": 100, "schedule": [{"_id": "681d59b17b0d9930228169b6", "createdOn": "2023-01-01T06:00:00.000Z", "isActive": true, "createdBy": {"_id": "ROBJCONT672bd5074a2abc656568ce65", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b1fba174a1738b5f39", "department": "ROBJGROU681d59b143f922a4558886df", "team": "ROBJGROU681d59b11e0f2e721e19d0a6", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "address": ["ROBJADDR681d59b1ce2cb66f8c5350b6"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1c854be05ae7573bb", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, "dates": {"startDateEstimated": "2025-06-01T09:58:34.000Z", "startDateActual": "2025-06-01T00:01:00.000Z", "completedDateEstimated": null, "completedDateActual": "2025-06-02T00:00:00.000Z", "dueDate": "2025-06-02T00:00:00.000Z", "durationEstimated": null, "durationActual": null, "earlyStart": 3, "earlyFinish": 2, "lateStart": 5, "lateFinish": 8, "lead": 1, "lag": 2}}], "sourceTemplate": "WDATSOBJ681d59b10e490b18fe8db45e", "isTrackingSourceTemplate": false, "isHold": false, "holdOwners": [], "RACI": "ROBJRACI681d59b1a1b834757414800b", "RACIHistorical": {"responsible": [{"name": "group name", "description": "group description", "accountNum": "group account number", "tags": ["group tags1", "group tags2"], "members": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b1562630b4b33d099a", "department": "ROBJGROU681d59b1442fb4834c38d93e", "team": "ROBJGROU681d59b17a27acc36061c078", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b1e180469c5a623947"], "phone": [{"number": "************", "type": "ROBJITYP681d59b14c265f8dada56bde", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b1142e132f7460d933", "department": "ROBJGROU681d59b1bf7cedae2c8192bb", "team": "ROBJGROU681d59b195e4e62d4a801e23", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b1bc406f438c825a77"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1161ed6e622d64fc9", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b123c30f3749cd8baf", "department": "ROBJGROU681d59b1a80dad8fc4198a33", "team": "ROBJGROU681d59b1057423423831b61a", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "address": ["ROBJADDR681d59b1ae54e450ba1d2f50"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1385691c744922dd1", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}]}], "accountable": {"title": "Scrum Master", "contact": {"_id": "ROBJCONT672bd5074a2abc656568ce5e", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b1cee9c566e85db8da", "department": "ROBJGROU681d59b117ba7cb7d7cc3739", "team": "ROBJGROU681d59b1b5837dda91cc5745", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b1c4c7d0579e1ead1d"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1e15bdd7c416f97a5", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}}, "consulted": [{"name": "group name", "description": "group description", "accountNum": "group account number", "tags": ["group tags1", "group tags2"], "members": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b13ef17fc99f0fea6b", "department": "ROBJGROU681d59b10557181cba35b719", "team": "ROBJGROU681d59b1d83596f5013aa3b5", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b1a4c371be0ff80a68"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1676741627fd7a664", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b1cf0b3633f321bd85", "department": "ROBJGROU681d59b1a62b480bd86a2dfc", "team": "ROBJGROU681d59b1fd209d65537179df", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b1b5c5bcc3702d5df3"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1f532fea5b0c64870", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b11f6607bd7d40a30c", "department": "ROBJGROU681d59b14d051d13a9e8665e", "team": "ROBJGROU681d59b1e0b4f70e6338b77f", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "address": ["ROBJADDR681d59b1fcd393d23cb6f22b"], "phone": [{"number": "************", "type": "ROBJITYP681d59b133fe67440f72e865", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}]}], "informed": [{"name": "group name", "description": "group description", "accountNum": "group account number", "tags": ["group tags1", "group tags2"], "members": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b109007e7f190eea9a", "department": "ROBJGROU681d59b18169856561940503", "team": "ROBJGROU681d59b1e622c5d2ab84fc25", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b11d63b1fa8f7a6a1f"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1b88a3ceb95d78e53", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b14a9f74074283ef6a", "department": "ROBJGROU681d59b1023cf50c2da1c2ba", "team": "ROBJGROU681d59b122f73aa9bf9fef13", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b1d71bbe145706439c"], "phone": [{"number": "************", "type": "ROBJITYP681d59b18a8c2926fede2b0a", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b106ae9e0f260de2b9", "department": "ROBJGROU681d59b1784be4186e68fc8f", "team": "ROBJGROU681d59b110f3484eb8e44d95", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "address": ["ROBJADDR681d59b164f31c4772b051ca"], "phone": [{"number": "************", "type": "ROBJITYP681d59b19c2803e3b1956a05", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}]}]}, "agileTaskName": "ROBJITYP681d59b1816ac7be5ee4a7c0", "agileTaskDescription": "this is an agile user story", "agileTaskPoints": 5, "agileTaskSize": "BigMac", "isExternal": false, "apiPreExecutionCallDefinition": "ROBJAPIB681d59b1ee72a84bf0903c74", "apiPostExecutionCallDefinition": "ROBJAPIA681d59b19e05a8ea508de5d2", "projectSponsor": [{"validFrom": "2023-01-01T06:00:00.000Z", "validTo": "2028-01-01T06:00:00.000Z", "title": "ROBJITYP681d59b13152425a003f08ef", "contact": {"_id": "ROBJCONT672bd5074a2abc656568ce65", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b1ae4dcd1cf0dcc809", "department": "ROBJGROU681d59b1f3bc407c6550c98f", "team": "ROBJGROU681d59b165ecdc907501f55d", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "address": ["ROBJADDR681d59b1aeb04e676f95de77"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1b4392570d3cb4d86", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}}], "projectLead": [{"validFrom": "2023-01-01T06:00:00.000Z", "validTo": "2028-01-01T06:00:00.000Z", "title": "ROBJITYP681d59b138a813a84934ca23", "contact": {"_id": "ROBJCONT672bd5074a2abc656568ce65", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b12b898c316ca49c01", "department": "ROBJGROU681d59b1367af8793d9cf3be", "team": "ROBJGROU681d59b1f283a0cd9e8fe07c", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "address": ["ROBJADDR681d59b164c6551d5cfb725f"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1f1ce57d1bf1bd04c", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}}]}, {"_id": "WDATSOBJ681d59b15da6b3c04321848b", "structureObjectType": "structureObjectBase", "structureRootReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "structureReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "name": "Base Structure Object", "display": {"label": "Base Structure Object", "description": "testing StructureObjects", "type": "label", "hidden": false, "required": true, "readOnly": true, "position": {"x": 0, "y": 0}, "schemeItem": "ROBJSSCH681d59b1bf8ce7f26227cd38"}, "organizationalReferenceId": "myOrgID", "status": "ROBJITYP681d59b1e8b957a65632bf12", "tags": ["best project", "first", "search tag2"], "isCompleted": false, "completedTimeStamp": "2025-05-09T01:26:09.000", "isSchedulingCurrent": false, "progress": 20, "schedule": [{"_id": "681d59b1b2b70ba71e7fb512", "createdOn": "2023-01-01T06:00:00.000Z", "isActive": true, "createdBy": {"_id": "ROBJCONT672bd5074a2abc656568ce65", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b13e6d9b1d7c7df428", "department": "ROBJGROU681d59b191d77d451a82d68f", "team": "ROBJGROU681d59b16b723f39a89bdf79", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "address": ["ROBJADDR681d59b1b20389670a500366"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1dfd4141c3ac54314", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, "dates": {"startDateEstimated": "2025-06-02T09:58:34.000", "startDateActual": "2025-06-02T00:01:00.000", "completedDateEstimated": "2025-06-03T00:00:00.000", "completedDateActual": "2025-06-03T00:00:00.000", "dueDate": "2025-10-01T00:00:00.000Z", "durationEstimated": 1, "durationActual": null, "earlyStart": 3, "earlyFinish": 2, "lateStart": 5, "lateFinish": 8, "lead": 1, "lag": 2}}], "sourceTemplate": "WDATSOBJ681d59b1e47c958d8d04cc17", "isTrackingSourceTemplate": false, "isHold": false, "holdOwners": [], "RACI": "ROBJRACI681d59b143876322fc48f42b", "RACIHistorical": {"responsible": [{"name": "group name", "description": "group description", "accountNum": "group account number", "tags": ["group tags1", "group tags2"], "members": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b10c21299a23e7f592", "department": "ROBJGROU681d59b1aca1eea5b75238b7", "team": "ROBJGROU681d59b17c340d3c9e781230", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b1628b90a9aa36bd87"], "phone": [{"number": "************", "type": "ROBJITYP681d59b171ef5e3209603246", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b16eed998bc26bdeed", "department": "ROBJGROU681d59b14b4257a030fea702", "team": "ROBJGROU681d59b13b499f06a910029a", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b1022b06b9db948afb"], "phone": [{"number": "************", "type": "ROBJITYP681d59b17be9f1612cc65161", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b18cc916bb9cb5ddaa", "department": "ROBJGROU681d59b15cc221eb4197d773", "team": "ROBJGROU681d59b1f51288c71a6a6d89", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "address": ["ROBJADDR681d59b135336662875a66b9"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1b8e1fa84dbc8c4ba", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}]}], "accountable": {"title": "Scrum Master", "contact": {"_id": "ROBJCONT672bd5074a2abc656568ce5e", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b13960e1281ad4d2f9", "department": "ROBJGROU681d59b1b8df70aa34ec8e7b", "team": "ROBJGROU681d59b137d7c47f4d1cab40", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b190348650a029e290"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1a0445aa94b210e0d", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}}, "consulted": [{"name": "group name", "description": "group description", "accountNum": "group account number", "tags": ["group tags1", "group tags2"], "members": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b1051186b63a2c5e67", "department": "ROBJGROU681d59b158c3f308db88113b", "team": "ROBJGROU681d59b1aa5120d630343838", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b175c9b5dd92f95387"], "phone": [{"number": "************", "type": "ROBJITYP681d59b10df6c4d4a507780d", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b19179e583882c5005", "department": "ROBJGROU681d59b19c56dc9cae215c78", "team": "ROBJGROU681d59b1492b55b1f56ee309", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b13fdb10532159c51f"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1d5774ae06000a3b4", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b13d743b13e9dca29a", "department": "ROBJGROU681d59b106cbfe85534388b6", "team": "ROBJGROU681d59b131e1fbe52b42ce60", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "address": ["ROBJADDR681d59b1d6a7c8a5073f6af7"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1b4ace606a8c3432f", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}]}], "informed": [{"name": "group name", "description": "group description", "accountNum": "group account number", "tags": ["group tags1", "group tags2"], "members": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b1d6f1bd13e09ac6a0", "department": "ROBJGROU681d59b1ba50b87d4bf08fad", "team": "ROBJGROU681d59b1cf147623d564e415", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b1a3fff6039b8f72b9"], "phone": [{"number": "************", "type": "ROBJITYP681d59b12e1e413c7c45fe63", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b1f48a35793d7ee185", "department": "ROBJGROU681d59b11fcd181b84d5d69d", "team": "ROBJGROU681d59b16aee14be2a09a5c6", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b1dc7a1e001f772693"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1badf3059b481b879", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b1b8f7f12bda74ca55", "department": "ROBJGROU681d59b169d458b7c6fe9def", "team": "ROBJGROU681d59b164b8c9dcb5b26765", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "address": ["ROBJADDR681d59b129b8690692e5c7ae"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1897757106c376b36", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}]}]}, "agileTaskName": "ROBJITYP681d59b169ad839a000fb374", "agileTaskDescription": "this is an agile user story", "agileTaskPoints": 5, "agileTaskSize": "BigMac", "isExternal": false, "apiPreExecutionCallDefinition": "ROBJAPIB681d59b19d0c6579c6f73eb7", "apiPostExecutionCallDefinition": "ROBJAPIA681d59b170dd671c20632714"}, {"_id": "WDATSOBJ681d59b1909e170a067fee7e", "structureObjectType": "structureObjectWork", "structureRootReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "structureReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "name": "Work Structure Object", "display": {"label": "Work Structure Object", "description": "testing StructureObjects", "type": "label", "hidden": false, "required": true, "readOnly": true, "position": {"x": 0, "y": 0}, "schemeItem": "ROBJSSCH681d59b189405066a24af188"}, "organizationalReferenceId": "myOrgID", "status": "ROBJITYP681d59b1e9334fbe3f5e178a", "tags": ["best project", "first", "search tag2"], "isCompleted": false, "completedTimeStamp": "2025-05-09T01:26:09.000", "isSchedulingCurrent": false, "progress": 50, "schedule": [{"_id": "681d59b10d25a88a64db4474", "createdOn": "2023-01-01T06:00:00.000Z", "isActive": true, "createdBy": {"_id": "ROBJCONT672bd5074a2abc656568ce65", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b1887980740f94b03a", "department": "ROBJGROU681d59b1b9b269fa5579e2ca", "team": "ROBJGROU681d59b17e31c390858f34cf", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "address": ["ROBJADDR681d59b17ef58462382f26f5"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1ca3eb052f9f8dca8", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, "dates": {"startDateEstimated": "2025-06-03T09:58:34.000Z", "startDateActual": "2025-06-03T00:01:00.000Z", "completedDateEstimated": null, "completedDateActual": "2025-06-04T00:00:00.000Z", "dueDate": "2025-06-04T00:00:00.000Z", "durationEstimated": null, "durationActual": null, "earlyStart": 3, "earlyFinish": 2, "lateStart": 5, "lateFinish": 8, "lead": 1, "lag": 2}}], "sourceTemplate": "WDATSOBJ681d59b1f8911e13a5b97f31", "isTrackingSourceTemplate": false, "isHold": false, "holdOwners": [], "RACI": "ROBJRACI681d59b1878e995f3eeb746a", "RACIHistorical": {"responsible": [{"name": "group name", "description": "group description", "accountNum": "group account number", "tags": ["group tags1", "group tags2"], "members": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b15992bbcf8b56bfb0", "department": "ROBJGROU681d59b1f9c2b37aa33a7b14", "team": "ROBJGROU681d59b1d533b89973e1a994", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b1444ebe9924e0eef2"], "phone": [{"number": "************", "type": "ROBJITYP681d59b12c9da8e98ca1bb00", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b1b3285a2c11752b9d", "department": "ROBJGROU681d59b1b41ab01e5a1b520c", "team": "ROBJGROU681d59b1122426a377bc27a3", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b17a8728df7451a90e"], "phone": [{"number": "************", "type": "ROBJITYP681d59b17939a494724d866f", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b14aa312e372e187f8", "department": "ROBJGROU681d59b17e528b0145dadc0c", "team": "ROBJGROU681d59b16bb05bc62161225e", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "address": ["ROBJADDR681d59b1cfbfc6d41f45f67a"], "phone": [{"number": "************", "type": "ROBJITYP681d59b10dff0cb96d2426b8", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}]}], "accountable": {"title": "Scrum Master", "contact": {"_id": "ROBJCONT672bd5074a2abc656568ce5e", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b164416f5e88a3db3b", "department": "ROBJGROU681d59b13345cd8f959140a2", "team": "ROBJGROU681d59b1637cdb0849abcd44", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b1ebdcfb3c1b008d1c"], "phone": [{"number": "************", "type": "ROBJITYP681d59b12897a702b73af9bd", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}}, "consulted": [{"name": "group name", "description": "group description", "accountNum": "group account number", "tags": ["group tags1", "group tags2"], "members": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b1c1299d5f0a822b99", "department": "ROBJGROU681d59b17881be31ab82a6de", "team": "ROBJGROU681d59b1e7401f0b5bfe3497", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b109f696ecfa782b73"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1397f805116f0ba57", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b17f66e21a384ba218", "department": "ROBJGROU681d59b1aa517a783e28dc3f", "team": "ROBJGROU681d59b15a6f711e399f02a3", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b1780c517e39807196"], "phone": [{"number": "************", "type": "ROBJITYP681d59b14b2040ead846e0c2", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b1c0cfc741c35a82c7", "department": "ROBJGROU681d59b1ec43bb22a48b09cb", "team": "ROBJGROU681d59b1948883621419d74e", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "address": ["ROBJADDR681d59b1c2d0bd7779c8f12e"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1595b6f5337194055", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}]}], "informed": [{"name": "group name", "description": "group description", "accountNum": "group account number", "tags": ["group tags1", "group tags2"], "members": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b145a9b13269f2df9d", "department": "ROBJGROU681d59b1240e8d85ea7861de", "team": "ROBJGROU681d59b18d999a46f59740a8", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b1b468dcd60173a064"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1b25f834dc1bc81f9", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b1249dda2090aee0c5", "department": "ROBJGROU681d59b1f445f757eb9f0e07", "team": "ROBJGROU681d59b1d616bdfda99478b8", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b133ef54052d888b58"], "phone": [{"number": "************", "type": "ROBJITYP681d59b164170fb40561e54c", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b165b1507fe3ea2c5b", "department": "ROBJGROU681d59b19e094256d12ba74b", "team": "ROBJGROU681d59b18a0be91c1de6952f", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "address": ["ROBJADDR681d59b19c0c1811737c592d"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1af73cfcb8c2f6e86", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}]}]}, "agileTaskName": "ROBJITYP681d59b12837f8315eae8a37", "agileTaskDescription": "this is an agile user story", "agileTaskPoints": 5, "agileTaskSize": "BigMac", "isExternal": false, "apiPreExecutionCallDefinition": "ROBJAPIB681d59b1beea6bb98019b531", "apiPostExecutionCallDefinition": "ROBJAPIA681d59b1d859d08fe9d76436", "workGroup": ["ROBJGROU681d59b118c3c2a52b85b392"]}, {"_id": "WDATSOBJ681d59b1eb9e17c1ef941a93", "structureObjectType": "structureObjectEnd", "structureRootReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "structureReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "name": "End Structure Object", "display": {"label": "End Structure Object", "description": "testing StructureObjects", "type": "label", "hidden": false, "required": true, "readOnly": true, "position": {"x": 0, "y": 0}, "schemeItem": "ROBJSSCH681d59b189405066a24af188"}, "organizationalReferenceId": "myOrgID", "status": "ROBJITYP681d59b17f642faaabfdf8f5", "tags": ["best project", "first", "search tag2"], "isCompleted": false, "completedTimeStamp": "2025-05-09T01:26:09.000", "isSchedulingCurrent": false, "progress": 0, "schedule": [{"_id": "681d59b1f37a40567fd9b1dc", "createdOn": "2023-01-01T06:00:00.000Z", "isActive": true, "createdBy": {"_id": "ROBJCONT672bd5074a2abc656568ce65", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b1bae374ff8d132603", "department": "ROBJGROU681d59b1a05450adec577b13", "team": "ROBJGROU681d59b184a27e715ff8e03c", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "address": ["ROBJADDR681d59b13964211b00672314"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1d7748a3c80e96d68", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, "dates": {"startDateEstimated": "2025-06-05T09:58:34.000Z", "startDateActual": "2025-06-05T00:01:00.000Z", "completedDateEstimated": null, "completedDateActual": "2025-06-06T00:00:00.000Z", "dueDate": "2025-06-06T00:00:00.000Z", "durationEstimated": null, "durationActual": null, "earlyStart": 3, "earlyFinish": 2, "lateStart": 5, "lateFinish": 8, "lead": 1, "lag": 2}}], "sourceTemplate": "WDATSOBJ681d59b19dec618012780399", "isTrackingSourceTemplate": false, "isHold": false, "holdOwners": [], "RACI": "ROBJRACI681d59b1dfe39ce92b7009fc", "RACIHistorical": {"responsible": [{"name": "group name", "description": "group description", "accountNum": "group account number", "tags": ["group tags1", "group tags2"], "members": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b154f7b412b9767ed0", "department": "ROBJGROU681d59b1735caa4c7b511ac0", "team": "ROBJGROU681d59b1a1154ac0b739ab1b", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b14335be695e1a270c"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1b9a1c1a6a742e832", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b1435ce65029e396fb", "department": "ROBJGROU681d59b10ff6e65a9111dcec", "team": "ROBJGROU681d59b1cb20a125c0990b59", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b1b2d2e373b3b61df7"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1a3eb2d08de04966b", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b1f27392dbea4f832f", "department": "ROBJGROU681d59b1a84881cd61505450", "team": "ROBJGROU681d59b1aa453c8ace02fe97", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "address": ["ROBJADDR681d59b1af73bd99c2cfb003"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1e6e3ff4ee30e2124", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}]}], "accountable": {"title": "Scrum Master", "contact": {"_id": "ROBJCONT672bd5074a2abc656568ce5e", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b108124e47919b2396", "department": "ROBJGROU681d59b1ed94ab7282d0f201", "team": "ROBJGROU681d59b1d70e8fb9220c2b05", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b1e7ed503da6c25184"], "phone": [{"number": "************", "type": "ROBJITYP681d59b172166867ff2c22b1", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}}, "consulted": [{"name": "group name", "description": "group description", "accountNum": "group account number", "tags": ["group tags1", "group tags2"], "members": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b19c3cd63a96dee534", "department": "ROBJGROU681d59b167e77a04c8fb8632", "team": "ROBJGROU681d59b19e06154459d8c927", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b1d5dbade0033df7e9"], "phone": [{"number": "************", "type": "ROBJITYP681d59b16c7934bb6ad23326", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b1d5b08b5ecebe0982", "department": "ROBJGROU681d59b1ec78526b5777a0e9", "team": "ROBJGROU681d59b163daae240d11bb44", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b1cbceabf19c0321e2"], "phone": [{"number": "************", "type": "ROBJITYP681d59b128064e187cd086f4", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b1c534f857186ba505", "department": "ROBJGROU681d59b13085b2bcf72a96dc", "team": "ROBJGROU681d59b140be88ab04e10462", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "address": ["ROBJADDR681d59b10658538d430744b6"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1417dc532893a67d5", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}]}], "informed": [{"name": "group name", "description": "group description", "accountNum": "group account number", "tags": ["group tags1", "group tags2"], "members": [{"_id": "ROBJCONT672bd5074a2abc656568ce5e", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b1d2da2eea239e2c4c", "department": "ROBJGROU681d59b1ddb7121e551990b5", "team": "ROBJGROU681d59b1a935fd7deafa59a5", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b16ca9257201a78e91"], "phone": [{"number": "************", "type": "ROBJITYP681d59b14ce65b8bc1fa2661", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, {"_id": "ROBJCONT672bd5074a2abc656568ce64", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b1ef046af8c2a7d358", "department": "ROBJGROU681d59b1c06b7d340f619030", "team": "ROBJGROU681d59b1a757e8aceebc6150", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Wife", "note": "likes volleyball", "address": ["ROBJADDR681d59b1d9e14acdb97c450a"], "phone": [{"number": "************", "type": "ROBJITYP681d59b1d115aadc89016193", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}, {"_id": "ROBJCONT672bd5074a2abc656568ce65", "isActive": true, "validFrom": "2024-01-01T00:00:00.000Z", "validTo": "2026-01-01T00:00:00.000Z", "accountNum": "myAccountNumber", "organization": "ROBJENTI681d59b17a5434d943ca2b8f", "department": "ROBJGROU681d59b1105e2eca88dc318d", "team": "ROBJGROU681d59b1fdcf590dafd20005", "nameFirst": "<PERSON>", "nameLast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameMiddle": "<PERSON>", "image": "http://defaultUserIcon", "title": "<PERSON><PERSON>'s Son", "note": "likes lambchops", "address": ["ROBJADDR681d59b1690745549e34a658"], "phone": [{"number": "************", "type": "ROBJITYP681d59b16e2ec6af76ac639e", "description": "use only after 5pm", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "email": [{"email": "<EMAIL>", "description": "default", "isActive": true, "validFrom": "2025-05-09T01:26:09.000", "validTo": "2025-05-09T01:26:09.000"}], "rateSchedules": null}]}]}, "agileTaskName": "ROBJITYP681d59b183f9af9f7016f462", "agileTaskDescription": "this is an agile user story", "agileTaskPoints": 5, "agileTaskSize": "BigMac", "isExternal": false, "apiPreExecutionCallDefinition": "ROBJAPIB681d59b1d0c5eb0bee059b2f", "apiPostExecutionCallDefinition": "ROBJAPIA681d59b1f05cae6538fed275"}, {"_id": "WDATSOBJ681d59b1eb9e17c1ef941a96", "structureObjectType": "structureObjectReferenceSource", "structureRootReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "structureReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "name": "Reference Node", "display": {"label": "Reference Node", "description": "A reference node inside the Base Structure Object", "type": "label", "hidden": false, "required": true, "readOnly": false, "position": {"x": 0, "y": 100}, "schemeItem": "ROBJSSCH681d59b189405066a24af188"}, "organizationalReferenceId": "myOrgID"}, {"structureObjectType": "structureObjectBase", "_id": "WDATSOBJ682f71a1b3c4d5e6f7g8h9b0", "structureRootReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "structureReference": {"containedById": "WDATSOBJ682f71a1b3c4d5e6f7g8h9i0", "containedByType": "structureObjectBase"}, "name": "Child Container 1", "display": {"label": "Child Container 1", "description": "A child container of the Base Structure Object", "type": "label", "hidden": false, "required": true, "readOnly": false, "position": {"x": -150, "y": 100}, "schemeItem": "ROBJSSCH681d59b189405066a24af188"}, "organizationalReferenceId": "myOrgID", "status": "ROBJITYP681d59b176ef1a26a26c87dc", "tags": [], "isCompleted": false, "completedTimeStamp": null, "isSchedulingCurrent": false, "schedule": [], "sourceTemplate": null, "isTrackingSourceTemplate": false, "isHold": false, "holdOwners": [], "RACI": null, "RACIHistorical": null, "isExternal": null, "apiPreExecutionCallDefinition": null, "apiPostExecutionCallDefinition": null}, {"_id": "WDATSOBJ682f71a2b3c4d5e6f7g8h9b4", "structureObjectType": "structureObjectReferenceTarget", "structureRootReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "structureReference": {"containedById": "WDATSOBJ682f71a1b3c4d5e6f7g8h9i0", "containedByType": "structureObjectBase"}, "name": "Reference Node", "display": {"label": "Reference Node", "description": "A reference node inside the Base Structure Object", "type": "label", "hidden": false, "required": true, "readOnly": false, "position": {"x": 0, "y": 100}, "schemeItem": "ROBJSSCH681d59b189405066a24af188"}, "organizationalReferenceId": "myOrgID"}, {"_id": "WDATSOBJ682f71a2b3c4d5e6f7g8h9b1", "structureObjectType": "structureObjectWork", "structureRootReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "structureReference": {"containedById": "WDATSOBJ682f71a1b3c4d5e6f7g8h9i0", "containedByType": "structureObjectBase"}, "name": "Child Work Item 1", "display": {"label": "Child Work Item 1", "description": "A work item inside the Base Structure Object", "type": "label", "hidden": false, "required": true, "readOnly": false, "position": {"x": 0, "y": 100}, "schemeItem": "ROBJSSCH681d59b189405066a24af188"}, "organizationalReferenceId": "myOrgID", "status": "ROBJITYP681d59b176ef1a26a26c87dc", "tags": [], "isCompleted": false, "completedTimeStamp": null, "isSchedulingCurrent": false, "schedule": [], "sourceTemplate": null, "isTrackingSourceTemplate": false, "isHold": false, "holdOwners": [], "RACI": null, "RACIHistorical": null, "isExternal": null, "apiPreExecutionCallDefinition": null, "apiPostExecutionCallDefinition": null, "workGroup": ["ROBJGROU681d59b118c3c2a52b85b392"]}, {"_id": "WDATSOBJ682f71a3b3c4d5e6f7g8h9b2", "structureObjectType": "structureObjectDecision", "structureRootReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "structureReference": {"containedById": "WDATSOBJ682f71a1b3c4d5e6f7g8h9i0", "containedByType": "structureObjectBase"}, "name": "Child Decision Point", "display": {"label": "Child Decision Point", "description": "A decision point inside the Base Structure Object", "type": "label", "hidden": false, "required": true, "readOnly": false, "position": {"x": 150, "y": 100}, "schemeItem": "ROBJSSCH681d59b189405066a24af188"}, "organizationalReferenceId": "myOrgID", "status": "ROBJITYP681d59b176ef1a26a26c87dc", "tags": [], "isCompleted": false, "completedTimeStamp": null, "isSchedulingCurrent": false, "schedule": [], "sourceTemplate": null, "isTrackingSourceTemplate": false, "isHold": false, "holdOwners": [], "RACI": null, "RACIHistorical": null}, {"structureObjectType": "structureObjectBase", "_id": "WDATSOBJ682f71a1b3c4d5e6f7g8h9i0", "structureRootReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "structureReference": {"containedById": "WDATSOBJ681d59b15da6b3c04321848b", "containedByType": "structureObjectBase"}, "name": "Child Container 1", "display": {"label": "Child Container 1", "description": "A child container of the Base Structure Object", "type": "label", "hidden": false, "required": true, "readOnly": false, "position": {"x": -150, "y": 100}, "schemeItem": "ROBJSSCH681d59b189405066a24af188"}, "organizationalReferenceId": "myOrgID", "status": "ROBJITYP681d59b176ef1a26a26c87dc", "tags": [], "isCompleted": false, "completedTimeStamp": null, "isSchedulingCurrent": false, "schedule": [], "sourceTemplate": null, "isTrackingSourceTemplate": false, "isHold": false, "holdOwners": [], "RACI": null, "RACIHistorical": null, "isExternal": null, "apiPreExecutionCallDefinition": null, "apiPostExecutionCallDefinition": null}, {"_id": "WDATSOBJ682f71a2b3c4d5e6f7g8h9i1", "structureObjectType": "structureObjectWork", "structureRootReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "structureReference": {"containedById": "WDATSOBJ681d59b15da6b3c04321848b", "containedByType": "structureObjectBase"}, "name": "Child Work Item 1", "display": {"label": "Child Work Item 1", "description": "A work item inside the Base Structure Object", "type": "label", "hidden": false, "required": true, "readOnly": false, "position": {"x": 0, "y": 100}, "schemeItem": "ROBJSSCH681d59b189405066a24af188"}, "organizationalReferenceId": "myOrgID", "status": "ROBJITYP681d59b176ef1a26a26c87dc", "tags": [], "isCompleted": false, "completedTimeStamp": null, "isSchedulingCurrent": false, "schedule": [], "sourceTemplate": null, "isTrackingSourceTemplate": false, "isHold": false, "holdOwners": [], "RACI": null, "RACIHistorical": null, "isExternal": null, "apiPreExecutionCallDefinition": null, "apiPostExecutionCallDefinition": null, "workGroup": ["ROBJGROU681d59b118c3c2a52b85b392"]}, {"_id": "WDATSOBJ682f71a3b3c4d5e6f7g8h9i2", "structureObjectType": "structureObjectDecision", "structureRootReference": {"containedById": "WDATSOBJ681d59b1d2c5e833b2fc428a", "containedByType": "structureObjectStart"}, "structureReference": {"containedById": "WDATSOBJ681d59b15da6b3c04321848b", "containedByType": "structureObjectBase"}, "name": "Child Decision Point", "display": {"label": "Child Decision Point", "description": "A decision point inside the Base Structure Object", "type": "label", "hidden": false, "required": true, "readOnly": false, "position": {"x": 150, "y": 100}, "schemeItem": "ROBJSSCH681d59b189405066a24af188"}, "organizationalReferenceId": "myOrgID", "status": "ROBJITYP681d59b176ef1a26a26c87dc", "tags": [], "isCompleted": false, "completedTimeStamp": null, "isSchedulingCurrent": false, "schedule": [], "sourceTemplate": null, "isTrackingSourceTemplate": false, "isHold": false, "holdOwners": [], "RACI": null, "RACIHistorical": null}]}