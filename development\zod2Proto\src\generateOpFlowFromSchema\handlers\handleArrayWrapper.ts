import { Node } from 'ts-morph';
import { Config, ProtoRegistry, ProtoRegistryEntry } from '../../types';
import { OpFlow } from '../../types';
import { handleFields } from './handleFields';
import { NodeProcessingContext } from '../protoTypes';

export function isArrayWrapper(node: Node): boolean {
  const text = node.getText();
  return /^z\.array\(/.test(text);
}

export function generateArrayWrapper(nodeProcessingContext: NodeProcessingContext) {
  const { node, registryEntry, protoRegistry, yamlConfig, processingLog } = nodeProcessingContext;

  // Example: extract the array element node and recurse
  processingLog.push(`Array wrapper detected for ${schemaName}: ${node.getText()}`);
  // TODO: extract element node and call processNode
}
