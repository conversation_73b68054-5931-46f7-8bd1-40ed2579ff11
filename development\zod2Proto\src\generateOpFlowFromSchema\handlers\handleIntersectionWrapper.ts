import { Node } from 'ts-morph';
import { ProtoRegistry } from '../../types';
import { OpFlow } from '../../types';
import { handleFields } from './handleFields';
import { NodeProcessingContext } from '../protoTypes';

export function isIntersectionWrapper(node: Node): boolean {
  const text = node.getText();
  return /^z\.intersection\(/.test(text);
}

export function generateIntersectionWrapper(nodeProcessingContext: NodeProcessingContext) {
  const { node, registryEntry, protoRegistry, yamlConfig, processingLog } = nodeProcessingContext;

  processingLog.push(`Intersection wrapper detected for ${schemaName}: ${node.getText()}`);
  // TODO: extract intersection members and call processNode for each
}
