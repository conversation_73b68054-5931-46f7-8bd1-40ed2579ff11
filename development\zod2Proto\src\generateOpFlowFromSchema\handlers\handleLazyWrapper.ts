import { Node } from 'ts-morph';
import { ProtoRegistry } from '../../types';
import { OpFlow } from '../../types';
import { NodeProcessingContext } from '../protoTypes';

export function isLazyWrapper(node: Node): boolean {
  const text = node.getText();
  return /^z\.lazy\(/.test(text);
}

export function generateLazyWrapper(nodeProcessingContext: NodeProcessingContext) {
  const { node, registryEntry, protoRegistry, yamlConfig, processingLog } = nodeProcessingContext;

  processingLog.push(`Lazy wrapper detected for ${schemaName}: ${node.getText()}`);
  // TODO: extract returned type and call processNode
}
