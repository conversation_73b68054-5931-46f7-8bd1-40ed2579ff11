import { Node } from 'ts-morph';
import { Config, Field, ProtoRegistry } from '../../types';
import { OpFlow } from '../../types';
import { processLeaf, processNode } from '../processNode';
import { NodeProcessingContext } from '../protoTypes';

export function isObjectNode(node: Node): boolean {
  const text = node.getText();
  // Only match z.object calls
  return /z\.object\(/.test(text);
}

export function generateObjectNode(nodeProcessingContext: NodeProcessingContext): void {
  const { node, registryEntry, protoRegistry, yamlConfig, processingLog } = nodeProcessingContext;

  // Only handle z.object nodes. If not, return empty object node below.

  // Build and return an ObjectNode from z.object({...}) only
  const callInfo = getCallExpressionInfo(node);
  let fields: Field[] = [];
  if (callInfo) {
    const { expr, args } = callInfo;
    if (expr === 'z.object' && args.length > 0 && Node.isObjectLiteralExpression(args[0])) {
      // z.object({ ... })
      const objLit = args[0];
      fields = [];
      objLit.getProperties().forEach(prop => {
        if (Node.isPropertyAssignment(prop)) {
          const fieldName = prop.getName();
          const initializer = prop.getInitializer();
          if (initializer) {
            if (processLeaf(initializer, opFlow, fieldName, schemaName, processingLog, yamlConfig)) {
              // processLeaf handles primitive leaves and pushes directly to opFlow
            } else {
              // processNode handles recursion and opFlow
              processNode(initializer, protoRegistry, opFlow, schemaName, processingLog);
            }
          }
        }
      });

      opFlow.push({
        type: 'object',
        fields: Array.isArray(fields) ? fields : [],
        isReference: false,
      });
      return;
    }
  }
  // If not z.object, just return empty object node
  opFlow.push({
    type: 'object',
    fields: [],
    isReference: false,
  });
}

// Helper to extract call info from a node
function getCallExpressionInfo(node: Node): { expr: string; args: Node[] } | null {
  if (Node.isCallExpression(node)) {
    const expr = node.getExpression().getText();
    const args = node.getArguments();
    return { expr, args };
  }
  return null;
}
