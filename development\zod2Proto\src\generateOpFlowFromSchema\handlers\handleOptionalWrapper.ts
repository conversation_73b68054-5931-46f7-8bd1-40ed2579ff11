import { Node } from 'ts-morph';
import { ProtoRegistry } from '../../types';
import { OpFlow } from '../../types';
import { NodeProcessingContext } from '../protoTypes';

export function isOptionalWrapper(node: Node): boolean {
  const text = node.getText();
  return /^z\.optional\(/.test(text);
}

export function generateOptionalWrapper(nodeProcessingContext: NodeProcessingContext) {
  const { node, registryEntry, protoRegistry, yamlConfig, processingLog } = nodeProcessingContext;

  processingLog.push(`Optional wrapper detected for ${schemaName}: ${node.getText()}`);
  // TODO: extract underlying type and call processNode
}
