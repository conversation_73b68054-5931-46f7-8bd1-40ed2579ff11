import { Node } from 'ts-morph';
import { Config, OpFlow } from '../../types';
import { stripZodModifiers } from '../../utils/protoAstUtils';

export function isPrimitiveLeaf(node: Node): boolean {
  const text = node.getText();
  return /^z\.(string|number|boolean|date)\(\)/.test(text);
}

export function generatePrimitiveLeaf(
  node: Node,
  opFlow: OpFlow,
  fieldName: string,
  schemaName: string,
  processingLog: string[],
  yamlConfig: Config,
): { fieldName: string; type: string; isReference: false } {
  // Extract the Zod type expression from the node
  const zodType = node.getText();

  // Convert Zod type to proto type
  let protoType = convertZodTypeToProto(zodType, schemaName, yamlConfig);
  opFlow.push({ type: 'field', name: fieldName, fieldType: protoType, isReference: false });
  pushIdFields(fieldName, protoType, opFlow, schemaName, processingLog);

  return { fieldName: fieldName, type: protoType, isReference: false };
}

/**
 * Converts Zod field type expressions to proto types
 */
// implement config settings and typeS
function convertZodTypeToProto(zodType: string, schemaName: string, yamlConfig: Config): string {
  // Remove whitespace and normalize
  let normalized = stripZodModifiers(zodType.trim());
  // Apply config replacements if present
  if (yamlConfig?.protoFieldTypeReplacements && yamlConfig.protoFieldTypeReplacements[normalized]) {
    normalized = yamlConfig.protoFieldTypeReplacements[normalized]!;
  }

  // Basic type mappings
  if (normalized === 'z.string()') return 'string';
  if (normalized === 'z.number()') return 'double';
  if (normalized === 'z.boolean()') return 'bool';
  if (normalized === 'z.date()') return 'google.protobuf.Timestamp';
  if (normalized.startsWith('z.literal(')) return 'string'; // Literals become strings
  if (normalized.startsWith('z.optional(')) {
    // Extract the inner type from z.optional(z.string()) -> z.string()
    const match = normalized.match(/z\.optional\((.*)\)$/);
    if (match && match[1]) {
      return 'optional ' + convertZodTypeToProto(match[1], schemaName, yamlConfig);
    }
  }
  // Default fallback, should be matched and replaced in postProcessFields
  return normalized;
}

function pushIdFields(
  fieldName: string,
  fieldType: string,
  opFlow: OpFlow,
  schemaName: string,
  processingLog: string[],
) {
  // Insert a new field after any field with name matching /Id$/ and type in fieldTypesToMatch
  const fieldTypesToMatch = ['preprocessObjectId32', 'preprocessObjectId32TYPED'];

  if (fieldName.endsWith('Id') && fieldTypesToMatch.includes(fieldType)) {
    const newFieldName = fieldName.slice(0, -2); // Remove 'Id' suffix
    if (newFieldName) {
      opFlow.push({
        name: newFieldName,
        type: 'google.protobuf.Struct',
        isReference: false,
      });
      processingLog.push(
        `Inserted field '${newFieldName}' of type 'google.protobuf.Struct' after '${fieldName}' in schema '${schemaName}'`,
      );
    }
  }
}
