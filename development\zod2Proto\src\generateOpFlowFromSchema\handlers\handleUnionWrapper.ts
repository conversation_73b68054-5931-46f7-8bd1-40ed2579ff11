import { Node } from 'ts-morph';
import { ProtoRegistry } from '../../types';
import { OpFlow } from '../../types';
import { handleFields } from './handleFields';
import { NodeProcessingContext } from '../protoTypes';

export function isUnionWrapper(node: Node): boolean {
  const text = node.getText();
  return /^z\.union\(/.test(text);
}

export function generateUnionWrapper(nodeProcessingContext: NodeProcessingContext) {
  const { node, registryEntry, protoRegistry, yamlConfig, processingLog } = nodeProcessingContext;

  processingLog.push(`Union wrapper detected for ${schemaName}: ${node.getText()}`);
  // TODO: extract union members and call processNode for each
}
