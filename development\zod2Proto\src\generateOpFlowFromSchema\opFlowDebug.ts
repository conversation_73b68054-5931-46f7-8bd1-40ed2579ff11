import { ProtoRegistryEntry } from '../types';
import { OpFlow } from '../types';
import * as fs from 'fs';
import * as path from 'path';
import { debugLog } from '../utils/debugUtils';

/**
 * Writes OpFlow debug information to a JSON file for inspection
 */
function writeOpFlowDebugFile(
  registryEntry: ProtoRegistryEntry,
  opFlow: OpFlow,
  protoRoot: string,
  processingLog: string[],
) {
  const debugDir = path.join(protoRoot, '__debug__');
  if (!fs.existsSync(debugDir)) {
    fs.mkdirSync(debugDir, { recursive: true });
  }

  const debugInfo = {
    schemaName: registryEntry.schemaName,
    definedIn: registryEntry.definedIn,
    opFlowLength: opFlow.length,
    operations: opFlow.map((op, index) => ({
      index,
      type: op.type,
      ...(op.type === 'union' ||
      op.type === 'intersection' ||
      op.type === 'discriminatedUnion' ||
      op.type === 'array'
        ? {
            objectNodesCount: (op as any).objectNodes?.length || 0,
            objectNodes:
              (op as any).objectNodes?.map((node: any, nodeIndex: number) => ({
                nodeIndex,
                type: node.type,
                fieldsCount: node.fields?.length || 0,
                fields: node.fields?.map((f: any) => ({ name: f.name, type: f.type })) || [],
                schemaName: node.schemaName,
                isReference: node.isReference,
                baseSchemaName: node.baseSchemaName,
                modifiers: node.modifiers,
              })) || [],
          }
        : {
            fieldsCount: (op as any).fields?.length || 0,
            fields: (op as any).fields?.map((f: any) => ({ name: f.name, type: f.type })) || [],
            schemaName: (op as any).schemaName,
            isReference: (op as any).isReference,
            baseSchemaName: (op as any).baseSchemaName,
            modifiers: (op as any).modifiers,
          }),
    })),
    // Processing log section at the bottom
    processingLog: {
      totalEntries: processingLog.length,
      entries: processingLog.map((entry, index) => ({
        index,
        timestamp: new Date().toISOString(),
        message: entry,
      })),
    },
  };

  const debugFilePath = path.join(debugDir, `${registryEntry.schemaName}.opflow.json`);
  fs.writeFileSync(debugFilePath, JSON.stringify(debugInfo, null, 2));
  debugLog.flow(`Debug OpFlow written: ${debugFilePath}`);
}
