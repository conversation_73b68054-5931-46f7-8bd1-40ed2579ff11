import { Node, Project, SourceFile } from 'ts-morph';
import { Config, ProtoRegistry, ProtoRegistryEntry } from '../types';
import { OpFlow } from '../types';
import { isPrimitiveLeaf, generatePrimitiveLeaf } from './handlers/handlePrimitiveLeaf';
import { isIdentifierLeaf, generateIdentifierLeaf } from './handlers/handleIdentifierLeaf';
import { isArrayWrapper, generateArrayWrapper } from './handlers/handleArrayWrapper';
import { isUnionWrapper, generateUnionWrapper } from './handlers/handleUnionWrapper';
import { isIntersectionWrapper, generateIntersectionWrapper } from './handlers/handleIntersectionWrapper';
import { isOptionalWrapper, generateOptionalWrapper } from './handlers/handleOptionalWrapper';
import { isLazyWrapper, generateLazyWrapper } from './handlers/handleLazyWrapper';
import { isObjectNode, generateObjectNode } from './handlers/handleObjectNode';
import { isEnumLeaf, generateEnumLeaf } from './handlers/handleEnumLeaf';
import { MessageType, NodeProcessingContext } from './protoTypes';

export function processFile(
  registryEntry: ProtoRegistryEntry,
  protoRegistry: ProtoRegistry,
  protoRoot: string,
  project: Project,
  yamlConfig: Config,
) {
  // get file, get all exported variables
  // for each variable, get the initializer
  // if the initializer is a zod schema, process it
  // if the initializer is a function, call the function and process the return value
  // if the initializer is a variable, get the variable's initializer and process it
  const filePath = registryEntry.definedIn;
  const schemaVarName = registryEntry.schemaName;
  const processingLog: string[] = [];

  processingLog.push(`Starting OpFlow creation for schema: ${schemaVarName}`);
  processingLog.push(`Source file: ${filePath}`);

  const sourceFile: SourceFile | undefined = project.addSourceFileAtPathIfExists(filePath);
  if (!sourceFile) {
    const error = `Source file not found: ${filePath}`;
    processingLog.push(`ERROR: ${error}`);
    throw new Error(error);
  }

  const schemaVar = sourceFile.getVariableDeclaration(schemaVarName);
  if (!schemaVar) {
    const error = `Schema variable declaration not found: ${schemaVarName}`;
    processingLog.push(`ERROR: ${error}`);
    throw new Error(error);
  }

  const schemaNode = schemaVar.getInitializer();
  if (!schemaNode) {
    const error = `Schema initializer not found for: ${schemaVarName}`;
    processingLog.push(`ERROR: ${error}`);
    throw new Error(error);
  }

  processingLog.push(`Schema AST node type: ${schemaNode.getKindName()}`);
  processingLog.push(`Schema AST text: ${schemaNode.getText().substring(0, 100)}...`);
  let messageType: MessageType = 'message';
  if (isEnumLeaf(schemaNode)) {
    messageType = 'enum';
  }
  startMessage({
    node: schemaNode,
    registryEntry,
    protoRegistry,
    yamlConfig,
    processingLog,
  });
}

// called from each process file schema,
// AND from an extend, intersection, union, array of more than one field, extend will create a new message manifest and start over
// if this was an extend, after generating the new protofile, the new message will be
// used by the extend caller to be used in it's manifest after calling generateProtoFromMessage
export function startMessage(nodeProcessingContext: NodeProcessingContext) {
  processNode(nodeProcessingContext);
  generateProtoFromMessage(nodeProcessingContext);

  // return the message manifest name to be used if called from union, ...
  //   if (registryEntry.protoFileManifest?.type === 'message') {
  //     return registryEntry.protoFileManifest.messages[0].name;
  //   }
}

export function processNode(nodeProcessingContext: NodeProcessingContext) {
  const { node, registryEntry, protoRegistry, yamlConfig, processingLog } = nodeProcessingContext;
  processingLog.push(`Processing node type: ${node.getKindName()} for ${registryEntry.schemaName}`);
  if (registryEntry.schemaName === 'prodAddSchema') {
    // [DEBUG]
    console.log();
  }
  // Detection chain
  if (isArrayWrapper(node)) {
    generateArrayWrapper(nodeProcessingContext);
    return;
  }
  if (isUnionWrapper(node)) {
    generateUnionWrapper(nodeProcessingContext);
    return;
  }
  if (isIntersectionWrapper(node)) {
    generateIntersectionWrapper(nodeProcessingContext);
    return;
  }
  if (isOptionalWrapper(node)) {
    generateOptionalWrapper(nodeProcessingContext);
    return;
  }
  if (isLazyWrapper(node)) {
    generateLazyWrapper(nodeProcessingContext);
    return;
  }
  if (isObjectNode(node)) {
    generateObjectNode(nodeProcessingContext);
    return;
  }

  processingLog.push(`WARNING: Unhandled AST node for ${registryEntry.schemaName}: ${node.getKindName()}`);
}

export function processLeaf(
  node: Node,
  opFlow: OpFlow,
  fieldName: string,
  schemaName: string,
  processingLog: string[],
  yamlConfig: Config,
): boolean {
  let returnValue = false;
  if (isPrimitiveLeaf(node)) {
    returnValue = true;
    generatePrimitiveLeaf(node, opFlow, fieldName, schemaName, processingLog, yamlConfig);
  }
  if (isIdentifierLeaf(node)) {
    returnValue = true;
    generateIdentifierLeaf(node, opFlow, schemaName, processingLog, yamlConfig);
  }
  if (isEnumLeaf(node)) {
    returnValue = true;
    generateEnumLeaf(node, opFlow, schemaName, processingLog, yamlConfig);
  }
  return returnValue;
}

function generateProtoFromMessage(NodeProcessingContext: NodeProcessingContext) {
  // 1. create output file path and proto packageName from registryEntry
  // 2. add header using message builder
  // 3. add imports using message builder
  // 4. add enums using message builder
  // 5. add oneofs using message builder
  // 6. add message using message builder
  // 7. Write types file
  // 8. create and write services file if needed
}
