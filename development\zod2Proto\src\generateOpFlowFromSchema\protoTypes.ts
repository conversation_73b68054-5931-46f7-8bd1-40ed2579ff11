// Manifest types for proto generation from Zod schemas

import { Config, ProtoRegistry, ProtoRegistryEntry } from '../types';
import { Node } from 'ts-morph';

// These types define the structure of the output manifest
export type NodeProcessingContext = {
  node: Node;
  registryEntry: ProtoRegistryEntry;
  protoRegistry: ProtoRegistry;
  yamlConfig: Config;
  processingLog: string[];
};

export type ProtoFileManifest = EnumManifest | MessageManifest;

export type MessageType = 'message' | 'enum';

export type Oneof = {
  name: string;
  options: Field[];
};

export type Import = {
  messageName: string;
  packageName: string;
  protoFilePath: string;
  schemaName: string;
  sourceSchemaFile: string;
};

// this is a file of enum type, that would be shared between messages
export type EnumManifest = {
  type: 'enum';
  enumName: string;
  packageName: string;
  protoFilePath: string;
  schemaName: string;
  sourceSchemaFile: string;
  values: string[];
};

// local oneofs, enums, messages will be created in this file
export type MessageManifest = {
  type: 'message';
  packageName: string;
  protoFilePath: string;
  schemaName: string;
  sourceSchemaFile: string;
  imports: Import[];
  oneofs: Oneof[];
  enums: ProtoEnum[];
  messages: ProtoMessage[];
};

export type ProtoMessage = {
  name: string;
  fields: Field[];
};

export type ProtoEnum = {
  name: string;
  values: string[];
};

export type Field = {
  type: 'field';
  name: string;
  fieldType: string; // e.g. "string", "double", "bool", "google.protobuf.Timestamp", etc.
  isReference: boolean;
  repeated?: boolean; // true if array
  optional?: boolean; // true if optional
};
