import { Project } from 'ts-morph';
import { ProtoRegistry, ProtoRegistryEntry } from './types';
import { generateOpFlowFromSchema } from './schemaProcessingOps/generateOpFlowFromSchema';
import { generateProtoFromOpFlow } from './schemaProcessingOps/generateProtoFromOpFlow';
import { generateCrudServices, generateServiceProtoFile } from './utils/crudGenerator';
import { createProtoDirectory } from './schemaProcessingOps/utils/namingUtils';
import { debugLog } from './utils/debugUtils';
import * as fs from 'fs';
import * as path from 'path';
import { extractSchemaDependencies } from './utils/extractSchemaDependencies';

// Multi-pass processing function
export function processSchemasLooper(
  protoRegistry: ProtoRegistry,
  project: Project,
  inputDir: string,
  protoRoot: string,
  yamlConfig: any,
  schemaKeyToVarMap: Record<string, string>,
  expand: boolean,
): void {
  const processedSchemas = new Set<string>();
  let passNumber = 1;
  let schemasProcessedThisPass = 0;

  debugLog.milestone('Starting multi-pass schema processing...');
  debugLog.info(`Found ${protoRegistry.size} schemas in registry`);
  validateProtoRegistry(protoRegistry);

  do {
    schemasProcessedThisPass = 0;
    debugLog.milestone(`--- Pass ${passNumber} ---`);

    // Try to process each schema in the registry
    for (const [registryKey, registryEntry] of protoRegistry.entries()) {
      if (processedSchemas.has(registryKey)) {
        continue; // Already processed
      }

      try {
        const sourceFile = project.addSourceFileAtPathIfExists(registryEntry.definedIn);
        if (sourceFile) {
          const schemaVar = sourceFile.getVariableDeclaration(registryEntry.schemaName);
          if (schemaVar) {
            const schemaNode = schemaVar.getInitializer();
            if (schemaNode) {
              // Extract dependencies as registry keys (not schema names)
              const dependencyKeys = extractSchemaDependencies(
                schemaNode,
                registryEntry.definedIn,
                protoRegistry,
              );
              debugLog.detail(` Dependencies for ${registryEntry.schemaName}: ${dependencyKeys.join(', ')}`);

              // Check for processed dependencies using registry keys directly
              const unprocessedKeys = dependencyKeys.filter(depKey => !processedSchemas.has(depKey));

              if (unprocessedKeys.length > 0) {
                debugLog.warn(
                  ` Skipping ${registryEntry.schemaName} due to unprocessed dependencies: ${unprocessedKeys.join(', ')}`,
                );
                continue;
              }
            }
          }
        }

        // create the operations flow required to generate the proto file
        const operations = generateOpFlowFromSchema(
          registryEntry,
          project,
          protoRegistry,
          processedSchemas,
          yamlConfig,
          expand,
          protoRoot,
          inputDir,
          schemaKeyToVarMap,
        );

        if (operations.length === 0) {
          debugLog.error(
            `No operations created for ${registryEntry.schemaName}, ${registryEntry.schemaRelativePath}`,
          );
          throw new Error(
            `No operations created for ${registryEntry.schemaName}, ${registryEntry.schemaRelativePath}`,
          );
        }

        // generate proto file from operations flow
        generateProtoFromOpFlow(registryEntry, protoRoot, yamlConfig, protoRegistry, operations);

        // Generate CRUD services if enabled and applicable
        // DEBUG_CRUD: first call
        generateCrudProtoFiles(registryEntry, protoRoot, yamlConfig);

        // Mark as processed
        registryEntry.isGenerated = true;
        processedSchemas.add(registryKey); // Add this line to track processed schemas
        schemasProcessedThisPass++;
        debugLog.success(`Processed: ${registryEntry.schemaName}`);
      } catch (error) {
        debugLog.error(`Error processing ${registryEntry.schemaName}:`, error);
      }
    }

    debugLog.progress(`Pass ${passNumber}: Processed ${schemasProcessedThisPass} schemas`);
    passNumber++;

    // Safety valve - prevent infinite loops
    if (passNumber > 10) {
      debugLog.warn(`Stopping after 10 passes. Some schemas may have unresolved dependencies.`);
      break;
    }
  } while (schemasProcessedThisPass > 0);

  debugLog.milestone(`Multi-pass processing complete after ${passNumber - 1} passes`);
  debugLog.success(`Total processed: ${processedSchemas.size} schemas`);

  // Report any unprocessed schemas
  const unprocessedSchemas = Object.keys(protoRegistry).filter(key => !processedSchemas.has(key));
  if (unprocessedSchemas.length > 0) {
    debugLog.warn(`${unprocessedSchemas.length} schemas were not processed:`);
    unprocessedSchemas.forEach(key => debugLog.warn(`   - ${key}`));
  }
}

function validateProtoRegistry(protoRegistry: ProtoRegistry) {
  let valid: boolean = true;

  // if (protoRegistry.size === 0) {
  //   debugLog.error(`Proto registry is empty`);
  //   valid = false;
  // }

  for (const [registryKey, registryEntry] of Object.entries(protoRegistry)) {
    if (!registryEntry.protoPath) {
      debugLog.error(`Proto path not set for ${registryKey}`);
      valid = false;
    }
    if (!registryEntry.protoMessageName) {
      debugLog.error(`Proto message name not set for ${registryKey}`);
      valid = false;
    }
    // check for duplicate schema names
    const duplicateSchemaNames = Object.entries(protoRegistry).filter(
      ([key, entry]) => entry.schemaName === registryEntry.schemaName && key !== registryKey,
    );
    if (duplicateSchemaNames.length > 0) {
      debugLog.warn(
        `Duplicate schema name found for ${registryEntry.schemaName} in \n ${duplicateSchemaNames
          .map(([key]) => key)
          .join(', ')}`,
      );
      valid = false;
    }
  }
  if (valid === false) {
    // debugLog.error(`Proto registry validation failed, exiting application`);
    // process.exit(1);
  }
}

// DEBUG_CRUD: second call
/**
 * Generates CRUD services for a schema if CRUD is enabled and the schema is eligible
 */
function generateCrudProtoFiles(registryEntry: ProtoRegistryEntry, protoRoot: string, yamlConfig: any) {
  const crudConfig = yamlConfig.crud;

  // Skip if CRUD is not enabled
  if (!crudConfig?.enabled) {
    return;
  }

  // Check if this schema's category is eligible for CRUD generation
  if (!crudConfig.generate_for.includes(registryEntry.category)) {
    debugLog.detail(
      `Skipping CRUD generation for ${registryEntry.schemaName} (category: ${registryEntry.category})`,
    );
    return;
  }

  debugLog.detail(
    `Generating CRUD services for ${registryEntry.schemaName} (category: ${registryEntry.category})`,
  );

  try {
    // Generate CRUD services using the existing utility
    const services = generateCrudServices(registryEntry, protoRoot, yamlConfig);

    // Generate command service if available
    if (services.commands) {
      writeServiceFile(services.commands, 'commands', registryEntry, protoRoot, yamlConfig);
    }

    // Generate query service if available
    if (services.queries) {
      writeServiceFile(services.queries, 'queries', registryEntry, protoRoot, yamlConfig);
    }

    if (services.commands || services.queries) {
      debugLog.info(`Generated CRUD services for ${registryEntry.schemaName}`);
    }
  } catch (error) {
    debugLog.error(`Error generating CRUD services for ${registryEntry.schemaName}:`, error);
  }
}

/**
 * Generates and writes a CRUD service proto file
 */
function writeServiceFile(
  service: any,
  serviceType: 'commands' | 'queries',
  registryEntry: ProtoRegistryEntry,
  protoRoot: string,
  yamlConfig: any,
) {
  // Generate service file path using config patterns
  const crudConfig = yamlConfig.crud;
  const filePattern =
    serviceType === 'commands'
      ? crudConfig.file_organization.commands_file
      : crudConfig.file_organization.queries_file;

  // Extract sourceSubdir from the registry entry's relative path
  const sourceSubdir = path.dirname(registryEntry.schemaRelativePath);

  // Extract collection name from entity name (lowercase)
  const collectionName = registryEntry.protoMessageName.toLowerCase();
  const protoFileRelativePath = path.dirname(registryEntry.protoFile.replace(`\\types`, ``));

  // 'services/{sourceSubdir}/{collectionName|lowercase}_commands.proto'
  let serviceFilename = filePattern
    .replace('{sourceSubdir}', protoFileRelativePath)
    .replace('{collectionName|lowercase}', collectionName)
    .replace(/\\/g, '/');

  // // Process the file pattern
  // let serviceFilename = filePattern
  //   .replace('{sourceSubdir}', sourceSubdir)
  //   .replace('{collectionName|lowercase}', collectionName)
  //   .replace(/\\/g, '/');

  // Create the full output path
  const serviceOutPath = createProtoDirectory(serviceFilename, protoRoot);

  // Generate the proto content
  const sourceRelativePath = path
    .normalize(path.relative(process.cwd(), registryEntry.definedIn))
    .replace(/\\/g, '/');

  const protoContent = generateServiceProtoFile(
    service,
    yamlConfig,
    sourceRelativePath,
    registryEntry.schemaName,
    yamlConfig.paths?.inputDir,
  );

  // Write the service file
  fs.writeFileSync(serviceOutPath, protoContent);
  debugLog.file(`Wrote ${serviceType} service: ${serviceOutPath}`);
}
