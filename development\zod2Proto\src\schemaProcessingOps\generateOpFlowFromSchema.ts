import { ProtoRegistry, ProtoRegistryEntry } from '../types';
import { OpFlow, Operation, Wrapper, ObjectNode, Modifier, Field, EnumNode } from '../types';
import { Node, Project, SourceFile, SyntaxKind } from 'ts-morph';
import * as fs from 'fs';
import * as path from 'path';
import { debugLog } from '../utils/debugUtils';
import {
  getBaseIdentifierFromCallChain,
  stripZodModifiers,
  stripZodWrappersFromNode,
} from '../utils/protoAstUtils';

/**
 * Main entry: Convert a schema registry entry to an OpFlow (linear operation flow)
 *
 * @param registryEntry - The registry entry for the schema
 * @param project - ts-morph Project instance
 * @param protoRegistry - Registry for proto info (from app)
 * @param processedSchemas - Set of already processed schemas
 * @param config - App config
 * @param expand - Whether to expand all fields
 * @returns OpFlow
 */
export function generateOpFlowFromSchema(
  registryEntry: ProtoRegistryEntry,
  project: Project,
  protoRegistry: ProtoRegistry,
  // will be needed later
  processedSchemas: Set<string>,
  config: unknown,
  expand: boolean,
  // will be needed later
  protoRoot: string,
  inputDir: string,
  schemaKeyToVarMap: Record<string, string>,
): OpFlow {
  const filePath = registryEntry.definedIn;
  const schemaVarName = registryEntry.schemaName;
  const processingLog: string[] = [];

  processingLog.push(`Starting OpFlow creation for schema: ${schemaVarName}`);
  processingLog.push(`Source file: ${filePath}`);

  const sourceFile: SourceFile | undefined = project.addSourceFileAtPathIfExists(filePath);
  if (!sourceFile) {
    const error = `Source file not found: ${filePath}`;
    processingLog.push(`ERROR: ${error}`);
    throw new Error(error);
  }

  const schemaVar = sourceFile.getVariableDeclaration(schemaVarName);
  if (!schemaVar) {
    const error = `Schema variable declaration not found: ${schemaVarName}`;
    processingLog.push(`ERROR: ${error}`);
    throw new Error(error);
  }

  const schemaNode = schemaVar.getInitializer();
  if (!schemaNode) {
    const error = `Schema initializer not found for: ${schemaVarName}`;
    processingLog.push(`ERROR: ${error}`);
    throw new Error(error);
  }

  processingLog.push(`Schema AST node type: ${schemaNode.getKindName()}`);
  processingLog.push(`Schema AST text: ${schemaNode.getText().substring(0, 100)}...`);

  const opFlow: OpFlow = [];
  buildOpFlowRecursive(schemaNode, protoRegistry, opFlow, schemaVarName, processingLog);

  processingLog.push(`OpFlow creation completed. Generated ${opFlow.length} operations.`);

  // Write debug file for inspection
  writeOpFlowDebugFile(registryEntry, opFlow, protoRoot, processingLog);

  return opFlow;
}

/**
 * Recursively walk the schema AST and build the OpFlow array.
 */
function buildOpFlowRecursive(
  node: Node,
  protoRegistry: ProtoRegistry,
  opFlow: OpFlow,
  schemaName: string,
  processingLog: string[],
) {
  processingLog.push(`Processing node type: ${node.getKindName()} for ${schemaName}`);
  if (schemaName === 'prodAddSchema') {
    // [DEBUG]
    console.log();
  }
  // Detect wrappers
  if (isWrapperNode(node)) {
    const wrapperType = getWrapperType(node);
    if (wrapperType === 'lazy') {
      handleLazyWrapper(node, protoRegistry, opFlow, schemaName, processingLog);
      return;
    }
    const objectNodes: ObjectNode[] = getWrapperObjectNodes(node, protoRegistry, schemaName, processingLog);
    processingLog.push(`Found ${wrapperType} wrapper for ${schemaName} with ${objectNodes.length} members`);
    opFlow.push({ type: wrapperType, objectNodes });
    return;
  }

  // Detect pick/omit modifiers on object/extend
  if (isPickOrOmitNode(node)) {
    const { baseNode, modifier } = extractPickOrOmit(node);
    processingLog.push(
      `Found ${modifier.type} operation for ${schemaName} with ${modifier.fields.length} fields`,
    );

    // Recursively build the base object node
    const objNode = buildObjectNode(baseNode, protoRegistry, schemaName, processingLog);
    objNode.modifiers = modifier;
    opFlow.push(objNode);
    return;
  }

  // Detect object/extend
  if (isObjectOrExtendNode(node)) {
    processingLog.push(`Found object/extend node for ${schemaName}`);
    const objNode = buildObjectNode(node, protoRegistry, schemaName, processingLog);
    opFlow.push(objNode);
    return;
  }

  // detect enum
  if (isEnumNode(node)) {
    processingLog.push(`Found enum node for ${schemaName}`);
    const enumNode = buildEnumNode(node, protoRegistry, schemaName, processingLog);
    opFlow.push(enumNode);
    return;
  }

  // Fallback: log unhandled node types for debugging
  processingLog.push(`WARNING: Unhandled AST node for ${schemaName}: ${node.getKindName()}`);
  processingLog.push(`Node text: ${node.getText().substring(0, 200)}...`);
}

// --- Type conversion helpers ---

/**
 * Converts Zod field type expressions to proto types
 */
// TODO: implement config settings and typeS
function convertZodTypeToProto(zodType: string, schemaName: string): string {
  // Remove whitespace and normalize
  const normalized = stripZodModifiers(zodType.trim());

  // Basic type mappings
  if (normalized === 'z.string()') return 'string';
  if (normalized === 'z.number()') return 'double';
  if (normalized === 'z.boolean()') return 'bool';
  if (normalized === 'z.date()') return 'google.protobuf.Timestamp';
  if (normalized.startsWith('z.literal(')) return 'string'; // Literals become strings
  if (normalized.startsWith('z.enum(')) return 'string'; // TODO: Enums become strings for now

  // since we checked for identifier, this should be a simple array?
  // but we need to extract the inner type and return it as repeated
  if (normalized.startsWith('z.array(')) {
    const match = normalized.match(/z\.array\((.*)\)$/);
    if (match && match[1]) {
      return 'repeated ' + convertZodTypeToProto(match[1], schemaName);
    }
  }
  // if (normalized.startsWith('z.array(')) return 'repeated string'; // Arrays need special handling

  if (normalized.startsWith('z.optional(')) {
    // Extract the inner type from z.optional(z.string()) -> z.string()
    const match = normalized.match(/z\.optional\((.*)\)$/);
    if (match && match[1]) {
      return 'optional ' + convertZodTypeToProto(match[1], schemaName);
    }
  }
  // Default fallback, should be matched and replaced in postProcessFields
  return normalized;
}

// --- Helper detection and builder functions ---

function isWrapperNode(node: Node): boolean {
  const callInfo = getCallExpressionInfo(node);
  if (!callInfo) return false;
  const { expr } = callInfo;
  return (
    expr === 'z.union' ||
    expr === 'z.intersection' ||
    expr === 'z.discriminatedUnion' ||
    expr === 'z.array' ||
    expr === 'z.lazy'
  );
}

function isObjectOrExtendNode(node: Node): boolean {
  // Detect z.object({...}) or .extend({...}) calls
  const callInfo = getCallExpressionInfo(node);
  if (!callInfo) {
    // Also accept identifier references (e.g., UserSchema in UserSchema.pick(...))
    return Node.isIdentifier(node);
  }
  const { expr } = callInfo;
  if (expr === 'z.object') return true;
  // .extend() is a chained call: obj.extend({...})
  if (Node.isCallExpression(node) && Node.isPropertyAccessExpression(node.getExpression())) {
    const exprNode = node.getExpression();
    if (Node.isPropertyAccessExpression(exprNode)) {
      const prop = exprNode.getName();
      if (prop === 'extend') return true;
    }
  }
  return false;
}

function isPickOrOmitNode(node: Node): boolean {
  // Detect .pick({...}) or .omit({...}) calls on object/extend
  if (!Node.isCallExpression(node)) return false;
  const expr = node.getExpression();
  if (!Node.isPropertyAccessExpression(expr)) return false;
  const method = expr.getName();
  if (method !== 'pick' && method !== 'omit') return false;
  // Ensure the base is an object/extend node
  const base = expr.getExpression();
  return isObjectOrExtendNode(base);
}

function isEnumNode(node: Node): boolean {
  // Detect z.object({...}) or .extend({...}) calls
  const callInfo = getCallExpressionInfo(node);
  if (!callInfo) {
    // Also accept identifier references (e.g., UserSchema in UserSchema.pick(...))
    return false;
  }
  const { expr } = callInfo;
  if (expr === 'z.enum') return true;
  return false;
}

function getWrapperType(node: Node): Wrapper['type'] {
  const callInfo = getCallExpressionInfo(node);
  if (callInfo) {
    const { expr } = callInfo;
    if (expr === 'z.union') return 'union';
    if (expr === 'z.intersection') return 'intersection';
    if (expr === 'z.discriminatedUnion') return 'discriminatedUnion';
    if (expr === 'z.array') return 'array';
    if (expr === 'z.lazy') return 'lazy';
  }
  return 'union'; // fallback
}

function getWrapperObjectNodes(
  node: Node,
  protoRegistry: ProtoRegistry,
  schemaName: string,
  processingLog: string[],
): ObjectNode[] {
  const callInfo = getCallExpressionInfo(node);
  if (!callInfo) {
    processingLog.push(`WARNING: No call info found for wrapper node in ${schemaName}`);
    return [];
  }
  const { expr, args } = callInfo;

  // Handle z.union and z.intersection with array syntax: z.intersection([UserSchema, AddressSchema])
  if (
    (expr === 'z.union' || expr === 'z.intersection') &&
    args.length > 0 &&
    Node.isArrayLiteralExpression(args[0])
  ) {
    const elements = args[0].getElements();
    processingLog.push(`Processing ${expr} with ${elements.length} elements (array syntax)`);
    return elements.map((el, idx) =>
      buildObjectNode(el, protoRegistry, `${schemaName}_Member${idx + 1}`, processingLog),
    );
  }

  // Handle z.intersection with individual arguments: z.intersection(UserSchema, AddressSchema)
  if (expr === 'z.intersection' && args.length >= 2) {
    processingLog.push(`Processing ${expr} with ${args.length} arguments (individual syntax)`);
    return args.map((arg, idx) =>
      buildObjectNode(arg, protoRegistry, `${schemaName}_Member${idx + 1}`, processingLog),
    );
  }

  // Handle z.union with individual arguments if needed (less common but for consistency)
  if (expr === 'z.union' && args.length >= 2) {
    processingLog.push(`Processing ${expr} with ${args.length} arguments (individual syntax)`);
    return args.map((arg, idx) =>
      buildObjectNode(arg, protoRegistry, `${schemaName}_Member${idx + 1}`, processingLog),
    );
  }
  if (expr === 'z.discriminatedUnion' && args.length > 1 && Node.isArrayLiteralExpression(args[1])) {
    const elements = args[1].getElements();
    processingLog.push(`Processing discriminatedUnion with ${elements.length} elements`);
    return elements.map((el, idx) =>
      buildObjectNode(el, protoRegistry, `${schemaName}_Discriminated${idx + 1}`, processingLog),
    );
  }
  if (expr === 'z.array' && args.length > 0) {
    processingLog.push(`Processing array wrapper`);
    return [buildObjectNode(args[0]!, protoRegistry, `${schemaName}_Element`, processingLog)];
  }
  // Handle z.lazy: process the return value of the function argument
  if (expr === 'z.lazy' && args.length > 0) {
    const fnArg = args[0];
    // Only handle arrow/function expressions
    if (Node.isArrowFunction(fnArg) || Node.isFunctionExpression(fnArg)) {
      const body = fnArg.getBody();
      let returnExpr: Node | undefined;
      if (Node.isBlock(body)) {
        // function with block: find return statement
        const returnStmt = body.getStatements().find(st => st.getKindName() === 'ReturnStatement');
        if (returnStmt && Node.isReturnStatement(returnStmt)) {
          returnExpr = returnStmt.getExpression();
        }
      } else {
        // concise arrow function: body is the return expr
        returnExpr = body;
      }
      if (returnExpr) {
        processingLog.push('Processing z.lazy: recursing into returned schema');
        return [buildObjectNode(returnExpr, protoRegistry, `${schemaName}_Lazy`, processingLog)];
      } else {
        processingLog.push('WARNING: z.lazy function has no return expression');
      }
    } else {
      processingLog.push('WARNING: z.lazy argument is not a function');
    }
    // If we can't process, just skip and continue
    return [];
  }
  processingLog.push(`WARNING: Unhandled wrapper expression: ${expr}`);
  return [];
}

function buildEnumNode(
  node: Node,
  protoRegistry: ProtoRegistry,
  schemaName: string,
  processingLog: string[],
): EnumNode {
  // Detect z.enum calls
  const callInfo = getCallExpressionInfo(node);
  if (callInfo) {
    const { expr, args } = callInfo;
    if (expr === 'z.enum' && args.length > 0) {
      const objLit = args[0];
      if (Node.isArrayLiteralExpression(objLit)) {
        const values = objLit.getElements().map((el, idx) => ({
          name: el.getText().replace(/['"]/g, ''),
          number: idx,
        }));
        processingLog.push(`Found enum with ${values.length} values`);
        return {
          type: 'enum',
          name: schemaName,
          values,
        };
      }
    }
  }
  processingLog.push(`WARNING: Unhandled enum node for ${schemaName}`);
  return {
    type: 'enum',
    name: schemaName,
    values: [],
  };
}

// Allows for post-processing or insertion of fields after initial field extraction
function postProcessFields(fields: Field[], schemaName: string, processingLog: string[]): Field[] {
  // Insert a new field after any field with name matching /Id$/ and type in fieldTypesToMatch
  const fieldTypesToMatch = ['preprocessObjectId32', 'preprocessObjectId32TYPED'];
  const newFields: Field[] = [];
  if (schemaName === 'GroupSchema') {
    // [DEBUG]
    console.log();
  }
  for (let i = 0; i < fields.length; i++) {
    const field = fields[i];
    if (!field || typeof field.name !== 'string' || typeof field.type !== 'string') {
      continue;
    }
    newFields.push(field);
    if (field.name.endsWith('Id') && fieldTypesToMatch.includes(field.type)) {
      const newFieldName = field.name.slice(0, -2); // Remove 'Id' suffix
      if (newFieldName) {
        newFields.push({
          name: newFieldName,
          type: 'google.protobuf.Struct',
        });
        processingLog.push(
          `Inserted field '${newFieldName}' of type 'google.protobuf.Struct' after '${field.name}' in schema '${schemaName}'`,
        );
      }
    }
    // let's look for an array of preprocessObjectId32TYPED fields
    if (field.name.endsWith('Ids') && fieldTypesToMatch.includes(field.type)) {
      const newFieldName = field.name.slice(0, -2); // Remove 'Id' suffix
      if (newFieldName) {
        newFields.push({
          name: newFieldName,
          type: 'google.protobuf.Struct',
        });
        processingLog.push(
          `Inserted field '${newFieldName}' of type 'google.protobuf.Struct' after '${field.name}' in schema '${schemaName}'`,
        );
      }
    }
  }
  return newFields;
}

function buildObjectNode(
  node: Node,
  protoRegistry: ProtoRegistry,
  schemaName: string,
  processingLog: string[],
): ObjectNode {
  // Check if this is an identifier reference (e.g., UserSchema)
  if (Node.isIdentifier(node)) {
    const identifierName = node.getText();
    processingLog.push(`Found identifier reference: ${identifierName}`);
    return {
      type: 'object',
      fields: [],
      schemaName: identifierName,
      isReference: true,
    };
  }
  // Build and return an ObjectNode from z.object({...}) or .extend({...})
  const callInfo = getCallExpressionInfo(node);
  let fields: Field[] = [];
  if (callInfo) {
    const { expr, args } = callInfo;
    if (expr === 'z.object' && args.length > 0 && Node.isObjectLiteralExpression(args[0])) {
      // z.object({ ... })
      const objLit = args[0];
      fields = objLit.getProperties().flatMap(prop => {
        if (Node.isPropertyAssignment(prop)) {
          const name = prop.getName();
          const initializer = prop.getInitializer();
          // Only check if this field is a reference to another schema
          const coreNode = initializer ? stripZodWrappersFromNode(initializer) : undefined;
          const baseIdentifier = coreNode ? getBaseIdentifierFromCallChain(coreNode) : undefined;
          if (baseIdentifier && Node.isIdentifier(baseIdentifier)) {
            // Reference to another schema (even if wrapped in .optional/.nullable/.default)
            const refName = baseIdentifier.getText();
            processingLog.push(`Object field: ${name} (reference to schema: ${refName})`);
            return [{ name, type: refName, isReference: true } as Field];
          } else {
            // Do NOT recurse into the initializer if it's not a reference
            const rawType = initializer?.getText() ?? 'unknown';
            const protoType = convertZodTypeToProto(rawType, schemaName);
            processingLog.push(`Object field: ${name} (${rawType} -> ${protoType})`);
            return [{ name, type: protoType } as Field];
          }
        }
        return [] as Field[];
      });
      // Post-process fields here
      fields = postProcessFields(fields, schemaName, processingLog);
      return {
        type: 'object',
        fields: Array.isArray(fields) ? fields : [],
        isReference: false,
        // baseProto: lookup from protoRegistry if needed
        // modifiers: undefined
      };
    } else if (Node.isCallExpression(node) && Node.isPropertyAccessExpression(node.getExpression())) {
      const exprNode = node.getExpression();
      if (Node.isPropertyAccessExpression(exprNode)) {
        const prop = exprNode.getName();
        if (prop === 'extend' && args.length > 0 && Node.isObjectLiteralExpression(args[0])) {
          // For extend operations, capture the base schema name
          const baseExpression = exprNode.getExpression();
          let baseSchemaName: string | undefined;

          if (Node.isIdentifier(baseExpression)) {
            baseSchemaName = baseExpression.getText();
            processingLog.push(`Extend operation base schema: ${baseSchemaName}`);
          }

          const objLit = args[0];
          const extendFields: Field[] = objLit.getProperties().flatMap(prop => {
            if (Node.isPropertyAssignment(prop)) {
              const name = prop.getName();

              const initializer = prop.getInitializer();
              const baseIdentifier = initializer ? getBaseIdentifierFromCallChain(initializer) : undefined;
              const rawType = baseIdentifier?.getText() ?? 'unknown';
              const protoType = convertZodTypeToProto(rawType, schemaName);
              processingLog.push(`Extend field: ${name} (${rawType} -> ${protoType})`);
              return [{ name, type: protoType } as Field];
            }
            return [] as Field[];
          });
          fields = fields.concat(extendFields);

          // Post-process fields here
          if (schemaName === 'InvitationSchema') {
            console.log('PPCall 2 fields', fields);
          }
          fields = postProcessFields(fields, schemaName, processingLog);

          // Return extend operation with base schema tracking
          return {
            type: 'extend',
            fields: Array.isArray(fields) ? fields : [],
            baseSchemaName,
            isReference: false,
          };
        }
      }
    }
  }
  // If not z.object or .extend, just return whatever fields we have (no post-processing)
  return {
    type: 'object',
    fields: Array.isArray(fields) ? fields : [],
    isReference: false,
    // baseProto: lookup from protoRegistry if needed
    // modifiers: undefined
  };
}

function extractPickOrOmit(node: Node): { baseNode: Node; modifier: Modifier } {
  // node is a CallExpression for .pick({...}) or .omit({...})
  if (!Node.isCallExpression(node)) throw new Error('Not a CallExpression');
  const expr = node.getExpression();
  if (!Node.isPropertyAccessExpression(expr)) throw new Error('Not a PropertyAccessExpression');
  const method = expr.getName();
  const baseNode = expr.getExpression();
  const args = node.getArguments();
  let fields: Field[] = [];
  if (args.length > 0 && Node.isObjectLiteralExpression(args[0])) {
    // z.object(...).pick({ a: true, b: true })
    fields = args[0].getProperties().flatMap(prop => {
      if (Node.isPropertyAssignment(prop)) {
        return [{ name: prop.getName() }];
      }
      return [];
    });
  } else if (args.length > 0 && Node.isArrayLiteralExpression(args[0])) {
    // z.object(...).pick(['a', 'b'])
    fields = args[0].getElements().map(el => ({
      name: el.getText().replace(/['"]/g, ''),
    }));
  }
  const modifier: Modifier = {
    type: method as 'pick' | 'omit',
    fields,
  };
  return { baseNode, modifier };
}

function getCallExpressionInfo(node: Node): { expr: string; args: Node[] } | null {
  if (Node.isCallExpression(node)) {
    const expr = node.getExpression().getText();
    const args = node.getArguments();
    return { expr, args };
  }
  return null;
}

/**
 * Writes OpFlow debug information to a JSON file for inspection
 */
function writeOpFlowDebugFile(
  registryEntry: ProtoRegistryEntry,
  opFlow: OpFlow,
  protoRoot: string,
  processingLog: string[],
) {
  const debugDir = path.join(protoRoot, '__debug__');
  if (!fs.existsSync(debugDir)) {
    fs.mkdirSync(debugDir, { recursive: true });
  }

  const debugInfo = {
    schemaName: registryEntry.schemaName,
    definedIn: registryEntry.definedIn,
    opFlowLength: opFlow.length,
    operations: opFlow.map((op, index) => ({
      index,
      type: op.type,
      ...(op.type === 'union' ||
      op.type === 'intersection' ||
      op.type === 'discriminatedUnion' ||
      op.type === 'array'
        ? {
            objectNodesCount: (op as any).objectNodes?.length || 0,
            objectNodes:
              (op as any).objectNodes?.map((node: any, nodeIndex: number) => ({
                nodeIndex,
                type: node.type,
                fieldsCount: node.fields?.length || 0,
                fields: node.fields?.map((f: any) => ({ name: f.name, type: f.type })) || [],
                schemaName: node.schemaName,
                isReference: node.isReference,
                baseSchemaName: node.baseSchemaName,
                modifiers: node.modifiers,
              })) || [],
          }
        : {
            fieldsCount: (op as any).fields?.length || 0,
            fields: (op as any).fields?.map((f: any) => ({ name: f.name, type: f.type })) || [],
            schemaName: (op as any).schemaName,
            isReference: (op as any).isReference,
            baseSchemaName: (op as any).baseSchemaName,
            modifiers: (op as any).modifiers,
          }),
    })),
    // Processing log section at the bottom
    processingLog: {
      totalEntries: processingLog.length,
      entries: processingLog.map((entry, index) => ({
        index,
        timestamp: new Date().toISOString(),
        message: entry,
      })),
    },
  };

  const debugFilePath = path.join(debugDir, `${registryEntry.schemaName}.opflow.json`);
  fs.writeFileSync(debugFilePath, JSON.stringify(debugInfo, null, 2));
  debugLog.flow(`Debug OpFlow written: ${debugFilePath}`);
}

/**
 * Handles z.lazy wrapper nodes by stripping the lazy and recursing into the actual schema node.
 */
function handleLazyWrapper(
  node: Node,
  protoRegistry: ProtoRegistry,
  opFlow: OpFlow,
  schemaName: string,
  processingLog: string[],
) {
  const callInfo = getCallExpressionInfo(node);
  if (callInfo && callInfo.args.length > 0) {
    const fnArg = callInfo.args[0];
    if (Node.isArrowFunction(fnArg) || Node.isFunctionExpression(fnArg)) {
      const body = fnArg.getBody();
      let returnExpr: Node | undefined;
      if (Node.isBlock(body)) {
        const returnStmt = body.getStatements().find(st => st.getKindName() === 'ReturnStatement');
        if (returnStmt && Node.isReturnStatement(returnStmt)) {
          returnExpr = returnStmt.getExpression();
        }
      } else {
        returnExpr = body;
      }
      if (returnExpr) {
        processingLog.push('Stripping z.lazy: recursing into returned schema');
        buildOpFlowRecursive(returnExpr, protoRegistry, opFlow, schemaName, processingLog);
        return;
      }
    }
  }
  processingLog.push('WARNING: z.lazy could not be resolved');
}
