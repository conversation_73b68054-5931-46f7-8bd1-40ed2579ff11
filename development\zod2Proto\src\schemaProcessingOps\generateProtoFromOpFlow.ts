import * as fs from 'fs';
import * as path from 'path';
import {
  Config,
  isImportCommonProtoType,
  ProtoImportEntry,
  ProtoRegistry,
  ProtoRegistryEntry,
  WELL_KNOWN_PROTO_IMPORTS,
  OpNode,
  ObjectOp,
  ExtendOp,
  UnionOp,
  IntersectionOp,
  ArrayOp,
  FieldOp,
  EnumOp,
} from '../types';
import { OpFlow } from '../types';
import { collectProtoImportsFromOpFlow, findImportEntryBySchemaName } from './protoImportUtils';
import { collectOneofWrapperMessages } from './protoOneofMessageUtils';
import { extractEntityName, determineCategoryFromRules, createProtoDirectory } from './utils/namingUtils';
import { getHeader } from '../utils/headerUtils';
import { ProtoMessageBuilder } from '../utils/protoMessageBuilder';
import { debugLog } from '../utils/debugUtils';
import { getFullyQualifiedProtoMessageName } from '../utils/protoMessageNaming';

/**
 * Entry point: generates a .proto file from the operations flow for a registry entry.
 */
export function generateProtoFromOpFlow(
  registryEntry: ProtoRegistryEntry,
  protoRoot: string,
  yamlConfig: Config,
  protoRegistry: ProtoRegistry,
  operations: OpFlow,
) {
  // 1. Determine output file path and proto package using proven naming utilities
  if (registryEntry.protoFile === '') {
    throw new Error(`Proto path not set for ${registryEntry.schemaName}`);
  }

  // Update the registry entry's category using the naming rules
  // what is the category used for?
  const updatedCategory = determineCategoryFromRules(
    registryEntry.schemaName,
    yamlConfig?.naming?.category_rules || [],
  );
  registryEntry.category = updatedCategory;
  debugLog.info(`Updated category for ${registryEntry.schemaName}: '${updatedCategory}'`);

  // 2. Generate header with metadata (from old implementation)
  const sourceRelativePath = path
    .normalize(path.relative(process.cwd(), registryEntry.definedIn))
    .replace(/\\/g, '/');
  const meta = yamlConfig.meta || {};
  const now = new Date();
  const dateStr = now.toISOString().replace('T', ' ').replace(/\..*$/, ' UTC');
  const header = getHeader(meta, dateStr, sourceRelativePath, registryEntry.schemaName);

  // 3. Start proto content
  let protoContent = '';
  protoContent += header;

  // Add composition comments if registry entry has operations (indicating it's composed)
  // if (registryEntry.operations && registryEntry.operations.length > 0) {
  //   protoContent += generateCompositionComment(registryEntry);
  // }

  protoContent += 'syntax = "proto3";\n';
  protoContent += `package ${registryEntry.protoPackageName};\n\n`;

  // 5. Collect imports for the proto file
  registryEntry.protoImports =
    collectProtoImportsFromOpFlow(operations, registryEntry, protoRegistry, yamlConfig) ??
    new Map<string, ProtoImportEntry>();

  // ALL imports found are collected here.
  // if an import is found in a union, intersection, ... later, it may need to be removed
  // as those types are generated inline and do not need to be imported.
  // imports are relative files paths from the root of the protobuf processor
  for (const [key, protoImportEntry] of registryEntry.protoImports) {
    if (protoImportEntry.type === 'registryKey') {
      const importedRegistryEntry = protoRegistry.get(protoImportEntry.key);
      if (importedRegistryEntry) {
        // add prefix of protoImportRefRoot if it exists
        const importFile = (
          yamlConfig.paths?.protoImportRefRoot
            ? `${yamlConfig.paths?.protoImportRefRoot}${importedRegistryEntry.protoFile}`
            : importedRegistryEntry.protoFile
        )
          .replaceAll('\\', '/')
          .replace(/^\/+/, '');

        protoContent += `import "${importFile}";\n`;
      }
    } else if (protoImportEntry.type === 'importString') {
      protoContent += `import "${protoImportEntry.key}";\n`;
    }
  }
  if (registryEntry.protoImports.size > 0) protoContent += '\n';

  // 6. Collect and emit oneof wrapper messages
  const oneofWrappers = collectOneofWrapperMessages(registryEntry);
  for (const wrapper of oneofWrappers) {
    protoContent += renderMessage(wrapper) + '\n';
  }
  if (oneofWrappers.length > 0) protoContent += '\n';

  // 7. Process OpFlow operations to render messages
  // depending on opType, the properties of op are different.

  for (const op of operations) {
    switch (op.type) {
      case 'object':
        protoContent +=
          renderObjectAsMessage(op as ObjectOp, registryEntry, protoRegistry, yamlConfig) + '\n';
        break;
      case 'extend':
        protoContent +=
          renderExtendAsMessage(op as ExtendOp, registryEntry, protoRegistry, yamlConfig) + '\n';
        break;
      case 'union':
        protoContent += renderUnionAsMessage(op as UnionOp, registryEntry, protoRegistry, yamlConfig) + '\n';
        break;
      case 'intersection':
        protoContent +=
          renderIntersectionAsMessage(op as IntersectionOp, registryEntry, protoRegistry, yamlConfig) + '\n';
        break;
      case 'discriminatedUnion':
        protoContent += renderUnionAsMessage(op as UnionOp, registryEntry, protoRegistry, yamlConfig) + '\n';
        break;
      case 'array':
        protoContent += renderArrayAsMessage(op as ArrayOp, registryEntry, protoRegistry, yamlConfig) + '\n';
        break;
      case 'field':
        protoContent += renderFieldOperation(op as FieldOp, registryEntry, protoRegistry, yamlConfig) + '\n';
        break;
      case 'enum':
        protoContent += renderEnum(op as EnumOp) + '\n';
        break;
      default:
        debugLog.warn(`[generateProtoFromOpFlow] Unknown operation type: ${op.type}`);
    }
  }

  // 8. Store the generated content in the registry entry for later reference
  registryEntry.protoContent = protoContent;
  registryEntry.protoFields = extractFieldsFromProtoContent(protoContent);

  // 9. Write to file using the determined path (create full absolute path from protoRoot? and registryEntry.protoPath)
  const protoFilePath = createProtoDirectory(registryEntry.protoFile, protoRoot);
  fs.writeFileSync(protoFilePath, protoContent);
  debugLog.file(`Wrote proto: ${protoFilePath}`);
}

// --- Helpers for rendering operations as proto constructs ---

function renderObjectAsMessage(
  op: ObjectOp,
  registryEntry: ProtoRegistryEntry,
  protoRegistry: ProtoRegistry,
  yamlConfig: Config,
): string {
  const messageName = registryEntry.protoMessageName;

  const builder = new ProtoMessageBuilder();

  debugLog.operation(
    `[generateProtoFromOpFlow] Generating object message: ${registryEntry.schemaName} -> ${messageName}`,
  );
  // Start message
  builder.startMessage(messageName);

  let fieldsToRender: any[] = [];
  const renderers = {
    object: renderObjectAsMessage,
    extend: renderExtendAsMessage,
    union: renderUnionAsMessage,
    intersection: renderIntersectionAsMessage,
    array: renderArrayAsMessage,
  };
  // This segment of code handles is only executed for object nodes that are references to another schema
  // and have modifiers (like .pick() or .omit()).
  if (op.isReference && op.schemaName && op.modifiers && protoRegistry) {
    debugLog.detail(
      `[generateProtoFromOpFlow] Processing schema reference ${op.schemaName} with ${op.modifiers.type} modifier`,
    );
    let baseRegistryEntry: ProtoRegistryEntry | undefined = undefined;
    if (registryEntry.protoImports && registryEntry.protoImports.size > 0) {
      baseRegistryEntry = findImportEntryBySchemaName(
        op.schemaName,
        registryEntry.protoImports,
        protoRegistry,
      );
    }
    // Extract fields from the referenced base schema
    // (should the modifiers be added during the generateOpFlowFromSchema process?)
    const baseFields = extractFieldsFromBaseSchema(op.schemaName, baseRegistryEntry, yamlConfig);
    debugLog.detail(
      `[generateProtoFromOpFlow] Extracted ${baseFields.length} fields from base schema ${op.schemaName}`,
    );
    if (op.modifiers.type === 'pick') {
      const fieldNames = op.modifiers.fields.map((f: any) => f.name);
      debugLog.detail(
        `[generateProtoFromOpFlow] Applying pick modifier for fields: ${fieldNames.join(', ')}`,
      );
      const filteredFields = baseFields.filter(field => fieldNames.includes(field.name));
      fieldsToRender = filteredFields.map((field, index) => ({
        ...field,
        number: index + 1,
        definition: `${field.type} ${field.name} = ${index + 1}`,
      }));
    } else if (op.modifiers.type === 'omit') {
      const fieldNames = op.modifiers.fields.map((f: any) => f.name);
      debugLog.detail(
        `[generateProtoFromOpFlow] Applying omit modifier, excluding fields: ${fieldNames.join(', ')}`,
      );
      const filteredFields = baseFields.filter(field => !fieldNames.includes(field.name));
      fieldsToRender = filteredFields.map((field, index) => ({
        ...field,
        number: index + 1,
        definition: `${field.type} ${field.name} = ${index + 1}`,
      }));
    }
    debugLog.trace(
      `[generateProtoFromOpFlow] Final field count after ${op.modifiers.type}: ${fieldsToRender.length}`,
    );
  } else if (op.fields && Array.isArray(op.fields)) {
    fieldsToRender = op.fields.map((field: any, index: number) => {
      const type = resolveProtoTypeForField(field, registryEntry, protoRegistry, yamlConfig, renderers);
      return {
        name: field.name,
        type,
        number: index + 1,
        definition: `${type} ${field.name} = ${index + 1}`,
      };
    });
  }

  // Add all fields to the message
  fieldsToRender.forEach(field => {
    builder.addField(field.definition, true);
  });

  builder.endMessage();
  return builder.build();
}

/**
 * Renders extend operation by first extracting base schema fields,
 * then adding/overwriting with extend fields, then applying modifiers
 */
function renderExtendAsMessage(
  op: ExtendOp,
  extendedRegistryEntry: ProtoRegistryEntry,
  protoRegistry: ProtoRegistry,
  yamlConfig: Config,
): string {
  const messageName = extendedRegistryEntry?.protoMessageName || extendedRegistryEntry.schemaName;
  const builder = new ProtoMessageBuilder();

  debugLog.operation(
    `[generateProtoFromOpFlow] Generating extend message: ${extendedRegistryEntry.schemaName} -> ${messageName}`,
  );
  builder.startMessage(messageName);

  // Step 1: Extract fields from base schema if it exists
  const baseFields = new Map<string, any>();
  let nextFieldNumber = 1;

  const baseSchemaName = op.baseSchemaName;
  const renderers = {
    object: renderObjectAsMessage,
    extend: renderExtendAsMessage,
    union: renderUnionAsMessage,
    intersection: renderIntersectionAsMessage,
    array: renderArrayAsMessage,
  };
  if (baseSchemaName && protoRegistry) {
    let baseRegistryEntry: ProtoRegistryEntry | undefined = undefined;

    if (extendedRegistryEntry.protoImports && extendedRegistryEntry.protoImports.size > 0) {
      baseRegistryEntry = findImportEntryBySchemaName(
        baseSchemaName,
        extendedRegistryEntry.protoImports,
        protoRegistry,
      );
    }

    if (!baseRegistryEntry) {
      debugLog.warn(`[generateProtoFromOpFlow] No base schema found for extend operation in ${messageName}`);
    } else {
      debugLog.detail(`[generateProtoFromOpFlow] Extending from base schema: ${baseSchemaName}`);
      const extractedFields = extractFieldsFromBaseSchema(baseSchemaName, baseRegistryEntry, yamlConfig);

      extractedFields.forEach(field => {
        baseFields.set(field.name, field);
        nextFieldNumber = Math.max(nextFieldNumber, field.number + 1);
      });
      debugLog.detail(
        `[generateProtoFromOpFlow] Extracted ${extractedFields.length} fields from base schema`,
      );
    }
  }

  // Step 2: Add/overwrite with fields from the extend operation
  if (op.fields && Array.isArray(op.fields)) {
    debugLog.detail(`[generateProtoFromOpFlow] Adding ${op.fields.length} extend fields to ${messageName}`);
    op.fields.forEach((field: any) => {
      const fieldType = field.type || 'string';
      const repeated = '';
      const optional = '';

      let fieldNumber = nextFieldNumber;
      const isOverwrite = baseFields.has(field.name);
      if (isOverwrite) {
        fieldNumber = baseFields.get(field.name).number;
        debugLog.trace(`[generateProtoFromOpFlow] Overwriting field: ${field.name}`);
      } else {
        nextFieldNumber++;
        debugLog.trace(`[generateProtoFromOpFlow] Adding new field: ${field.name}`);
      }
      const type = resolveProtoTypeForField(
        field,
        extendedRegistryEntry,
        protoRegistry,
        yamlConfig,
        renderers,
      );
      const enhancedField = {
        name: field.name,
        type,
        repeated: false,
        optional: false,
        number: fieldNumber,
        definition: `${type} ${field.name} = ${fieldNumber}`,
      };

      baseFields.set(field.name, enhancedField);
    });
  }

  // Step 3: Add all fields to the builder
  const finalFields = Array.from(baseFields.values()).sort((a, b) => a.number - b.number);
  debugLog.trace(`[generateProtoFromOpFlow] Final field count for ${messageName}: ${finalFields.length}`);

  finalFields.forEach(field => {
    builder.addField(field.definition, true);
  });

  // Step 4: Handle modifiers (pick/omit) if present
  if (op.modifiers) {
    debugLog.detail(
      `[generateProtoFromOpFlow] Applying ${op.modifiers.type} modifier with ${op.modifiers.fields.length} fields`,
    );
    if (op.modifiers.type === 'pick') {
      const fieldNames = op.modifiers.fields.map((f: any) => f.name);
      builder.pickFields(fieldNames);
    } else if (op.modifiers.type === 'omit') {
      const fieldNames = op.modifiers.fields.map((f: any) => f.name);
      builder.omitFields(fieldNames);
    }
  }

  builder.endMessage();
  return builder.build();
}

/**
 * Extracts field definitions from a base schema's proto registry entry
 */
function extractFieldsFromBaseSchema(
  baseSchemaName: string,
  baseRegistryEntry: ProtoRegistryEntry | undefined,
  yamlConfig?: Config,
): Array<{ name: string; type: string; number: number; definition: string; isReference?: boolean }> {
  const fields: Array<{
    name: string;
    type: string;
    number: number;
    definition: string;
    isReference?: boolean;
  }> = [];

  if (!baseRegistryEntry) {
    debugLog.warn(`[generateProtoFromOpFlow] Base schema ${baseSchemaName} not found in registry`);
    return fields;
  }

  // Use the stored proto fields if available (preferred method)
  if (baseRegistryEntry.protoFields && baseRegistryEntry.protoFields.length > 0) {
    debugLog.detail(
      `[generateProtoFromOpFlow] Using stored proto fields from ${baseRegistryEntry.schemaName}`,
    );
    for (const protoField of baseRegistryEntry.protoFields) {
      const repeated = protoField.repeated ? 'repeated ' : '';
      const optional = protoField.optional ? 'optional ' : '';
      const isReference = !isImportCommonProtoType(protoField.type);
      fields.push({
        name: protoField.name,
        type: protoField.type,
        number: protoField.number,
        isReference,
        definition: `${repeated}${optional}${protoField.type} ${protoField.name} = ${protoField.number}`,
      });
    }
    return fields;
  }

  // Fallback: Extract fields from the base schema's operations (OpFlow is an array of operations)
  if (baseRegistryEntry.operations && Array.isArray(baseRegistryEntry.operations)) {
    debugLog.detail(
      `[generateProtoFromOpFlow] Fallback: Extracting fields from OpFlow operations for ${baseRegistryEntry.schemaName}`,
    );
    let fieldNumber = 1;
    for (const baseOp of baseRegistryEntry.operations) {
      if (baseOp.type === 'object' || baseOp.type === 'extend' || baseOp.type === 'field') {
        if (baseOp.fields && Array.isArray(baseOp.fields)) {
          for (const field of baseOp.fields) {
            const fieldType = field.type || 'string';
            // Note: Field type doesn't have repeated/optional, so we use defaults
            const repeated = '';
            const optional = '';
            const isReference = !isImportCommonProtoType(fieldType);
            fields.push({
              name: field.name,
              type: fieldType,
              number: fieldNumber,
              isReference,
              definition: `${repeated}${optional}${fieldType} ${field.name} = ${fieldNumber}`,
            });
            fieldNumber++;
          }
        }
      }
      // Handle Wrapper operations (union, intersection, discriminatedUnion, array)
      else if (
        baseOp.type === 'union' ||
        baseOp.type === 'intersection' ||
        baseOp.type === 'discriminatedUnion' ||
        baseOp.type === 'array'
      ) {
        // For wrapper types, we need to extract fields from their objectNodes
        if (baseOp.objectNodes && Array.isArray(baseOp.objectNodes)) {
          for (const objectNode of baseOp.objectNodes) {
            if (objectNode.fields && Array.isArray(objectNode.fields)) {
              for (const field of objectNode.fields) {
                const fieldType = field.type || 'string';
                const repeated = '';
                const optional = '';
                const isReference = !isImportCommonProtoType(fieldType);
                fields.push({
                  name: field.name,
                  type: fieldType,
                  number: fieldNumber,
                  isReference,
                  definition: `${repeated}${optional}${fieldType} ${field.name} = ${fieldNumber}`,
                });
                fieldNumber++;
              }
            }
          }
        }
      }
    }
  }

  return fields;
}

// a union is either one or the other of the types generated
// in a protofile this would be created as a oneof
function renderUnionAsMessage(
  op: UnionOp | { type: 'discriminatedUnion'; objectNodes: OpNode[] },
  registryEntry: ProtoRegistryEntry,
  protoRegistry: ProtoRegistry,
  yamlConfig: Config,
): string {
  const builder = new ProtoMessageBuilder();

  debugLog.operation(
    `[generateProtoFromOpFlow] Generating union message: ${registryEntry.schemaName} -> ${registryEntry.protoMessageName}`,
  );
  builder.startMessage(registryEntry.protoMessageName);

  if (op.objectNodes && Array.isArray(op.objectNodes)) {
    builder.startOneof('value');

    op.objectNodes.forEach((field: any, index: number) => {
      const fieldNumber = index + 1;

      // Use the shared utility for type resolution and recursion
      const fieldType = resolveProtoTypeForField(field, registryEntry, protoRegistry, yamlConfig, {
        object: renderObjectAsMessage,
        extend: renderExtendAsMessage,
        union: renderUnionAsMessage,
        intersection: renderIntersectionAsMessage,
        array: renderArrayAsMessage,
      });
      const fieldName =
        fieldType
          .split('.')
          .pop()
          ?.replace(/^./, c => c.toLowerCase()) || 'option';
      builder.addOneofField(`${fieldType} ${fieldName}_${index + 1} = ${fieldNumber}`);
      // builder.addOneofField(`${fieldType} option_${index + 1} = ${fieldNumber}`);
    });

    builder.endOneof();
  }

  builder.endMessage();
  return builder.build();
}

function renderEnum(op: EnumOp): string {
  // Basic enum rendering (kept for future use)
  let s = `enum ${op.name} {\n`;
  for (const v of op.values) {
    s += `  ${op.name}_${v.name} = ${v.number};\n`;
  }
  s += '}\n';
  return s;
}

/**
 * Renders intersection operation as a message that merges all fields
 */
function renderIntersectionAsMessage(
  op: IntersectionOp,
  registryEntry: ProtoRegistryEntry,
  protoRegistry: ProtoRegistry,
  yamlConfig: Config,
): string {
  const builder = new ProtoMessageBuilder();

  debugLog.operation(
    `[generateProtoFromOpFlow] Generating intersection message: ${registryEntry.schemaName} -> ${registryEntry.protoMessageName}`,
  );
  builder.startMessage(registryEntry.protoMessageName);

  // Merge fields from all referenced schemas in the intersection
  const mergedFields = new Map<string, any>();
  let nextFieldNumber = 1;
  const renderers = {
    object: renderObjectAsMessage,
    extend: renderExtendAsMessage,
    union: renderUnionAsMessage,
    intersection: renderIntersectionAsMessage,
    array: renderArrayAsMessage,
  };
  if (op.objectNodes && Array.isArray(op.objectNodes)) {
    debugLog.detail(
      `[generateProtoFromOpFlow] Creating intersection with ${op.objectNodes.length} parent message references`,
    );

    op.objectNodes.forEach((node: any) => {
      if (node.schemaName && protoRegistry && registryEntry.protoImports) {
        // Always resolve the registry entry and extract fields
        const baseRegistryEntry = findImportEntryBySchemaName(
          node.schemaName,
          registryEntry.protoImports,
          protoRegistry,
        );
        if (baseRegistryEntry) {
          debugLog.detail(
            `Resolved intersection member ${node.schemaName} -> ${baseRegistryEntry.protoMessageName}`,
          );
          const extractedFields = extractFieldsFromBaseSchema(node.schemaName, baseRegistryEntry, yamlConfig);
          for (const field of extractedFields) {
            const type = resolveProtoTypeForField(field, registryEntry, protoRegistry, yamlConfig, renderers);
            if (!mergedFields.has(field.name)) {
              mergedFields.set(field.name, {
                name: field.name,
                type,
                number: nextFieldNumber++,
                repeated: false,
                optional: false,
              });
              debugLog.trace(`[generateProtoFromOpFlow] Adding field: ${field.name} (${field.type})`);
            } else {
              debugLog.trace(
                `[generateProtoFromOpFlow] Field ${field.name} already exists, keeping first definition`,
              );
            }
          }
        } else {
          debugLog.warn(
            `[generateProtoFromOpFlow] Referenced schema ${node.schemaName} not found in registry`,
          );
        }
      } else {
        // Handle inline object nodes (if any)
        if (node.fields && Array.isArray(node.fields)) {
          debugLog.detail(`[generateProtoFromOpFlow] Adding ${node.fields.length} inline fields`);
          for (const field of node.fields) {
            const type = resolveProtoTypeForField(field, registryEntry, protoRegistry, yamlConfig, renderers);
            if (!mergedFields.has(field.name)) {
              mergedFields.set(field.name, {
                name: field.name,
                type: field.type || 'string',
                number: nextFieldNumber++,
                repeated: false,
                optional: false,
              });
              debugLog.trace(
                `[generateProtoFromOpFlow] Adding inline field: ${field.name} (${field.type || 'string'})`,
              );
            }
          }
        }
      }
    });
  }

  // Add all merged fields to the message
  for (const field of mergedFields.values()) {
    const repeated = field.repeated ? 'repeated ' : '';
    const optional = field.optional ? 'optional ' : '';
    const fieldDefinition = `${repeated}${optional}${field.type} ${field.name} = ${field.number}`;
    builder.addField(fieldDefinition, true);
  }

  debugLog.trace(
    `[generateProtoFromOpFlow] Final field count for ${registryEntry.protoMessageName}: ${mergedFields.size}`,
  );

  builder.endMessage();
  return builder.build();
}

/**
 * Renders array wrapper as repeated field or separate message
 */
function renderArrayAsMessage(
  op: ArrayOp,
  registryEntry: ProtoRegistryEntry,
  protoRegistry: ProtoRegistry,
  yamlConfig: Config,
): string {
  const builder = new ProtoMessageBuilder();

  debugLog.operation(
    `[generateProtoFromOpFlow] Generating array message: ${registryEntry.schemaName} -> ${registryEntry.protoMessageName}`,
  );
  builder.startMessage(registryEntry.protoMessageName);

  if (op.objectNodes && Array.isArray(op.objectNodes) && op.objectNodes.length > 0) {
    // For arrays, typically we have one element type
    const elementNode = op.objectNodes[0];
    let elementType = 'string';
    if ('name' in elementNode! && typeof elementNode.name === 'string') {
      elementType = elementNode.name;
    }
    if (
      'schemaName' in elementNode! &&
      elementNode.schemaName &&
      protoRegistry &&
      registryEntry.protoImports
    ) {
      const elementRegistryEntry = findImportEntryBySchemaName(
        elementNode.schemaName,
        registryEntry.protoImports,
        protoRegistry,
      );
      if (elementRegistryEntry) {
        elementType = getFullyQualifiedProtoMessageName(elementRegistryEntry);
        debugLog.detail(
          `[generateProtoFromOpFlow] Resolved array element ${elementNode.schemaName} -> ${elementType}`,
        );
      } else {
        debugLog.warn(
          `[generateProtoFromOpFlow] Array element ${elementNode.schemaName} not found in registry`,
        );
      }
    }
    builder.addField(`repeated ${elementType} items = 1`, true);
  }

  builder.endMessage();
  return builder.build();
}

/**
 * Renders individual field operations
 */
function renderFieldOperation(
  op: FieldOp,
  registryEntry: ProtoRegistryEntry,
  protoRegistry: ProtoRegistry,
  yamlConfig: Config,
): string {
  // Field operations typically modify existing messages rather than create new ones
  // This is a placeholder for field-level transformations
  return `// Field operation: ${op.type || 'unknown'}\n`;
}

/**
 * Generates enhanced composition comment (adapted from old implementation)
 */
function generateCompositionComment(registryEntry: ProtoRegistryEntry): string {
  const lines: string[] = [];

  lines.push(`// Enhanced Composition Analysis for ${registryEntry.schemaName}`);

  // Extract base schema from operations if available
  let baseSchemaName = 'N/A';
  if (registryEntry.operations && Array.isArray(registryEntry.operations)) {
    for (const op of registryEntry.operations) {
      if ((op.type === 'extend' || op.type === 'object') && op.baseSchemaName) {
        baseSchemaName = op.baseSchemaName;
        break;
      }
    }
  }
  lines.push(`// Base Schema: ${baseSchemaName}`);
  lines.push(`// Operation: N/A`);
  lines.push(`// Domain: ${registryEntry.domain}`);
  lines.push(`// Entity: ${registryEntry.entity}`);
  lines.push(`// Category: ${registryEntry.category}`);

  if (registryEntry.operations && registryEntry.operations.length > 0) {
    lines.push(`// Operations Count: ${registryEntry.operations.length}`);
    registryEntry.operations.forEach((op, index) => {
      lines.push(`//   ${index + 1}. ${op.type}`);
    });
  }

  lines.push('//');

  return lines.join('\n') + '\n';
}

function renderMessage(op: any, messageName?: string): string {
  const actualMessageName = messageName || op.name;
  let s = `message ${actualMessageName} {\n`;
  for (const f of op.fields) {
    if (f.oneof) {
      s += `  oneof ${f.oneof.name} {\n`;
      for (const o of f.oneof.fields) {
        s += `    ${o.type} ${o.name} = ${o.number};\n`;
      }
      s += '  }\n';
    } else {
      s += `  ${f.label ? f.label + ' ' : ''}${f.type} ${f.name} = ${f.number};\n`;
    }
  }
  s += '}\n';
  return s;
}

function renderService(op: any): string {
  let s = `service ${op.name} {\n`;
  for (const m of op.methods) {
    s += `  rpc ${m.name} (${m.inputType}) returns (${m.outputType});\n`;
  }
  s += '}\n';
  return s;
}

/**
 * Parses proto message content to extract field definitions
 */
function extractFieldsFromProtoContent(protoContent: string): Array<{
  name: string;
  type: string;
  number: number;
  repeated?: boolean;
  optional?: boolean;
}> {
  const fields: Array<{
    name: string;
    type: string;
    number: number;
    repeated?: boolean;
    optional?: boolean;
  }> = [];

  // Find message blocks using regex (updated to handle message names with dots)
  const messageRegex = /message\s+([\w.]+)\s*\{([^}]+)\}/g;
  let messageMatch;

  while ((messageMatch = messageRegex.exec(protoContent)) !== null) {
    const messageContent = messageMatch[2];
    if (!messageContent) continue; // Defensive: skip if messageContent is undefined or empty

    // Extract field lines (ignoring comments and empty lines)
    const fieldRegex = /^\s*(optional\s+|repeated\s+)?(\w+)\s+(\w+)\s*=\s*(\d+)\s*;/gm;
    let fieldMatch;

    while ((fieldMatch = fieldRegex.exec(messageContent)) !== null) {
      const [, modifier, type, name, number] = fieldMatch;

      // Defensive: ensure type, name, and number are defined
      if (!type || !name || !number) continue;

      const parsedNumber = parseInt(number, 10);
      if (isNaN(parsedNumber)) continue; // Defensive: skip if number is not a valid integer

      fields.push({
        name,
        type,
        number: parsedNumber,
        repeated: modifier?.trim() === 'repeated',
        optional: modifier?.trim() === 'optional',
      });
    }
  }

  return fields;
}

/**
 *
 * @param field
 * @param registryEntry
 * @param protoRegistry
 * @param yamlConfig
 * @param renderers
 * @returns
 */
function resolveProtoTypeForField(
  field: any,
  registryEntry: ProtoRegistryEntry,
  protoRegistry: ProtoRegistry,
  yamlConfig: Config,
  renderers: {
    object: typeof renderObjectAsMessage;
    extend: typeof renderExtendAsMessage;
    union: typeof renderUnionAsMessage;
    intersection: typeof renderIntersectionAsMessage;
    array: typeof renderArrayAsMessage;
  },
): string {
  // Primitive type
  if (field.type && isImportCommonProtoType(field.type)) {
    return field.type;
  }

  // if node.type is a WELL_KNOWN_PROTO_IMPORTS value in protoImports[]
  if (field.type && registryEntry.protoImports) {
    const wellKnownImport = Array.from(registryEntry.protoImports.values()).find(
      entry => entry.type === 'importString' && entry.protoMessageName === field.type,
    );
    if (wellKnownImport) {
      return field.type;
    }
  }

  // Reference to another schema, may be an object?!
  if (field.type && registryEntry.protoImports && protoRegistry) {
    let importSearchName = field.type;
    if (field.type === 'object' && field.isReference && field.schemaName) {
      importSearchName = field.schemaName;
    }
    const refEntry = findImportEntryBySchemaName(importSearchName, registryEntry.protoImports, protoRegistry);
    if (refEntry) {
      return getFullyQualifiedProtoMessageName(refEntry);
    }
  }

  // Inline complex type: recurse using the appropriate renderer
  switch (field.type) {
    case 'object':
      renderers.object(field, registryEntry, protoRegistry, yamlConfig);
      return registryEntry.protoMessageName;
    case 'extend':
      renderers.extend(field, registryEntry, protoRegistry, yamlConfig);
      return registryEntry.protoMessageName;
    case 'union':
      renderers.union(field, registryEntry, protoRegistry, yamlConfig);
      return registryEntry.protoMessageName;
    case 'discriminatedUnion':
      renderers.union(field, registryEntry, protoRegistry, yamlConfig);
      return registryEntry.protoMessageName;
    case 'intersection':
      renderers.intersection(field, registryEntry, protoRegistry, yamlConfig);
      return registryEntry.protoMessageName;
    case 'array':
      renderers.array(field, registryEntry, protoRegistry, yamlConfig);
      return registryEntry.protoMessageName;
    default:
      // Fallback: use node.name or string
      return field.name || 'string';
  }
}
