// types.ts
import { ProtoFileManifest } from './generateOpFlowFromSchema/protoTypes';
import { SchemaImportMapEntry } from './utils/buildSchemaFileImportMap';

// export type ImportMap = Record<string, string>;
// export type Messages = Record<string, string>;
// export type GetProtoType = (
//   node: Node,
//   messages: Messages,
//   parentName: string,
//   // importMap: ImportMap,
//   fromFile: string,
//   protoOutDir: string,
//   importSet: Set<string>,
//   fieldName?: string,
//   schemaKeyToVarMap?: Record<string, string>,
//   expand?: boolean,
// ) => { type: string; repeated: boolean; oneof?: string };

// Partial with required fields type
export type PartialWithRequired<T, K extends keyof T> = Partial<T> & Pick<T, K>;

export type Resolver<T> = () => T | null | undefined;

export interface Config {
  paths: {
    inputDir: string;
    protoRoot: string;
    extraProtoDirs?: string[];
    excludeDirs: string[];
    expand: boolean;
    protoDomainDefault?: string;
    protoPackageDefault?: string;
    protoImportRefRoot?: string;
  };
  protoFieldTypeReplacements?: Record<string, string>;
  meta: {
    generator: string;
    codeVersion: string;
    maintainer: string;
    teamEmail: string;
  };
  debug?: {
    level: number;
    generateOpFlowFiles?: boolean;
    includeProcessingLogs?: boolean;
  };
  naming: {
    file_patterns: {
      default: string;
      overrides?: Record<string, NamingOverride>;
    };
    message_patterns?: {
      types?: { single: string };
      commands?: { request: string; response: string };
      queries?: { request: string; response: string };
      services?: { single: string };
      composed?: {
        union: string;
        intersection: string;
        discriminatedUnion: string;
        array: string;
      };
    };
    package_patterns?: {
      default: string;
    };
    version?: string;
    category_rules?: CategoryRule[];
  };
  crud?: {
    enabled: boolean;
    generate_for: string[];
    operations: string[];
    service_pattern: string;
    file_organization: {
      commands_file: string;
      queries_file: string;
    };
    list_options: {
      pagination: boolean;
      filtering: boolean;
      sorting: boolean;
    };
    response_metadata: {
      include_message: boolean;
      include_timestamp: boolean;
      include_version: boolean;
    };
    import_types: boolean;
  };
}

export interface CategoryRule {
  pattern?: string;
  category?: string;
  default?: string;
}

export interface NamingOverride {
  pattern?: string;
  domain?: string;
  service?: string;
  category?: string;
  version?: string;
  [key: string]: any;
}

export interface SchemaMapEntry {
  referenceString: string;
  referenceId: string;
  dbName: string;
  dbId: string;
  collectionName: string;
  collectionId: string;
  schema: any;
  indexes: any[];
  serverUri: string;
  [key: string]: any;
}

export interface ProtoRegistryEntry {
  protoFile: string;
  protoMessageName: string;
  protoImports?: ProtoImportMap;
  protoPackageName?: string;
  dependencies?: string[];
  definedIn: string; // what is definedIn used for, full schema file path?
  schemaName: string;
  schemaRelativePath: string;
  schemaImports?: SchemaImportMapEntry[]; // List of schema dependencies (schema names this entry depends on)
  domain: string;
  entity: string;
  category: string;
  protoFileManifest?: ProtoFileManifest;
  // OpFlow operations for this schema
  operations?: OpFlow;
  // Generated proto content for reference and composition
  protoContent?: string;
  // Parsed field definitions for easy extraction
  protoFields?: Array<{
    name: string;
    type: string;
    number: number;
    repeated?: boolean;
    optional?: boolean;
  }>;
  // Generation state
  isGenerated?: boolean;
}

export type ProtoImportEntry =
  | { type: 'registryKey'; key: string } // must match a key in the protoRegistry
  | { type: 'importString'; protoMessageName: string; key: string }; // string such as "google/protobuf/timestamp.proto";

// key should be protoRegistryKey?
export type ProtoImportMap = Map<string, ProtoImportEntry>;

export type ProtoRegistry = Map<string, ProtoRegistryEntry>;

export interface CrudOperation {
  name: string;
  method: string;
  request_type: string;
  response_type: string;
  category: 'command' | 'query';
}

export interface GeneratedService {
  serviceName: string;
  operations: CrudOperation[];
  imports: string[];
  messages: string[];
}

// Helper functions for ProtoRegistry composite keys

/**
 * Find base schema by name (search across all files)
 */
export function findBaseSchema(
  baseSchemaName: string,
  protoRegistry: ProtoRegistry,
): ProtoRegistryEntry | null {
  for (const [key, entry] of Object.entries(protoRegistry)) {
    if (entry.schemaName === baseSchemaName) {
      return entry;
    }
  }
  return null;
}

export function isImportCommonProtoType(value: string): boolean {
  return (
    COMMON_PROTO_TYPES.includes(value) ||
    COMMON_PROTO_PREFIXES.some(prefix =>
      COMMON_PROTO_TYPES.some(identifier => `${prefix} ${identifier}` === value),
    )
  );
}

// Array of commonly used proto prefixes
const COMMON_PROTO_PREFIXES = ['repeated', 'optional'];

// Array of commonly used proto types
const COMMON_PROTO_TYPES = [
  'string',
  'int32',
  'int64',
  'uint32',
  'uint64',
  'sint32',
  'sint64',
  'fixed32',
  'fixed64',
  'sfixed32',
  'sfixed64',
  'bool',
  'float',
  'double',
  'bytes',
];

// Map of well-known/common proto types to their import paths
export const WELL_KNOWN_PROTO_IMPORTS: ProtoImportEntry[] = [
  {
    type: 'importString',
    protoMessageName: 'google.protobuf.Timestamp',
    key: 'google/protobuf/timestamp.proto',
  },
  {
    type: 'importString',
    protoMessageName: 'google.protobuf.Any',
    key: 'google/protobuf/any.proto',
  },
  {
    type: 'importString',
    protoMessageName: 'google.protobuf.Struct',
    key: 'google/protobuf/struct.proto',
  },
  {
    type: 'importString',
    protoMessageName: 'google.protobuf.Json_format',
    key: 'google/protobuf/json_format.proto',
  },
  {
    type: 'importString',
    protoMessageName: 'google.protobuf.Duration',
    key: 'google/protobuf/duration.proto',
  },
  {
    type: 'importString',
    protoMessageName: 'google.protobuf.FieldMask',
    key: 'google/protobuf/field_mask.proto',
  },
  {
    type: 'importString',
    protoMessageName: 'google.protobuf.Empty',
    key: 'google/protobuf/empty.proto',
  },
  {
    type: 'importString',
    protoMessageName: 'google.protobuf.Value',
    key: 'google/protobuf/struct.proto',
  },
  {
    type: 'importString',
    protoMessageName: 'google.protobuf.NullValue',
    key: 'google/protobuf/struct.proto',
  },
  {
    type: 'importString',
    protoMessageName: 'google.protobuf.ListValue',
    key: 'google/protobuf/list_value.proto',
  },
  {
    type: 'importString',
    protoMessageName: 'google.protobuf.DoubleValue',
    key: 'google/protobuf/wrappers.proto',
  },
  {
    type: 'importString',
    protoMessageName: 'google.protobuf.FloatValue',
    key: 'google/protobuf/wrappers.proto',
  },
  {
    type: 'importString',
    protoMessageName: 'google.protobuf.Int32Value',
    key: 'google/protobuf/wrappers.proto',
  },
  {
    type: 'importString',
    protoMessageName: 'google.protobuf.UInt32Value',
    key: 'google/protobuf/wrappers.proto',
  },
  {
    type: 'importString',
    protoMessageName: 'google.protobuf.Int64Value',
    key: 'google/protobuf/wrappers.proto',
  },
  {
    type: 'importString',
    protoMessageName: 'google.protobuf.UInt64Value',
    key: 'google/protobuf/wrappers.proto',
  },
  {
    type: 'importString',
    protoMessageName: 'google.protobuf.BoolValue',
    key: 'google/protobuf/wrappers.proto',
  },
  {
    type: 'importString',
    protoMessageName: 'google.protobuf.StringValue',
    key: 'google/protobuf/wrappers.proto',
  },
  {
    type: 'importString',
    protoMessageName: 'google.protobuf.BytesValue',
    key: 'google/protobuf/wrappers.proto',
  },
];

// AST Operations types
// Common field type for object/extend/field nodes
export interface OpField {
  name: string;
  type?: string;
  schemaName?: string;
  // ...other field properties as needed
}

// Object operation
export interface ObjectOp {
  type: 'object';
  fields: OpField[];
  isReference?: boolean;
  schemaName?: string;
  modifiers?: {
    type: 'pick' | 'omit';
    fields: { name: string }[];
  };
}

// Extend operation
export interface ExtendOp {
  type: 'extend';
  baseSchemaName: string;
  fields: OpField[];
  modifiers?: {
    type: 'pick' | 'omit';
    fields: { name: string }[];
  };
}

// Union operation
export interface UnionOp {
  type: 'union' | 'discriminatedUnion';
  objectNodes: OpNode[];
}

// Intersection operation
export interface IntersectionOp {
  type: 'intersection';
  objectNodes: OpNode[];
}

// Array operation
export interface ArrayOp {
  type: 'array';
  objectNodes: OpNode[];
}

// Field operation
export interface FieldOp {
  type: 'field';
  fields: OpField[];
}

// Enum operation (if you support enums)
export interface EnumOp {
  type: 'enum';
  name: string;
  values: { name: string; number: number }[];
}

// The discriminated union of all possible op types
export type OpNode = ObjectOp | ExtendOp | UnionOp | IntersectionOp | ArrayOp | FieldOp | EnumOp;

export type OpFlow = Operation[];

export type Operation = Wrapper | ObjectNode | EnumNode;

// Proto type should come from the app's types/protoRegistry, not here.

export type Wrapper = {
  // type should be one of the wrapper types
  type: 'union' | 'intersection' | 'discriminatedUnion' | 'array' | 'lazy';
  objectNodes: ObjectNode[];
};

export type ObjectNode = {
  type: 'object' | 'extend' | 'field';
  // baseProto: Proto; // REMOVE: proto info comes from protoRegistry, not here
  fields: Field[];
  modifiers?: Modifier;
  // For tracking schema references (e.g., UserSchema in z.union([UserSchema, ...]))
  schemaName?: string; // The name of the referenced schema (if this is an identifier reference)
  isReference?: boolean; // True if this ObjectNode represents a schema reference rather than inline fields
  // For tracking base schema in extend operations (e.g., UserSchema in UserSchema.extend({...}))
  baseSchemaName?: string; // The name of the base schema being extended
};

export type EnumNode = {
  type: 'enum';
  name: string;
  values: { name: string; number: number }[];
};

export type Modifier = {
  type: 'pick' | 'omit';
  fields: Field[];
};

export type Field = {
  name: string;
  type?: string;
};
