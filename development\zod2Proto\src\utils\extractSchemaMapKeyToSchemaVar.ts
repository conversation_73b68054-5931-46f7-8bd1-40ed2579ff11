import { Project, SyntaxKind, PropertyAssignment, Node } from 'ts-morph';
import * as path from 'path';

/**
 * Extracts a mapping of { key, value } from SchemaMapEntries in schemaMap.ts
 * key: the SchemaMapEntries property name
 * value: the variable name assigned to the `schema` property
 *
 * this registry ONLY includes entries for schemas that are defined in the SchemaMap object in the zod-database-schemas package
 *
 *
 */
export function extractSchemaMapKeyToSchemaVar(schemaMapPath: string): { key: string; value: string }[] {
  const project = new Project();
  const sourceFile = project.addSourceFileAtPath(schemaMapPath);
  const result: { key: string; value: string }[] = [];

  const schemaMapEntriesVar = sourceFile.getVariableDeclaration('SchemaMapEntries');
  if (schemaMapEntriesVar) {
    let initializer = schemaMapEntriesVar.getInitializer();
    // Unwrap AsExpression or SatisfiesExpression if present
    while (
      initializer &&
      (initializer.getKind() === SyntaxKind.AsExpression ||
        initializer.getKind() === SyntaxKind.SatisfiesExpression)
    ) {
      initializer = (initializer as any).getExpression();
    }
    if (initializer && initializer.getKind() === SyntaxKind.ObjectLiteralExpression) {
      const objLiteral = initializer as Node;
      for (const prop of (objLiteral as any).getProperties()) {
        if (prop.getKind() === SyntaxKind.PropertyAssignment) {
          const pa = prop as PropertyAssignment;
          const key = pa.getName();
          const value = pa.getInitializerIfKind(SyntaxKind.ObjectLiteralExpression);
          if (value) {
            const schemaProp = value.getProperty('schema');
            if (schemaProp && schemaProp.getKind() === SyntaxKind.PropertyAssignment) {
              const schemaPA = schemaProp as PropertyAssignment;
              const schemaValue = schemaPA.getInitializer();
              if (schemaValue) {
                // console.log('key:', key, 'value:', schemaValue.getText());
                result.push({
                  key,
                  value: schemaValue.getText(),
                });
              }
            }
          }
        }
      }
    } else {
      console.warn(
        'SchemaMapEntries initializer is not an object literal. Kind:',
        initializer && initializer.getKindName(),
      );
    }
  } else {
    console.warn('SchemaMapEntries variable not found in', schemaMapPath);
  }
  return result;
}
