import * as fs from 'fs';
import * as path from 'path';

export function walkDir(dir: string, cb: (file: string) => void, excludeDirs: string[] = []) {
  for (const entry of fs.readdirSync(dir, { withFileTypes: true })) {
    const fullPath = path.join(dir, entry.name);
    if (entry.isDirectory()) {
      if (excludeDirs.some((ex: string) => fullPath.startsWith(ex))) continue;
      walkDir(fullPath, cb, excludeDirs);
    } else if (entry.isFile() && fullPath.endsWith('.ts')) cb(fullPath);
  }
}

export function resolveImportPath(modulePath: string, fromFile: string): string {
  if (modulePath.startsWith('.')) {
    let resolved = path.resolve(path.dirname(fromFile), modulePath);
    if (!resolved.endsWith('.ts')) resolved += '.ts';
    return resolved;
  }
  // For node_modules or aliases, skip for now
  return '';
}
