// helpers/headerUtils.ts
// Utility for generating auto-generated file headers for proto output

export function getHeader(meta: any, date: string, sourcePath: string, sourceSchemaName: string) {
  return [
    '// -----------------------------------------------------------------------------',
    '// THIS FILE WAS AUTO-GENERATED. DO NOT EDIT DIRECTLY.',
    '//',
    `// Source file: ${sourcePath}`,
    `// Source Schema: ${sourceSchemaName}`,
    `// Generator: ${meta.generator || ''}`,
    `// Generation Date: ${date}`,
    `// Code Version: ${meta.codeVersion || ''}`,
    `// Maintainer: ${meta.maintainer || ''}`,
    `// Team Email: ${meta.teamEmail || ''}`,
    '//',
    '// Any manual changes will be overwritten the next time the file is generated.',
    '// For questions, contact the maintainer or team email above.',
    '// -----------------------------------------------------------------------------',
    '',
  ].join('\n');
}
