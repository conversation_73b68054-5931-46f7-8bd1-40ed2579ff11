import { ProtoRegistry } from '../types';

export enum ProtoLineType {
  MESSAGE_FIELD = 'field',
  SERVICE_METHOD = 'method',
  MESSAGE_START = 'message_start',
  SERVICE_START = 'service_start',
  CLOSING_BRACE = 'closing_brace',
  ONEOF_START = 'oneof_start',
  ONEOF_FIELD = 'oneof_field',
  ONEOF_END = 'oneof_end',
  ENUM_START = 'enum_start',
  ENUM_VALUE = 'enum_value',
  ENUM_END = 'enum_end',
}

// Helper to create consistent spacing
const sp = (count: number) => ' '.repeat(count);

export function protoIndent(lineType: ProtoLineType, content: string): string {
  const indentMap = {
    [ProtoLineType.MESSAGE_FIELD]: sp(2), // 2 spaces for message fields
    [ProtoLineType.SERVICE_METHOD]: sp(2), // 2 spaces for service methods
    [ProtoLineType.MESSAGE_START]: sp(0), // No indent for message declaration
    [ProtoLineType.SERVICE_START]: sp(0), // No indent for service declaration
    [ProtoLineType.CLOSING_BRACE]: sp(0), // No indent for closing braces
    [ProtoLineType.ONEOF_START]: sp(2), // 2 spaces for oneof declaration
    [ProtoLineType.ONEOF_FIELD]: sp(4), // 4 spaces for oneof fields
    [ProtoLineType.ONEOF_END]: sp(2), // 2 spaces for oneof closing brace
    [ProtoLineType.ENUM_START]: sp(0), // No indent for enum declaration
    [ProtoLineType.ENUM_VALUE]: sp(2), // 2 spaces for enum values
    [ProtoLineType.ENUM_END]: sp(0), // No indent for enum closing brace
  };

  return `${indentMap[lineType]}${content}`;
}

interface FieldEntry {
  definition: string;
  enabled: boolean;
  source?: 'base' | 'extended' | 'inherited';
  reason?: string; // why disabled (e.g., "omitted", "not picked")
}

export class ProtoMessageBuilder {
  private lines: string[] = [];
  private fields: FieldEntry[] = []; // Change from string[] to FieldEntry[]
  private content: string = '';
  private endContent: string = '';

  startMessage(name: string): this {
    this.lines.push(`message ${name} {`);
    return this;
  }

  addField(fieldDefinition: string, enabled: boolean = true, source?: string): this {
    this.fields.push({
      definition: fieldDefinition,
      enabled,
      source: source as any,
      reason: enabled ? undefined : 'manually disabled',
    });
    return this;
  }

  addFieldIf(condition: boolean, field: string): this {
    if (condition) {
      this.addField(field);
    }
    return this;
  }

  addCustomLine(line: string): this {
    this.lines.push(protoIndent(ProtoLineType.MESSAGE_FIELD, line));
    return this;
  }

  startOneof(name: string): this {
    this.lines.push(protoIndent(ProtoLineType.ONEOF_START, `oneof ${name} {`));
    return this;
  }

  addOneofField(field: string): this {
    const fieldWithSemicolon = field.endsWith(';') ? field : `${field};`;
    this.lines.push(protoIndent(ProtoLineType.ONEOF_FIELD, fieldWithSemicolon));
    return this;
  }

  endOneof(): this {
    this.lines.push(protoIndent(ProtoLineType.ONEOF_END, '}'));
    return this;
  }

  startEnum(name: string): this {
    this.lines.push(protoIndent(ProtoLineType.ENUM_START, `enum ${name} {`));
    return this;
  }

  addEnumValue(value: string): this {
    this.lines.push(protoIndent(ProtoLineType.ENUM_VALUE, `${value};`));
    return this;
  }

  endEnum(): this {
    this.lines.push(protoIndent(ProtoLineType.ENUM_END, '}'));
    return this;
  }

  endMessage(): this {
    this.lines.push('}');
    return this;
  }

  addBlankLine(): this {
    this.lines.push('');
    return this;
  }

  copyFieldsFromSchema(schemaName: string, protoRegistry: ProtoRegistry): this {
    // Implementation will come later - for now just add a placeholder
    console.log(`Would copy fields from ${schemaName}`);
    return this;
  }

  omitFields(fieldNames: string[]): this {
    fieldNames.forEach(fieldName => {
      const field = this.fields.find(f => f.definition.includes(` ${fieldName} =`));
      if (field) {
        field.enabled = false;
        field.reason = 'omitted';
      }
    });
    return this;
  }

  pickFields(fieldNames: string[]): this {
    this.fields.forEach(field => {
      const fieldName = this.extractFieldName(field.definition);
      if (fieldNames.includes(fieldName)) {
        field.enabled = true;
        field.reason = undefined;
      } else {
        field.enabled = false;
        field.reason = 'not picked';
      }
    });
    return this;
  }

  // Helper to extract field name from definition
  private extractFieldName(definition: string): string {
    const match = definition.match(/\s+(\w+)\s+=\s+\d+/);
    return match?.[1] ?? '';
  }

  build(includeDisabledFields: boolean = false): string {
    const result = [...this.lines];

    // Find the last closing brace (should be the message closing brace)
    const lastBraceIndex = result.lastIndexOf('}');

    if (lastBraceIndex !== -1) {
      // Insert fields before the closing brace
      const fieldsToInsert: string[] = [];

      // Add enabled fields
      const enabledFields = this.fields.filter(f => f.enabled);
      enabledFields.forEach(field => {
        const fieldLine = protoIndent(ProtoLineType.MESSAGE_FIELD, field.definition + ';');
        fieldsToInsert.push(fieldLine);
      });

      // Optionally add disabled fields as comments
      if (includeDisabledFields) {
        const disabledFields = this.fields.filter(f => !f.enabled);
        if (disabledFields.length > 0) {
          fieldsToInsert.push('');
          fieldsToInsert.push(protoIndent(ProtoLineType.MESSAGE_FIELD, '// Disabled fields:'));
          disabledFields.forEach(field => {
            const reason = field.reason ? ` (${field.reason})` : '';
            const commentLine = protoIndent(ProtoLineType.MESSAGE_FIELD, `// ${field.definition};${reason}`);
            fieldsToInsert.push(commentLine);
          });
        }
      }

      // Insert all fields before the closing brace
      result.splice(lastBraceIndex, 0, ...fieldsToInsert);
    }

    return result.join('\n');
  }
}
