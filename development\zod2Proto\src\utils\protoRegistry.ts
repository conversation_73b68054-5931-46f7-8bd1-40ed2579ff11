import * as fs from 'fs';
import * as path from 'path';
import { Project, Node, SourceFile, VariableDeclaration } from 'ts-morph';
import { ProtoRegistry, ProtoRegistryEntry } from '../types';
import { generatePackageName, generateProtoMessageValues } from '../utils/protoMessageNaming';
import { buildImportSchemaMap, SchemaImportMapEntry } from '../utils/buildSchemaFileImportMap';
import { buildProtoFileImportMap } from '../utils/buildProtoFileImportMap';
import { extractEntityName } from './namingUtils';

/**
 *
 * @param schemaFilePath should be the relative path from the inputDir
 * @param schemaName
 * @returns
 */
export function createRegistryKey(schemaFilePath: string, schemaName: string): string {
  const normalizedPath = schemaFilePath.replace(/^\/+/, '');
  return normalizedPath ? `${normalizedPath}/${schemaName}` : schemaName;
}

/**
 * Parse composite key into components
 */
function parseRegistryKey(compositeKey: string): { filePath: string; schemaName: string } {
  const lastColonIndex = compositeKey.lastIndexOf(':');
  return {
    filePath: compositeKey.substring(0, lastColonIndex),
    schemaName: compositeKey.substring(lastColonIndex + 1),
  };
}

export function getProtoRegistryEntryFromSchemaNameAndSchemaPath(
  schemaName: string,
  schemaFilePath: string,
  protoRegistry: ProtoRegistry,
) {
  const registrykey = createRegistryKey(schemaFilePath, schemaName);
  const registryEntry = protoRegistry.get(registrykey);
  return registryEntry;
}

export function getProtoRegistryKeyFromSchemaNameAndSchemaPath(
  schemaName: string,
  schemaFilePath: string,
  protoRegistry: ProtoRegistry,
) {
  const registrykey = createRegistryKey(schemaFilePath, schemaName);
  return registrykey;
}

export function getProtoRegistryEntryFromProtoNameAndProtoPath(
  protoName: string,
  protoFilePath: string,
  protoRegistry: ProtoRegistry,
) {
  const registryEntry = Array.from(protoRegistry.values()).find(
    (entry): entry is ProtoRegistryEntry =>
      entry !== undefined && entry.protoFile === protoFilePath && entry.protoMessageName === protoName,
  );
  return registryEntry;
}

export function getProtoRegistryKeyFromProtoNameAndProtoPath(
  protoName: string,
  protoFilePath: string,
  protoRegistry: ProtoRegistry,
): string | null {
  for (const [key, entry] of protoRegistry.entries()) {
    if (entry !== undefined && entry.protoFile === protoFilePath && entry.protoMessageName === protoName) {
      return key;
    }
  }
  return null;
}

export function getProtoRegistryEntryfromProtoNameAndSchemaPath(
  protoName: string,
  schemaFilePath: string,
  protoRegistry: ProtoRegistry,
) {
  const registryEntry = Array.from(protoRegistry.values()).find(
    (entry): entry is ProtoRegistryEntry =>
      entry !== undefined &&
      entry.schemaRelativePath === schemaFilePath &&
      entry.protoMessageName === protoName,
  );
  return registryEntry;
}

export function getProtoRegistryKeyfromProtoNameAndSchemaPath(
  protoName: string,
  schemaFilePath: string,
  protoRegistry: ProtoRegistry,
): string | null {
  for (const [key, entry] of protoRegistry.entries()) {
    if (
      entry !== undefined &&
      entry.schemaRelativePath === schemaFilePath &&
      entry.protoMessageName === protoName
    ) {
      return key;
    }
  }
  return null;
}

// Enhanced registration function using path-based extraction
export function registerProtoMessage(
  sourceFile: SourceFile,
  schemaVar: VariableDeclaration,
  filePath: string,
  inputDir: string,
  protoRegistry: ProtoRegistry,
  schemaImportMap: SchemaImportMapEntry[],
  yamlConfig: any,
): void {
  const schemaName = schemaVar.getName();
  // Extract domain/entity/category from file path
  const pathInfo = parseSchemaPath(filePath, inputDir);
  const protoMessageName = extractEntityName(schemaName);
  // Generate proto file path that matches where files will be generated
  const protoFile = path.normalize(`/types/${pathInfo.schemaRelativePath}/${protoMessageName}.proto`);
  const defaultPackageName = yamlConfig.paths?.protoPackageDefault || 'z2p';
  let protoPackageName = pathInfo.schemaRelativePath.replace(/\//g, '.').replace(/^types\./, '');
  if (protoPackageName.split('.').length === 1) {
    protoPackageName =
      protoPackageName === '' ? defaultPackageName : `${defaultPackageName}.${protoPackageName}`;
  }
  // Create enhanced registry entry with composite key from schema path and name
  const registryKey = createRegistryKey(pathInfo.schemaRelativePath, schemaName);

  protoRegistry.set(registryKey, {
    protoFile: protoFile,
    protoMessageName: protoMessageName,
    protoPackageName: protoPackageName,
    definedIn: filePath,
    schemaName: schemaName,
    schemaRelativePath: pathInfo.schemaRelativePath,
    schemaImports: schemaImportMap, // Add importMap to registry entry
    domain: '',
    entity: '',
    category: '',
  });
}

// Helper function to extract domain, entity, and category from file path
function parseSchemaPath(
  filePath: string,
  inputDir: string,
): {
  domain: string;
  entity: string;
  category: string;
  schemaRelativePath: string;
} {
  // Get relative path from inputDir: "inventory/commandSchemas/product.ts"

  const schemaRelativePath = path.relative(inputDir, path.dirname(filePath)).replace(/\\/g, '/');

  // Remove src/ prefix if present: "inventory/commandSchemas/product.ts"
  const cleanPath = schemaRelativePath.replace(/^src\//, '');

  // Split into parts: ["inventory", "commandSchemas", "product.ts"]
  const parts = cleanPath.split('/');

  if (parts.length >= 3) {
    const domain = parts[0]; // Add type guard
    const categoryDir = parts[1]; // Add type guard
    const lastPart = parts[parts.length - 1]; // Add type guard

    // Type guards to ensure values exist
    if (!domain || !categoryDir || !lastPart) {
      return fallbackReturn(filePath, cleanPath);
    }

    const filename = path.basename(lastPart, '.ts'); // "product"

    // Map directory names to categories (now categoryDir is guaranteed to be string)
    let category = '';
    if (categoryDir.includes('command')) category = 'command';
    else if (categoryDir.includes('transaction')) category = 'transaction';
    else if (categoryDir.includes('query')) category = 'query';
    else if (categoryDir.includes('structure')) category = 'structure';
    else category = categoryDir; // fallback to directory name

    return {
      domain,
      entity: filename,
      category,
      schemaRelativePath: cleanPath.replace('.ts', ''),
    };
  }

  // Fallback for shorter paths
  return fallbackReturn(filePath, cleanPath);
}

// Helper function for fallback return
function fallbackReturn(filePath: string, cleanPath: string) {
  const filename = path.basename(filePath, '.ts');
  const parts = cleanPath.split('/');
  return {
    domain: parts[0] || '',
    entity: filename,
    category: '',
    schemaRelativePath: cleanPath.replace('.ts', ''),
  };
}
