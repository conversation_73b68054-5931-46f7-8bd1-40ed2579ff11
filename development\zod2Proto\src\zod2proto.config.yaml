# Path configuration
paths:
  # Directory containing your Z  # NOW TESTING WITH FULL SCHEMAS
  inputDir: 'libraries/zod-database-schemas/src/testSchemas'
  # inputDir: 'libraries/zod-database-schemas/src'

  # Where generated .proto files will be written will be
  # protoRoot/types
  # protoRoot/service
  # import references for the proto files will be prefixed with the protoImportRefRoot
  # protoRoot should include a folder named __generated__ for gitignore
  protoRoot: 'development/zod2proto/__generated__/z2p/zod-database-schemas'
  # protoImportRefRoot -- if the buf.gen.yaml files is src/proto
  # then protoImportRefRoot should be the first subdirectory desired in src/proto
  protoImportRefRoot: 'z2p/zod-database-schemas'

  protoDomainDefault: 'z2p'

  # If true, expands preprocessObjectId32TYPED as a oneof in proto
  expand: true

  # Additional directories to scan for proto dependencies
  extraProtoDirs:
    # - 'libraries/zod-database-schemas/src/common'
    # - 'libraries/zod-database-schemas/src/shared'

  # Directories to skip during scanning
  excludeDirs:
    - 'libraries/zod-database-schemas/src/__tests__'
    - 'node_modules'
    - '.git'
    - 'dist'
    - 'build'

# proto field.type replacements
protoFieldTypeReplacements:
  preprocessObjectId: 'string'
  preprocessObjectId32: 'string'
  preprocessObjectId32TYPED: 'string'
  preprocessDate: 'google.protobuf.Timestamp'
  date: 'google.protobuf.Timestamp'
  oid: 'string'

# Metadata for generated files
meta:
  generator: 'zod2Proto'
  codeVersion: '1.0.0'
  maintainer: 'Jane Doe <<EMAIL>>'
  teamEmail: '<EMAIL>'

# Debug configuration
debug:
  # Debug level controls console output and debug file generation
  # 0 = silent (no debug output)
  # 1 = minimal (errors and warnings only)
  # 2 = normal (basic progress and key operations)
  # 3 = verbose (detailed processing info)
  # 4 = full (complete debug logs and OpFlow files)
  level: 1

  # Generate OpFlow debug files (.opflow.json)
  generateOpFlowFiles: true

  # Include processing logs in OpFlow debug files
  includeProcessingLogs: true

# Enhanced naming configuration
naming:
  # File naming patterns - controls where proto files are generated
  file_patterns:
    # Mirror the source directory structure exactly
    # Pattern: types/{sourceSubdir}/{schemaName}.proto
    default: 'types/{sourceSubdir}/{schemaName|snake_case}.proto'

    # Override specific schemas (for shared/nested types without database context)
    overrides:
      # Example: Shared/nested schemas go to shared folder
      'ContactSchema':
        pattern: 'types/shared/{schemaName|snake_case}.proto'

      'AddressSchema':
        pattern: 'types/shared/{schemaName|snake_case}.proto'

      'ScheduleDatesSchema':
        pattern: 'types/shared/{schemaName|snake_case}.proto'

      # Example: Custom filename override
      'SpecialSchema':
        pattern: 'types/shared/custom_special.proto'

      # Example: Change category to enable CRUD generation
      'InventoryDbProductSchema':
        category: 'entities' # This will enable CRUD generation

  # Message naming patterns - controls the actual message names inside proto files
  # Following Google protobuf style guide: TitleCase for messages, domain prefixing for conflict prevention
  message_patterns:
    types:
      # Use {Domain}{Entity} pattern for conflict-free naming
      single: '{domain}{entity}' # InventoryProduct, WorkDataStructure, AuthUser
    commands:
      request: '{domain}{action}{entity}Request'
      response: '{domain}{action}{entity}Response'
    queries:
      request: '{domain}{action}{entity}Request'
      response: '{domain}{action}{entity}Response'
    services:
      single: '{domain}{entity}Service'
    # Patterns for composed types (union, intersection, etc.)
    composed:
      union: '{domain}{entity}Union'
      intersection: '{domain}{entity}Intersection'
      discriminatedUnion: '{domain}{entity}DiscriminatedUnion'
      array: '{domain}{entity}Array'

  # Package naming following protobuf best practices
  package_patterns:
    # Use dot-delimited lowercase without organization prefix
    default: '{domain|lowercase}.v{version}' # inventory.v1, workdata.v1, auth.v1

  # Default version
  version: '1'

  # Category determination rules (applied in order)
  # Category rules determine schema classification for documentation/organization
  # Since all schemas go to shared/types, categories are used for:
  # 1. CRUD generation decisions
  # 2. Service organization
  # 3. Documentation purposes
  category_rules:
    # Commands (CQS Write operations) - eligible for CRUD generation
    - pattern: '*Command*|*Create*|*Update*|*Delete*|*Upsert*|*Insert*|*Remove*'
      category: 'commands'

    # Queries (CQS Read operations) - eligible for CRUD generation
    - pattern: '*Query*|*Get*|*List*|*Search*|*Find*|*Fetch*|*Read*'
      category: 'queries'

    # Events (Domain events) - not eligible for CRUD
    - pattern: '*Event*|*Changed*|*Created*|*Updated*|*Deleted*|*Removed*'
      category: 'events'

    # Request/Response DTOs - not eligible for CRUD
    - pattern: '*Request*|*Response*|*DTO*|*Data*'
      category: 'contracts'

    # Database schemas - eligible for CRUD generation (main entities)
    - pattern: '*DbSchema|*Schema'
      category: 'entities'

    # Default fallback for nested/embedded types
    - default: 'types'

# CRUD Generation Configuration
crud:
  enabled: true

  # Generate CRUD services for these categories (entities are main database schemas)
  generate_for:
    - 'entities'

  # CRUD operations to include
  operations:
    - 'create'
    - 'get'
    - 'update'
    - 'delete'
    - 'list'

  # Service naming
  service_pattern: '{entity}Service'

  # File organization - services go in domain-specific directories under services/
  file_organization:
    commands_file: 'services/{sourceSubdir}/{collectionName|lowercase}_commands.proto'
    queries_file: 'services/{sourceSubdir}/{collectionName|lowercase}_queries.proto'

  # Additional fields for list operations
  list_options:
    pagination: true
    filtering: true
    sorting: false

  # Response metadata
  response_metadata:
    include_message: true
    include_timestamp: false
    include_version: false

  # Import paths for shared types
  import_types: true
