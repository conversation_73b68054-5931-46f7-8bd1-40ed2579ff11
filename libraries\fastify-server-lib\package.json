{"name": "@tnt/fastify-server-lib", "version": "1.0.0", "description": "A reusable library for creating Fastify-based microservices with Connect RPC support", "type": "module", "main": "./lib/index.js", "types": "./lib/index.d.ts", "exports": {".": {"import": "./lib/index.js", "types": "./lib/index.d.ts"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint .", "test": "jest ./src"}, "devDependencies": {"@jest/globals": "29.7.0", "@tnt/eslint-config": "workspace:*", "@tnt/typescript-config": "workspace:*", "@types/jest": "29.5.14", "@types/node": "22.8.7", "jest": "29.7.0", "ts-jest": "29.3.2", "typescript": "5.6.3"}, "dependencies": {"@connectrpc/connect": "^2.0.2", "@connectrpc/connect-fastify": "^2.0.2", "@connectrpc/connect-node": "^2.0.2", "@connectrpc/connect-web": "^2.0.0", "@fastify/cors": "8.5.0", "@fastify/helmet": "13.0.1", "@fastify/rate-limit": "10.3.0", "@tnt/env-loader": "workspace:*", "@tnt/error-logger": "workspace:*", "fastify": "^4.28.1", "zod": "^3.25.74"}, "peerDependencies": {"@tnt/mongo-client": "workspace:*", "@tnt/protos-gen": "workspace:*", "@tnt/zod-client-schemas": "workspace:*"}, "files": ["lib/**/*", "README.md"]}