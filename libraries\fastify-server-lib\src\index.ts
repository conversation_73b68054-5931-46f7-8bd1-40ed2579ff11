// 🏭 TNT Fastify Server Library
// Enterprise-grade microservice factory for Fastify + Connect RPC
//
// Quick Start:
// ```typescript
// import { createMicroservice, createServerConfig } from '@tnt/fastify-server-lib';
//
// const server = createMicroservice({
//   config: createServerConfig('my-service', { port: 8080 }),
//   // Add your routes, handlers, etc.
// });
//
// await server.start();
// ```
//
// 📚 Documentation:
// - Creation Guide: ./MICROSERVICE_CREATION_GUIDE.md
// - Directory Standards: ./DIRECTORY_STRUCTURE_STANDARDS.md
// - Template: ./MICROSERVICE_TEMPLATE.md

// Main server factory
export { createMicroservice } from './server.js';

// Types
export type {
  ServerConfig,
  AdvancedServerConfig,
  CorsConfig,
  RouteHandler,
  HttpRoute,
  ServerOptions,
  MicroserviceServer,
} from './types.js';

export type { RouteDefinition, RouteRegistryEntry } from './types-route.js';

// Utilities
export {
  createServerConfig,
  createAuthServerConfig,
  createHealthCheckHandler,
  createInfoHandler,
  setupGracefulShutdown,
} from './utils.js';

// Environment helpers
export { validateEnvironment, getEnvVar } from './env.js';

// Interceptors
export { createLoggingInterceptor, createAuthInterceptor, createMetricsInterceptor } from './interceptors.js';

// Standard routes
export { createAuthRoutes, combineRoutes, createMonitoringRoutes } from './standard-routes.js';
export type { LoginHandler, AuthRoutesOptions } from './standard-routes.js';

// 🆕 Advanced features
// Circuit breaker
export { CircuitBreaker, createCircuitBreakerInterceptor } from './circuit-breaker.js';

// Security
export {
  setupSecurity,
  createApiKeyInterceptor,
  createSignatureInterceptor,
  createSanitizationInterceptor,
} from './security.js';
export type { SecurityConfig } from './security.js';

// Middleware & Rate limiting
export { setupRateLimit, createRateLimitInterceptor, createValidationInterceptor } from './middleware.js';
export type { RateLimitConfig, ValidationConfig } from './middleware.js';

// Observability & Tracing
export {
  createTracingInterceptor,
  createAdvancedHealthCheck,
  createAdvancedMetricsInterceptor,
} from './observability.js';
export type { TracingConfig, TraceContext, HealthStatus, HealthCheck } from './observability.js';

// Configuration management
export {
  ConfigManager,
  createConfigurableServerConfig,
  defaultConfigSchema,
  configValidators,
} from './config.js';
export type { ConfigSchema, FeatureFlags } from './config.js';

// Service discovery
export {
  InMemoryServiceRegistry,
  LoadBalancer,
  ServiceDiscoveryClient,
  createServiceRegistration,
} from './service-discovery.js';
export type { ServiceInstance, ServiceRegistry, LoadBalancingStrategy } from './service-discovery';

// 🆕 Advanced features - Extended
// Caching
export {
  CacheManager,
  MemoryCache,
  RedisCache,
  createCachePlugin,
  createCacheInterceptor,
  createCacheKey,
} from './caching.js';
export type { CacheConfig, CacheInterface } from './caching.js';

// Async processing & Events
export {
  JobQueue,
  EventBus,
  AsyncProcessor,
  createAsyncProcessorInterceptor,
  createScheduledJob,
} from './async-processing.js';
export type { JobConfig, Job, JobProcessor, EventData, EventHandler } from './async-processing.js';

// Monitoring & Alerting
export {
  MonitoringSystem,
  MetricsCollector,
  AlertManager,
  AlertSeverity,
  createCommonAlerts,
  createMonitoringInterceptor,
} from './monitoring.js';
export type { AlertConfig, Alert, MetricPoint, TimeSeries, AlertHandler } from './monitoring.js';

// Data Validation & Transformation
export {
  DataValidator,
  transforms,
  rules,
  createDataValidationInterceptor,
  createCommonSchemas,
} from './validation.js';
export type {
  ValidationRule,
  FieldSchema,
  Schema,
  ValidationError,
  ValidationResult,
  DataTransformer,
} from './validation.js';
