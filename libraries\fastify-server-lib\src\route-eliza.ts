import type { ConnectRouter } from '@connectrpc/connect';
import { ElizaService } from '@tnt/protos-gen/src/__generated__/tnt/eliza_service/v1/eliza_service_pb';

// default export function;
// use like this:
// import registerElizaService from './eliza.routes';
// registerElizaService(router);
export default (router: ConnectRouter) => {
  console.log('[DEBUG] elizaRoute registering service');
  router.service(ElizaService, {
    async say(req) {
      return {
        sentence: `You said: ${req.sentence}`,
      };
    },
  });
};

// named function equivalent of above
// export function fnElizaService(router: ConnectRouter): void {
//   router.service(ElizaService, {
//     async say(req) {
//       return {
//         sentence: `You said: ${req.sentence}`,
//       };
//     },
//   });
// }

// to use this factory function, you must first call the factory, then pass the result to the router
// const registerElizaRoutes = createElizaRoutes();
// registerElizaRoutes(router);
// export function createElizaRoutes() {
//   return (router: ConnectRouter): void => {
//     router.service(ElizaService, {
//       // implements rpc Say
//       async say(req) {
//         return {
//           sentence: `You said: ${req.sentence}`,
//         };
//       },
//     });
//   };
// }
