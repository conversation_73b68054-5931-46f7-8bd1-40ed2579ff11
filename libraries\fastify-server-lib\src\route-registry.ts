// server-lib/route-registry.ts
import { ConnectRouter } from '@connectrpc/connect';
import { RouteDefinition, RouteRegistryEntry } from './types-route';
import { ServiceImpl } from '@connectrpc/connect';

export class ConnectRpcRouteRegistry {
  private registry = new Map<string, RouteRegistryEntry>();
  private serviceGroups = new Map<string, Map<string, RouteDefinition>>();

  addRoute(route: RouteDefinition): void {
    const routeKey = `${route.serviceName}.${route.methodName}`;

    // Add to main registry
    this.registry.set(routeKey, {
      route: routeKey,
      serviceName: route.serviceName,
      methodName: route.methodName,
      handler: route.handler,
      metadata: route.metadata,
      registeredAt: new Date(),
    });

    // Group by service for ConnectRPC registration
    if (!this.serviceGroups.has(route.serviceName)) {
      this.serviceGroups.set(route.serviceName, new Map());
    }
    this.serviceGroups.get(route.serviceName)!.set(route.methodName, route);
  }

  getRoutes(): RouteRegistryEntry[] {
    return Array.from(this.registry.values());
  }

  getServiceRoutes(serviceName: string): RouteRegistryEntry[] {
    return this.getRoutes().filter(route => route.serviceName === serviceName);
  }

  getRoute(serviceName: string, methodName: string): RouteRegistryEntry | undefined {
    return this.registry.get(`${serviceName}.${methodName}`);
  }

  buildRouteHandlers(): Array<(router: ConnectRouter) => void> {
    const handlers: Array<(router: ConnectRouter) => void> = [];
    const registeredServices = new Set<string>(); // Track registered services

    for (const [serviceName, methods] of this.serviceGroups) {
      // Skip if service already registered
      if (registeredServices.has(serviceName)) {
        continue;
      }

      // Get the service type from the first method
      const firstMethod = methods.values().next().value;
      if (!firstMethod) continue;
      const serviceType = firstMethod.serviceType;
      serviceType.type = serviceName;

      // Build the service implementation with ONLY the designated methods
      const serviceImpl: Partial<ServiceImpl<any>> = {};
      for (const [methodName, routeDef] of methods) {
        serviceImpl[methodName] = routeDef.handler;
      }

      // Create the route handler function
      const routeHandler = (router: ConnectRouter) => {
        console.log('[DEBUG] buildRouteHandlers Registering service:', serviceType.proto.typeName);
        for (const method of serviceType.methods) {
          console.log(`  -> Method: ${method.name}`);
        }
        router.service(serviceType, serviceImpl);
      };

      handlers.push(routeHandler);
      registeredServices.add(serviceName);
    }

    return handlers;
  }

  // Utility methods for introspection
  getServiceNames(): string[] {
    return Array.from(this.serviceGroups.keys());
  }

  getMethodNames(serviceName: string): string[] {
    const service = this.serviceGroups.get(serviceName);
    return service ? Array.from(service.keys()) : [];
  }

  // Export registry data for external tools
  exportRegistry(): {
    routes: RouteRegistryEntry[];
    services: Record<string, string[]>;
    metadata: {
      totalRoutes: number;
      totalServices: number;
      registeredAt: Date;
    };
  } {
    return {
      routes: this.getRoutes(),
      services: Object.fromEntries(this.getServiceNames().map(name => [name, this.getMethodNames(name)])),
      metadata: {
        totalRoutes: this.registry.size,
        totalServices: this.serviceGroups.size,
        registeredAt: new Date(),
      },
    };
  }
}
