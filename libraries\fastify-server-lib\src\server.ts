import { dirname, join } from 'path';
import { fileURLToPath } from 'url';
import { readFileSync } from 'fs';
import { inspect } from 'util';

import fastifyCors from '@fastify/cors';
import { ConnectRouter, cors } from '@connectrpc/connect';
import { fastify } from 'fastify';
import { fastifyConnectPlugin } from '@connectrpc/connect-fastify';
import { Logger } from '@tnt/error-logger';

import { getEnvVar, validateEnvironment } from './env';
import { createLoggingInterceptor } from './interceptors';

import type { CorsConfig, MicroserviceServer, ServerOptions } from './types';
import { ConnectRpcRouteRegistry } from './route-registry';
import { RouteRegistryEntry } from './types-route';
import elizaRoute from './route-eliza';

/**
 * Creates a new microservice server with the provided configuration
 */
export function createMicroservice(options: ServerOptions): MicroserviceServer {
  const {
    config,
    routes,
    rpcRouteDefinitions,
    httpRoutes = [],
    interceptors = [],
    cors: corsConfig,
    plugins = [],
    beforeStart,
    afterStart,
  } = options;

  // Validate environment variables
  validateEnvironment(config);

  // Create Fastify instance
  // const server = fastify({
  // logger: config.loggerConfig ?? config.enableLogging ?? true,
  // });
  const server = fastify({
    logger: false,
  });

  // Create logger for the service
  const logger = new Logger({ serviceName: config.serviceName });

  /**
   * Get the package name from the nearest package.json
   */
  function getPackageName(): string {
    try {
      // Try to find package.json starting from current working directory
      const currentDir = process.cwd();
      let packageJsonPath = join(currentDir, 'package.json');

      try {
        const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'));
        return packageJson.name || config.serviceName;
      } catch {
        // If not found in cwd, try relative to this file
        const __filename = fileURLToPath(import.meta.url);
        const __dirname = dirname(__filename);
        packageJsonPath = join(__dirname, '..', 'package.json');
        const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'));
        return packageJson.name || config.serviceName;
      }
    } catch {
      return config.serviceName;
    }
  }

  /**
   * Sets up CORS configuration
   */
  function setupCors(corsOptions?: CorsConfig) {
    const defaultCors: CorsConfig = {
      origin: '*',
      credentials: true,
      allowedHeaders: [...cors.allowedHeaders, 'Authorization'],
      exposedHeaders: [...cors.exposedHeaders, 'Authorization'],
      maxAge: 2 * 60 * 60, // 2 hours
    };

    const finalCorsConfig = { ...defaultCors, ...corsOptions };

    return server.register(fastifyCors, {
      origin: finalCorsConfig.origin,
      credentials: finalCorsConfig.credentials,
      methods: [...cors.allowedMethods],
      allowedHeaders: finalCorsConfig.allowedHeaders,
      exposedHeaders: finalCorsConfig.exposedHeaders,
      maxAge: finalCorsConfig.maxAge,
      optionsSuccessStatus: 200,
    });
  }

  /**
   * Registers Connect RPC routes
   */
  async function setupConnectRoutes() {
    // ---------------- START OF ORIGINAL CODE ----------------
    if (!routes) return;

    const allInterceptors = [
      ...(config.enableLogging !== false ? [createLoggingInterceptor(config.serviceName)] : []),
      ...interceptors,
    ];

    //
    await server.register(fastifyConnectPlugin, {
      routes,
      interceptors: allInterceptors,
    });
  }

  // ---------------- END OF ORIGINAL CODE ----------------

  // ---------------- START OF ROUTE REGISTRY CODE ----------------
  const registry = new ConnectRpcRouteRegistry();
  // const allInterceptors = buildInterceptors();
  // registerConnectRoutes();

  function buildInterceptors() {
    const allInterceptors = [
      ...(config.enableLogging !== false ? [createLoggingInterceptor(config.serviceName)] : []),
      ...interceptors,
    ];
    return allInterceptors;
  }

  function registerConnectRoutes() {
    if (!rpcRouteDefinitions) return;
    // Build registry from route definitions
    for (const route of rpcRouteDefinitions) {
      registry.addRoute(route);
      // if node in dev mode
      console.log(
        `[DEBUG ]Registered route: ${config.host}:${config.port}/${route.serviceName}/${route.methodName}`,
      );
    }
  }

  async function setupRegisteredConnectRoutes() {
    if (!rpcRouteDefinitions || rpcRouteDefinitions.length === 0) return;

    // Build the routes function that combines all services
    const routes = (router: ConnectRouter): void => {
      const routeHandlers = registry.buildRouteHandlers();
      routeHandlers.forEach(handler => handler(router));
    };
    console.log(`[DEBUG] buildRoutes: ${routes.toString()}`);
    // Register with fastify-connect-plugin
    await server.register(fastifyConnectPlugin, {
      routes: elizaRoute,
      // interceptors: allInterceptors,
    });
  }

  async function setupElizaRouteTest() {
    console.log('[DEBUG] setupElizaRouteTest executing');
    // Register with fastify-connect-plugin
    try {
      await server.register(fastifyConnectPlugin, {
        routes: elizaRoute,
        // interceptors: allInterceptors,
      });
      console.log('[DEBUG] fastifyConnectPlugin registered successfully');
    } catch (error) {
      logger.error('Failed to register elizaRoute', { error });
      throw error;
    }
  }

  // ---------------- END OF ROUTE REGISTRY CODE ----------------

  /**
   * Registers custom HTTP routes
   */
  function setupHttpRoutes() {
    // Map of allowed HTTP methods to Fastify instance methods
    const methodMap = {
      get: server.get.bind(server),
      post: server.post.bind(server),
      put: server.put.bind(server),
      delete: server.delete.bind(server),
      patch: server.patch.bind(server),
    } as const;

    for (const route of httpRoutes) {
      const method = route.method.toLowerCase() as keyof typeof methodMap;
      const registerRoute = methodMap[method];
      if (registerRoute) {
        registerRoute(route.path, route.options || {}, route.handler);
      } else {
        throw new Error(`Unsupported HTTP method: ${route.method}`);
      }
    }
  }

  /**
   * Registers custom plugins
   */
  async function setupPlugins() {
    for (const { plugin, options: pluginOptions } of plugins) {
      await server.register(plugin, pluginOptions);
    }
  }

  /**
   * Sets up default health check route
   */
  function setupHealthCheck() {
    server.get('/health', (_, reply) => {
      reply.type('application/json');
      reply.send({
        status: 'healthy',
        service: config.serviceName,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
      });
    });

    // Backward compatibility route
    server.get('/', (_, reply) => {
      reply.type('text/plain');
      reply.send(`${config.serviceName} is running`);
    });
  }

  // add endpoints for service registry
  // function setupServiceRegistry() {
  //   server.get('/serviceregistry', async (_, reply) => {
  //     // If you attached registry to microservice, use microservice.registry
  //     // Otherwise, use the local registry variable
  //     reply.type('application/json');
  //     reply.send({
  //       services: registry.getServices(),
  //       endpoints: registry.getEndpoints(),
  //     });
  //   });
  // }

  const microservice: MicroserviceServer = {
    fastify: server,

    async start(): Promise<string> {
      console.log('[DEBUG] Fastify options:', server.initialConfig);
      try {
        // Setup all components
        // await setupPlugins();
        // await setupCors(corsConfig);
        // await setupConnectRoutes();
        // await setupRegisteredConnectRoutes();
        await setupElizaRouteTest();
        console.log('[DEBUG] Registered fastifyConnectPlugin with elizaRoute');

        // Add a simple health check for testing
        server.get('/health', (_, reply) => {
          reply.type('application/json');
          reply.send({
            status: 'healthy',
            service: config.serviceName,
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
          });
        });

        // Add debug route to show all registered routes
        server.get('/debug/routes', (_, reply) => {
          reply.type('application/json');
          const routes = server.printRoutes();
          reply.send({
            routes: routes,
            addresses: server.addresses(),
          });
        });

        // setupHttpRoutes();
        // setupHealthCheck();

        // Call beforeStart hook if provided
        if (beforeStart) {
          //await beforeStart(server);
        }

        // Start the server
        const host = config.host ?? getEnvVar('SERVICE_HOST', 'localhost');
        const port = config.port ?? getEnvVar('SERVICE_PORT', 8080);

        await server.listen({ host, port });

        const addresses = server.addresses();
        const address = Array.isArray(addresses) ? addresses[0] : addresses;
        const addressStr = typeof address === 'string' ? address : `${host}:${port}`;

        const message = `${getPackageName()} server is listening at ${addressStr}`;
        logger.info(message);

        // Call afterStart hook if provided
        if (afterStart) {
          // await afterStart(server, addressStr);
        }

        return addressStr;
      } catch (error) {
        logger.error('Failed to start server', { error });
        throw error;
      }
    },

    async stop(): Promise<void> {
      try {
        await server.close();
        logger.info(`${config.serviceName} server stopped`);
      } catch (error) {
        logger.error('Failed to stop server gracefully', { error });
        throw error;
      }
    },

    async onRequest(request, reply, done): Promise<void> {
      try {
        console.log(`[DEBUG] Incoming request: ${request.method} ${request.url}`);
        done();
      } catch (error) {
        logger.error('Incoming request failed', { error });
        throw error;
      }
    },
    async onHook(routeOptions): Promise<void> {
      try {
        console.log('[DEBUG] Route registered:', routeOptions.method, routeOptions.url);
      } catch (error) {
        logger.error('onHook failed', { error });
        throw error;
      }
    },

    getAddresses(): string[] {
      const addresses = server.addresses();
      if (!addresses) return [];

      if (Array.isArray(addresses)) {
        return addresses.map(addr => {
          if (typeof addr === 'string') return addr;
          if (typeof addr === 'object' && addr && 'address' in addr && 'port' in addr) {
            // Node.js AddressInfo type
            return `${(addr as { address: string; port: number }).address}:${(addr as { address: string; port: number }).port}`;
          }
          return '';
        });
      } else {
        if (typeof addresses === 'string') {
          return [addresses];
        } else if (
          typeof addresses === 'object' &&
          addresses &&
          'address' in addresses &&
          'port' in addresses
        ) {
          return [
            `${(addresses as { address: string; port: number }).address}:${(addresses as { address: string; port: number }).port}`,
          ];
        }
        return [];
      }
    },
    // Public API for registry access
    getRegistry(): ConnectRpcRouteRegistry {
      return registry;
    },

    getRoutes(): RouteRegistryEntry[] {
      return registry.getRoutes();
    },

    getInterceptors(): any[] {
      return interceptors;
    },

    // Add interceptor at runtime
    addInterceptor(interceptor: any): void {
      interceptors.push(interceptor);
    },

    // Export registry for external tools (API docs, etc.)
    exportRegistryData() {
      return {
        ...registry.exportRegistry(),
        interceptors: interceptors.map(i => i.constructor.name || 'Anonymous'),
        config: {
          serviceName: config.serviceName,
          enableLogging: config.enableLogging,
          totalInterceptors: interceptors.length,
        },
      };
    },
  };
  return microservice;
}
