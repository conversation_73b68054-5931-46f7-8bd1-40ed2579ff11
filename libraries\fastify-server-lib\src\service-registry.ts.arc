import { fastifyConnectPlugin } from '@connectrpc/connect-fastify';
import type { RouteHandler } from './types';
import { ConnectRouter } from '@connectrpc/connect';

// Create a Custom Service Registry
// Create a custom registry that integrates with your service registration
export class ConnectRPCServiceRegistry {
  private services = new Map<string, any>();

  register(server: ConnectRouter, serviceDefinition: any, implementation: any, interceptors: any[] = []) {
    const serviceName = serviceDefinition.typeName;
    const methods = Object.keys(serviceDefinition.methods);

    // Store service info
    this.services.set(serviceName, {
      definition: serviceDefinition,
      implementation,
      methods,
      endpoints: methods.map(method => `POST /${serviceName}/${method}`),
      interceptors,
    });
  }

  registerAll(server: any) {
    for (const service of this.services.values()) {
      server.register(fastifyConnectPlugin, {
        routes: (router: any) => {
          router.service(service.definition, service.implementation);
        },
        interceptors: service.interceptors,
      });
    }
  }

  // register(server: any, routeFn: RouteHandler, interceptors: any[] = []) {
  //   server.register(fastifyConnectPlugin, {
  //     routes: routeFn,
  //     interceptors,
  //   });
  // }

  // // routes is a single function that 'creates' all of the routes, somehow it must be broken up
  // // and the routes registered separately?
  // registerAll(server: any, routeFn: RouteHandler, interceptors: any[] = []) {
  //   // for (const routeFn of routeFns) {
  //   //   this.register(server, routeFn, interceptors);
  //   // }
  // }

  getServices() {
    return Array.from(this.services.values());
  }

  getEndpoints() {
    return this.getServices().flatMap(service => service.endpoints);
  }

  getService(serviceName: string) {
    return this.services.get(serviceName);
  }
}

// // Usage
// const registry = new ConnectRPCServiceRegistry();

// // Register services through the registry
// await registry.register(server, TestService, implementation);

// // Get all registered services
// const allServices = registry.getServices();
// const allEndpoints = registry.getEndpoints();
