// server-lib/types.ts
export interface RouteDefinition {
  serviceName: string;
  serviceType: any; // The protobuf service class
  methodName: string;
  handler: (request: any, context: any) => Promise<any>;
  metadata?: {
    description?: string;
    tags?: string[];
    deprecated?: boolean;
    version?: string;
  };
}

export interface RouteRegistryEntry {
  route: string; // "ServiceName.methodName"
  serviceName: string;
  methodName: string;
  handler: Function;
  metadata?: RouteDefinition['metadata'];
  registeredAt: Date;
}

export interface ServerLibConfig {
  routes: RouteDefinition[];
  interceptors?: any[]; // ConnectRPC interceptors
  enableRegistry?: boolean;
  enableLogging?: boolean;
  serviceName?: string;
  registryOptions?: {
    includeMetadata?: boolean;
    logRegistration?: boolean;
  };
}
