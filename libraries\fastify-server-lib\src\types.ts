import type {
  FastifyInstance,
  FastifyPluginCallback,
  FastifyPluginOptions,
  FastifyReply,
  FastifyRequest,
} from 'fastify';
import type { ConnectRouter } from '@connectrpc/connect';
import type { Interceptor } from '@connectrpc/connect';
import type { SecurityConfig } from './security.js';
import type { HealthCheck, TracingConfig } from './observability.js';
import type { RateLimitConfig, ValidationConfig } from './middleware.js';
import type { ConfigManager } from './config.js';
import type { LoadBalancingStrategy, ServiceRegistry } from './service-discovery';
import type { CacheConfig } from './caching.js';
import type { MonitoringSystem } from './monitoring.js';
import type { AsyncProcessor } from './async-processing.js';
import type { DataValidator } from './validation.js';
import { RouteDefinition, RouteRegistryEntry } from './types-route.js';
import { ConnectRpcRouteRegistry } from './route-registry.js';

/**
 * Configuration for the Fastify server
 */
export interface ServerConfig {
  /** Service name for logging and identification */
  serviceName: string;
  /** Host to bind the server to */
  host?: string;
  /** Port to bind the server to */
  port?: number;
  /** Environment variables required by the service */
  requiredEnvVars?: string[];
  /** Optional environment variables with defaults */
  optionalEnvVars?: Record<string, string>;
  /** Enable logging (default: true) */
  enableLogging?: boolean;
  /** Custom Fastify logger configuration */
  loggerConfig?: boolean | object;
}

/**
 * CORS configuration options
 */
export interface CorsConfig {
  /** Allowed origins (default: '*') */
  origin?: string | string[] | boolean;
  /** Allow credentials (default: true) */
  credentials?: boolean;
  /** Additional allowed headers */
  allowedHeaders?: string[];
  /** Additional exposed headers */
  exposedHeaders?: string[];
  /** Cache time for preflight requests in seconds (default: 2 hours) */
  maxAge?: number;
}

/**
 * Route definition function type
 */
export type RouteHandler = (router: ConnectRouter) => void;

/**
 * Custom HTTP route definition
 */
export interface HttpRoute {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  path: string;
  handler: (request: FastifyRequest, reply: FastifyReply) => Promise<unknown> | void;
  options?: Record<string, unknown>;
}

/**
 * Server options for creating a microservice
 */
export interface ServerOptions {
  /** Server configuration */
  config: ServerConfig;
  /** Connect RPC routes */
  routes?: RouteHandler;
  /** Connect RPC routes */
  rpcRouteDefinitions?: RouteDefinition[];
  /** Custom HTTP routes */
  httpRoutes?: HttpRoute[];
  /** Connect RPC interceptors */
  interceptors?: Interceptor[];
  /** CORS configuration */
  cors?: CorsConfig;
  /** Custom Fastify plugins */
  plugins?: Array<{
    plugin: FastifyPluginCallback;
    options?: FastifyPluginOptions;
  }>;
  /** Custom setup function called before server starts */
  beforeStart?: (server: FastifyInstance) => Promise<void> | void;
  /** Custom setup function called after server starts */
  afterStart?: (server: FastifyInstance, address: string) => Promise<void> | void;
}

/**
 * Advanced server configuration with all best practices
 */
export interface AdvancedServerConfig extends ServerConfig {
  /** Security configuration */
  security?: SecurityConfig;
  /** Distributed tracing configuration */
  tracing?: TracingConfig;
  /** Rate limiting configuration */
  rateLimit?: RateLimitConfig;
  /** Request validation configuration */
  validation?: ValidationConfig;
  /** Configuration manager instance */
  configManager?: ConfigManager;
  /** Service registry for service discovery */
  serviceRegistry?: ServiceRegistry;
  /** Load balancing strategy for service calls */
  loadBalancingStrategy?: LoadBalancingStrategy;
  /** Custom health checks */
  healthChecks?: Record<string, HealthCheck>;
  /** Circuit breaker configuration */
  circuitBreaker?: {
    threshold?: number;
    timeout?: number;
    retryTimeout?: number;
  };
  /** Request timeout in milliseconds */
  requestTimeout?: number;
  /** Enable automatic service registration */
  autoRegister?: boolean;

  // 🆕 New advanced features
  /** Caching configuration */
  cache?: CacheConfig;
  /** Monitoring system instance */
  monitoring?: MonitoringSystem;
  /** Async processor for jobs and events */
  asyncProcessor?: AsyncProcessor;
  /** Data validator instance */
  dataValidator?: DataValidator;
  /** Feature flags */
  featureFlags?: Record<string, boolean>;
  /** Performance settings */
  performance?: {
    maxRequestsPerSecond?: number;
    maxConcurrentRequests?: number;
    memoryThresholdMB?: number;
    cpuThresholdPercent?: number;
  };
  /** Backup and recovery settings */
  backup?: {
    enabled?: boolean;
    intervalHours?: number;
    retentionDays?: number;
    destination?: string;
  };
}

/**
 * Server instance with additional utilities
 */
export interface MicroserviceServer {
  /** The underlying Fastify instance */
  fastify: FastifyInstance;
  /** Start the server */
  start(): Promise<string>;
  /** Stop the server */
  stop(): Promise<void>;
  /** Request handler for logging and monitoring */
  onRequest(request: FastifyRequest, reply: FastifyReply, done: (err?: Error) => void): Promise<void>;
  onHook(routeOptions: any): Promise<void>;
  /** Get server addresses */
  getAddresses(): string[];
  /** Get all registered routes */
  getRegistry(): ConnectRpcRouteRegistry;
  /** Get all registered routes */
  getRoutes(): RouteRegistryEntry[];
  /** Get all registered interceptors */
  getInterceptors(): any[];
  /** Add interceptor at runtime */
  addInterceptor(interceptor: any): void;
  /** Export registry data for external tools */
  exportRegistryData(): any;
}
