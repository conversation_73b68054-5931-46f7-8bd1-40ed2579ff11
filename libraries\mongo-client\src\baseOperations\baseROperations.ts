import { type Filter } from 'mongodb';
import { Logger } from '@tnt/error-logger';

import { baseDbOperations } from './baseDbOperations';

import type { ObjectId32, SchemaMapItemType } from '@tnt/zod-database-schemas';
import type { PipelineStage, QueryOptions } from '../types';
import type { ZodTypeAny } from 'zod/v4';

// provides basic Read operations
export class baseROperations<T extends { _id: ObjectId32 }> extends baseDbOperations<T> {
  // TODO M-1:  -- should these parse after fetching and before returning?
  // if not, defaults may not be shown to consumer for fields not in db
  // this was getAll, but then why have filter? renamed and added filter in args
  /**
   *
   * @param options MongoDb Query Options
   * @param filter MongoDb Filter<T>
   * @returns Promise<Array<T>
   */

  logger = new Logger({ serviceName: '@tnt/mongo-client' });
  async getMany(
    filter?: Filter<T>,
    options?: QueryOptions,
    smi: SchemaMapItemType<ZodTypeAny, string> = this._schemaMapItem,
  ): Promise<Array<T>> {
    const { info } = options ?? {};
    const col = await this.getCollection(smi);

    // if(filter.$in[0].value.splice(0, 8) <>'smi.referenceString') {
    //   console.log('baseROperations:getMany: referenceId mismatch');
    // }

    try {
      const result = await col
        .find(filter ?? ({} as Filter<T>), {
          projection: info,
        })
        .toArray();
      return result as Array<T>;
    } catch (err: unknown) {
      this.logger.error(`baseROperations:getMany: error`, err);
      throw new Error(`Failed to find the document by provided id: ${JSON.stringify(filter)}`);
    }
  }

  // TODO M-1:  -- should these parse after fetching and before returning?
  // if not, defaults may not be shown to consumer for fields not in db
  /**
   *
   * @param id as ObjectId32 of object to retrieve
   * @param options QueryOptions
   * @returns Promise<T>
   */
  async getById(
    id: string,
    options?: QueryOptions,
    smi: SchemaMapItemType<ZodTypeAny, string> = this._schemaMapItem,
  ): Promise<T> {
    const { info } = options ?? {};
    const col = await this.getCollection(smi);

    const result = await col
      .findOne({ _id: { $eq: `${id}` } } as Filter<T>, {
        projection: info,
      })
      .catch((err: unknown) => {
        this.logger.error(`baseROperations:getById: error`, err);
        throw new Error(`Failed to find the document by provided id: ${id}`);
      });

    return result as T;
  }

  /**
   * TODO: M-1: Integrate into inventory, ... aggregations
   * @param pipeline
   * @param smi
   * @returns
   */
  async aggregateReadOnly(
    pipeline: PipelineStage[],
    smi: SchemaMapItemType<ZodTypeAny, string> = this._schemaMapItem,
  ): Promise<any[]> {
    // Ensure pipeline is a non-empty array
    if (!Array.isArray(pipeline) || pipeline.length === 0) {
      throw new Error('Aggregation pipeline must be a non-empty array.');
    }

    const forbiddenStages = ['$out', '$merge'];
    for (const stage of pipeline) {
      if (typeof stage !== 'object' || stage === null) {
        throw new Error('Each pipeline stage must be a non-null object.');
      }
      const stageKeys = Object.keys(stage);
      if (stageKeys.length === 0) {
        throw new Error('Each pipeline stage must have at least one operator key.');
      }
      for (const stageKey of stageKeys) {
        if (forbiddenStages.includes(stageKey)) {
          throw new Error(`Forbidden aggregation stage detected: ${stageKey}`);
        }
      }
    }
    const collection = await this.getCollection(smi);
    return await collection.aggregate(pipeline).toArray();
  }
}
