import { getSmiByReferenceIdString } from '@tnt/zod-database-schemas';
import * as _ from 'lodash-es';

import { InventoryReadOperations } from '../inventoryOperations/inventoryReadOperations';
// import type { ObjectId32 } from '@tnt/zod-database-schemas';

//
// https://www.timsanteford.com/posts/why-you-should-choose-lodash-es-over-lodash/
// pnpm -F mongo-client install lodash-es
// pnpm -F mongo-client install --save-dev @types/lodash-es

// this probably needs to be an async non-static class so that it can be called
// and ran on multiple objectIds simultaneously

// Define the structure of the data as a recursive type
// eslint-disable-next-line @typescript-eslint/no-explicit-any -- mongodb types of any data, and recursive of any object
type tsObjectType = Record<string, any> | any[];

export class CrossDataOperations {
  // export class CrossDataOperations<T extends { _id: ObjectId32 }, U extends T> {
  // internal objects/methods
  private referenceIds: Map<string, Array<string>> = new Map();
  private referenceObjects: Map<string, object> = new Map();

  constructor() {}

  async expandDbObject(obj: object, depth: number): Promise<object> {
    // to accurately limit depth, the depth used should be the entered value
    // multiplied by 2, each object itself is consider another level
    // therefore each time you bore down a level it takes
    // a 2 count to get to the actual key:values
    const idArrays = this.getAllReferenceIdsInObject(obj, depth * 2);

    const _retObjs = await this.loadObjectsFromDb(idArrays);
    // the return values for this method are stored in a class instance object
    // so that those values don't have to be passed through the iteration
    // to the callback.

    const newObj = this.replaceReferencesWithObjects(obj, depth * 2);
    return newObj;
  }

  // TODO M-1:  should the recursive function be asynchronous using bullmq?
  // a web worker is ONLY applicable on browser.

  // import { Queue } from 'bullmq';
  // const myQueue = new Queue('my-queue');
  // async function longRunningFunction(data: any): Promise<any> {
  //   // Perform your time-consuming operation here
  //   return result;}

  // // Add job to the queue
  // myQueue.add('my-job', { data });

  // // Process jobs in a worker
  // myQueue.process(async (job) => {
  //   const result = await longRunningFunction(job.data);
  //   return result;
  // });

  // TODO M-1:  implement deepIterateAsync as an asynchronous loop?

  private _deepIterateAsync(
    obj: tsObjectType,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- mongodb types of any data, and recursive of any object
    fn: (value: string, key?: string) => any,
    levels: number = Infinity,
    currentLevel: number = 0,
  ): tsObjectType {
    return this.deepIterate(obj, fn, levels, currentLevel);
  }

  // https://www.geeksforgeeks.org/passing-a-function-as-a-parameter-in-javascript/
  // recursively iterate through a nested object
  // The lodash mapValues method in Lodash allows you to iterate over the values of an object
  //  and transform them using a provided function. It returns a new object with the same keys
  //  as the original, but with the transformed values.

  private deepIterate = (
    obj: tsObjectType,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- mongodb types of any data, and recursive of any object
    fn: (value: string, key?: string) => any,
    levels: number = Infinity,
    currentLevel: number = 0,
  ): tsObjectType => {
    // Return the object as is if it exceeds the recursion depth
    let newObj: tsObjectType = obj;
    if (currentLevel >= levels) return newObj;
    // use (value,key) rather than just value to allow the function
    // to use the key to determine the transformation to apply

    newObj = _.mapValues(obj, (value, key) => {
      // return _.mapValues(obj, value => {
      if (typeof value === 'string') {
        // Execute the callback for strings
        // this will overwrite the value with the return of the function call
        // if you don't want to change it, simply return what was passed
        // eslint-disable-next-line @typescript-eslint/no-unsafe-return -- mongodb types of any data, and recursive of any object
        return fn(value, key);
        // return value;
      } else if (_.isObject(value)) {
        // Recurse into objects or arrays
        return this.deepIterate(value as tsObjectType, fn, levels, currentLevel + 1);
      }
      // Return non-string, non-object values as is
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return -- mongodb types of any data, and recursive of any object
      return value;
    });
    return newObj;
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- mongodb types of any data, and recursive of any object
  private isReferenceId(data: any): boolean {
    if (typeof data === 'string' && data.length === 32 && getSmiByReferenceIdString(data)) {
      return true;
    } else {
      return false;
    }
  }

  private extractIdCallback(value: string): string {
    //   console.log(data + ': ' + typeof data);
    if (this.isReferenceId(value)) {
      // this is a reference id
      const refString = value.slice(0, 8);
      if (this.referenceIds.has(refString)) {
        this.referenceIds.get(refString)?.push(value);
      } else {
        this.referenceIds.set(refString, [value]);
      }
    }
    return value;
  }

  private replaceIdObjectCallback(value: string, key?: string): object | string {
    if (this.isReferenceId(value) && key !== '_id') {
      // this is a reference id, return object?
      const retval = this.referenceObjects.get(value);
      if (!retval) {
        return `ObjectId ${value} not found`;
        // return value;
      } else {
        return retval;
      }
    } else {
      return value;
    }
  }

  private getAllReferenceIdsInObject(obj: object, depth: number): Map<string, Array<string>> {
    // iterate through object, extracting all the refence Ids
    this.deepIterate(obj, (value: string, _key?: string) => this.extractIdCallback(value), depth);
    return this.referenceIds;
  }

  private async loadObjectsFromDb(refIds: Map<string, Array<string>>): Promise<Map<string, object>> {
    // iterate through object, extracting all referenced ids.

    // const refIds: Map<string, Array<string>> = new Map();
    const refObjs: Map<string, object> = new Map();

    for (const [key, value] of refIds) {
      // ensure that there are no duplicates
      const uniqRefIdType = [...new Set(value)];

      // get smi from schemaMap
      const keyDbId = key.toString().slice(0, 4);
      const keyColId = key.toString().slice(4, 8);

      // this is creating an error?
      console.log(keyDbId);
      console.log(keyColId);

      const smi = getSmiByReferenceIdString(key);
      if (!smi) {
        throw new Error(`Schema Map Item matching type ${key.toString().slice(0, 8)} not found.`);
      }

      // get matching values from the correct database/collection
      const filter = {
        _id: { $in: uniqRefIdType },
      };

      const col = new InventoryReadOperations();
      const retObjs = await col.getMany(filter);

      for (const obj of retObjs) {
        refObjs.set(obj._id, obj);
      }
    }
    this.referenceObjects = refObjs;
    return refObjs;
  }

  // replace refIds in object with actual objects from array.
  private replaceReferencesWithObjects(obj: object, depth: number): object {
    // verify replacement Map is not null
    if (this.referenceObjects.size <= 0) return obj;
    // just iterate through again, callback function will replace the items
    const transformedObj = this.deepIterate(
      obj,
      (value: string, key?: string) => this.replaceIdObjectCallback(value, key),
      depth,
    );
    return transformedObj;
  }
}
