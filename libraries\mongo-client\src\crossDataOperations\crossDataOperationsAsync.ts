import { getSmiByReferenceIdString } from '@tnt/zod-database-schemas';
import * as _ from 'lodash-es';
import { InventoryReadOperations } from '../inventoryOperations/inventoryReadOperations';
import { baseROperations } from '../baseOperations';

// Type for recursive object/array structures
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type tsObjectType = Record<string, any> | any[];

/**
 * Asynchronous, recursive cross-database object expander.
 * Expands reference IDs in objects by fetching and replacing them with their actual objects from the DB.
 */
export class CrossDataOperationsAsync {
  // Cache for loaded objects to avoid redundant DB reads
  private loadedObjects: Map<string, object> = new Map();

  // Fields to ignore when searching for or replacing references
  private static readonly ignoreFields = [
    '_id',
    'structureRootReference',
    'structureReference' /* add more fields here */,
    'sourceTemplate',
  ];

  constructor() {}

  /**
   * Checks if a value is a valid reference ID (32-char string with a matching schema map item).
   */
  private isReferenceId(data: any): boolean {
    return typeof data === 'string' && data.length === 32 && !!getSmiByReferenceIdString(data);
  }

  /**
   * Recursively extracts all reference IDs from an object up to a given depth.
   * Ignores reference IDs in specified fields for performance.
   * @param obj The object/array to search
   * @param depth Maximum recursion depth
   * @param currentLevel Current recursion level
   * @param found Set to accumulate found reference IDs
   */
  private extractReferenceIds(
    obj: tsObjectType,
    depth: number,
    currentLevel = 0,
    found: Set<string> = new Set(),
  ): Set<string> {
    if (currentLevel >= depth) return found;
    if (_.isArray(obj)) {
      for (const item of obj) {
        this.extractReferenceIds(item, depth, currentLevel + 1, found);
      }
    } else if (_.isObject(obj)) {
      for (const [key, value] of Object.entries(obj)) {
        if (CrossDataOperationsAsync.ignoreFields.includes(key)) continue;
        if (this.isReferenceId(value)) {
          found.add(value);
        } else if (_.isObject(value) || _.isArray(value)) {
          this.extractReferenceIds(value, depth, currentLevel + 1, found);
        }
      }
    }
    return found;
  }

  /**
   * Loads objects from the DB for all reference IDs not already loaded.
   * Fetches in parallel, grouped by reference type.
   * @param refIds Set of reference IDs to load
   */
  private async loadObjects(refIds: Set<string>): Promise<void> {
    const idsToFetch = Array.from(refIds).filter(id => !this.loadedObjects.has(id));
    if (idsToFetch.length === 0) return;
    // Group IDs by type (first 8 chars)
    const typeGroups: Map<string, string[]> = new Map();
    for (const id of idsToFetch) {
      const refString = id.slice(0, 8);
      if (!typeGroups.has(refString)) typeGroups.set(refString, []);
      typeGroups.get(refString)!.push(id);
    }
    // Fetch all groups in parallel
    await Promise.all(
      Array.from(typeGroups.entries()).map(async ([type, ids]) => {
        const smi = getSmiByReferenceIdString(type);
        if (!smi) throw new Error(`Schema Map Item matching type ${type} not found.`);
        const filter = { _id: { $in: ids } };
        const col = new baseROperations(smi);
        const retObjs = await col.getMany(filter, undefined, smi);
        for (const obj of retObjs) {
          this.loadedObjects.set(obj._id, obj);
        }
      }),
    );
  }

  /**
   * Recursively replaces reference IDs in the object with their loaded objects.
   * Only replaces up to the given depth per call.
   * @param obj The object/array to process
   * @param depth Maximum recursion depth
   * @param currentLevel Current recursion level
   */
  private replaceReferences(
    obj: tsObjectType,
    depth: number,
    currentLevel = 0,
    parentKey?: string,
  ): tsObjectType {
    if (currentLevel >= depth) return obj;
    if (_.isArray(obj)) {
      return obj.map(item => this.replaceReferences(item, depth, currentLevel + 1, parentKey));
    } else if (_.isObject(obj)) {
      return _.mapValues(obj, (value, key) => {
        if (this.isReferenceId(value) && !CrossDataOperationsAsync.ignoreFields.includes(key)) {
          const foundObj = this.loadedObjects.get(value);
          if (foundObj) {
            // console.log(
            //   `[replaceReferences] ObjectId ${value} found (key: ${key}, parent: ${parentKey ?? 'root'})`,
            // );
          } else {
            // console.log(
            //   `[replaceReferences] ObjectId ${value} NOT found (key: ${key}, parent: ${parentKey ?? 'root'})`,
            // );
          }
          return foundObj || `ObjectId ${value} not found`;
        } else if (_.isObject(value) || _.isArray(value)) {
          return this.replaceReferences(value, depth, currentLevel + 1, key);
        }
        return value;
      });
    }
    return obj;
  }

  /**
   * Recursively expands all reference IDs in the object up to the specified depth.
   * Loads all referenced objects in parallel and replaces them in the result.
   * Each pass expands one level deeper, until depth is reached or no more references are found.
   * @param obj The object/array to expand
   * @param depth Maximum expansion depth
   */
  async expandDbObject(obj: tsObjectType, depth: number): Promise<tsObjectType> {
    let currentObj = obj;
    let currentDepth = 0;
    while (currentDepth < depth) {
      // Extract reference IDs only one level deep per pass
      const refIds = this.extractReferenceIds(currentObj, 1);
      if (refIds.size === 0) break;
      await this.loadObjects(refIds);
      currentObj = this.replaceReferences(currentObj, 1);
      currentDepth++;
    }
    return currentObj;
  }
}
