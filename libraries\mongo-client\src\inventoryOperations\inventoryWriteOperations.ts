import { inspect } from 'util';
import { z } from 'zod/v4';
import { Logger } from '@tnt/error-logger';
import { getSmiByKeyString, InventoryTransactionSchema } from '@tnt/zod-database-schemas';
import { SchemaMap } from '@tnt/zod-database-schemas';

import { baseDbOperations } from '../baseOperations/baseDbOperations';

import type { InventoryTransactionResult } from './types/transactionResultType';
import type {
  InventoryDbItem,
  InventoryDbLocation,
  InventoryDbProduct,
  InventoryDbSKU,
  InventoryDbStockLocation,
  InventoryTransaction,
  InventoryTransactionItem,
  InventoryTransactionSku,
  InventoryTransactionStockLocation,
  ObjectId32,
} from '@tnt/zod-database-schemas';
import type { Collection } from 'mongodb';

// creation of the products, modifying product parameters (except quantities) should follow standard CRUD operations
// inventory transactions will modify Product and insert Transaction collections in atomic transactions

type validateInventoryTransactionReturn = {
  transaction: InventoryTransaction | undefined;
  inventoryItem: InventoryDbItem | undefined;
  inventoryProduct: InventoryDbProduct | undefined;
  inventorySku: InventoryDbSKU | undefined;
  inventoryStockLocation: InventoryDbStockLocation | undefined;
  inventoryLocation: InventoryDbLocation | undefined;
  error: Error | undefined;
};

export class InventoryWriteOperations<T extends { _id: ObjectId32 }> extends baseDbOperations<T> {
  // because the methods added are specific to this database, we need to fix
  // the SchemaMap to the proper one during the instantiation process
  constructor() {
    super(SchemaMap.Inventory_Products);
  }

  logger = new Logger({ serviceName: '@tnt/mongo-client' });

  async executeTransaction(transaction: InventoryTransaction): Promise<InventoryTransactionResult> {
    // check transaction prior to executing to ensure it is valid
    const validationReturn: validateInventoryTransactionReturn =
      this.validateInventoryTransaction(transaction);
    if (validationReturn === undefined) {
      return { success: false, error: `Transaction Error - pre-execution validation failed` };
    }

    // check and set transaction
    if (validationReturn.transaction === undefined) {
      return { success: false, error: `Transaction Error - transaction undefined` };
    }

    const tType: string = validationReturn.transaction.transactionData.transactionDataType;
    switch (true) {
      case ['Inventory.Products.Create', 'Inventory.Products.Modify'].includes(tType):
        if (validationReturn.inventoryProduct === undefined) {
          return {
            success: false,
            error: `Transaction Error! Transaction.ReferenceId: ${validationReturn.transaction.referenceId}
            Transaction Data Error - product undefined`,
          };
        }
        return this.productTransaction(validationReturn);
      // break;
      case ['Inventory.Skus.Create', 'Inventory.Skus.Modify'].includes(tType):
        if (validationReturn.inventorySku === undefined) {
          return {
            success: false,
            error: `Transaction Error! Transaction.ReferenceId: ${validationReturn.transaction.referenceId}
            Transaction Data Error - sku undefined`,
          };
        }
        return this.skuTransaction(validationReturn);
      // break;
      case ['Inventory.Locations.Create', 'Inventory.Locations.Modify'].includes(tType):
        if (validationReturn.inventoryLocation === undefined) {
          return {
            success: false,
            error: `Transaction Error! Transaction.ReferenceId: ${validationReturn.transaction.referenceId}
            Transaction Data Error - location undefined`,
          };
        }
        return this.locationTransaction(validationReturn);
      // break;
      case ['Inventory.StockLocations.Create', 'Inventory.StockLocations.Modify'].includes(tType):
        if (validationReturn.inventoryStockLocation === undefined) {
          return {
            success: false,
            error: `Transaction Error! Transaction.ReferenceId: ${validationReturn.transaction.referenceId}
            Transaction Data Error - stockLocation undefined`,
          };
        }
        return this.stockLocationTransaction(validationReturn);
      // break;
      case ['Inventory.Items.Issue', 'Inventory.Items.Adjust', 'Inventory.Items.Transfer'].includes(tType):
        if (validationReturn.inventoryItem === undefined) {
          return {
            success: false,
            error: `Transaction Error! Transaction.ReferenceId: ${validationReturn.transaction.referenceId}
            Transaction Data Error - item undefined`,
          };
        }
        return this.itemTransaction(validationReturn);
      // break;

      default:
        break;
    }
    return { success: false, error: 'Transaction Error - unknown error' };
  }

  // Example override method
  // public override async insert(
  //   data: WithOptionalObjectId32<T> | WithOptionalObjectId32<T>[],
  // ): Promise<T | Array<T> | z.ZodError<any>> {
  //   return [];
  // }

  // private async defaultInventoryItem(): Promise<void> {
  /* REVIEW Default Inventory Item -- used for inventory tracking of items that don't use skus or itemIds,
    these items are either a user choice or more typically items that are only used in a single structure object
    */
  // TODO M-1:  this will be used for creating a default inventory item. Modifications and deletions should be done using
  // standard methods.
  // this method should simply call each product, sku, inventory location, item create function with appropriate
  // default substituted values
  //   return;
  // }

  private async productTransaction(
    validationReturn: validateInventoryTransactionReturn,
  ): Promise<InventoryTransactionResult> {
    const vTransaction = validationReturn.transaction!;
    const vTransactionProduct = validationReturn.inventoryProduct!;
    // Ensuring product is initialized correctly, include ONLY the product data, if extraneous data exists, remove it.
    vTransactionProduct.skus = [];
    vTransactionProduct.productSum = 0;
    vTransactionProduct.active = true;
    try {
      // Ensuring data is initialized correctly, include ONLY the item data, if extraneous data exists, remove it.
      return await this.transactionalOperation<InventoryTransactionResult>(async session => {
        // initialize return value
        const sessionReturn: InventoryTransactionResult = { success: false };
        const client = await this.getClient();
        if (client === null) {
          throw new Error('inventoryOperations.transactionalOperation.addStockLocation getClient Error');
        }
        const invTransactionsDb = client.db(SchemaMap.Inventory_Transactions.dbName);
        const invProductsDb = client.db(SchemaMap.Inventory_Products.dbName);

        const invTransCollection: Collection<InventoryTransaction> = invTransactionsDb.collection(
          SchemaMap.Inventory_Transactions.collectionName,
        );
        const invProductsCollection: Collection<InventoryDbProduct> = invProductsDb.collection(
          SchemaMap.Inventory_Products.collectionName,
        );

        // update timestamp
        vTransaction.createdOn = new Date();

        // get existing product if exists
        let existingProduct: InventoryDbProduct | null = null;

        if (vTransaction.transactionData.transactionDataType === 'Inventory.Products.Create') {
          existingProduct = await invProductsCollection.findOne({
            $or: [{ productCode: vTransactionProduct.productCode }, { _id: vTransactionProduct._id }],
          });
          if (existingProduct !== null) {
            sessionReturn.success = false;
            const estring = `InventoryWriteOperations.productTransaction ${vTransaction.transactionData.transactionDataType} error`;
            sessionReturn.error = sessionReturn.error ? estring : sessionReturn.error + '/n' + estring;

            return sessionReturn;
          }
        }

        // should productModify search for existing product by productCode or
        if (vTransaction.transactionData.transactionDataType === 'Inventory.Products.Modify') {
          existingProduct = await invProductsCollection.findOne({
            _id: vTransactionProduct._id,
          });
          if (existingProduct === null || existingProduct === undefined) {
            throw new Error(
              `Transaction Error! Transaction.ReferenceId: ${vTransaction.referenceId}
              ExistingProduct not found
              Search values {product} : ${vTransactionProduct._id}
              `,
            );
          } else {
            // create updated product
          }
        }

        // Insert the inventory transaction data
        const transactionResult = await invTransCollection.insertOne(vTransaction, { session });
        if (transactionResult.acknowledged) {
          sessionReturn.transactionId = transactionResult.insertedId;
        } else {
          sessionReturn.success = false;
          sessionReturn.error = 'InventoryWriteOperations.itemTransaction invTransCollection.insertOne error';
        }

        if (vTransaction.transactionData.transactionDataType === 'Inventory.Products.Create') {
          const _insertOneResult = await invProductsCollection.insertOne(vTransactionProduct);
          if (_insertOneResult.acknowledged) {
            sessionReturn.collectionDocId = _insertOneResult.insertedId;
          } else {
            sessionReturn.success = false;
            const estring = `InventoryWriteOperations.productTransaction ${vTransaction.transactionData.transactionDataType} error`;
            sessionReturn.error = sessionReturn.error ? estring : sessionReturn.error + '/n' + estring;
          }
        } else if (vTransaction.transactionData.transactionDataType === 'Inventory.Products.Modify') {
          const filter = { _id: vTransactionProduct._id };
          const query = {
            $set: {
              ...existingProduct!,
              active: vTransactionProduct.active,
              cost: vTransactionProduct.cost,
              productName: vTransactionProduct.productName,
              productDescription: vTransactionProduct.productDescription,
            },
          };
          const _updateOneResult = await invProductsCollection.updateOne(filter, query);
          if (_updateOneResult.acknowledged) {
            sessionReturn.collectionDocId = _updateOneResult.upsertedId;
          } else {
            sessionReturn.success = false;
            const estring = `InventoryWriteOperations.productTransaction ${vTransaction.transactionData.transactionDataType} error`;
            sessionReturn.error = sessionReturn.error ? estring : sessionReturn.error + '/n' + estring;
          }
        }
        // session return
        return sessionReturn;
      });

      // finally create return object
    } catch (error: unknown) {
      if (error instanceof Error) {
        this.logger.error('Transaction error', { error });
      }
      throw error;
    }
  }

  private async skuTransaction(
    validationReturn: validateInventoryTransactionReturn,
  ): Promise<InventoryTransactionResult> {
    const vTransaction = validationReturn.transaction!;
    const vTransactionSku = validationReturn.inventorySku!;

    if (vTransaction.transactionData.transactionDataType === 'Inventory.Skus.Create') {
      // Ensuring sku is initialized correctly, include ONLY the sku data, if extraneous data exists, remove it.
      vTransactionSku.stockLocations = [];
      vTransactionSku.skuSum = 0;
      vTransactionSku.active = true;
    }

    try {
      // Ensuring data is initialized correctly, include ONLY the item data, if extraneous data exists, remove it.
      return await this.transactionalOperation<InventoryTransactionResult>(async session => {
        // initialize return value
        const sessionReturn: InventoryTransactionResult = { success: false };
        const client = await this.getClient();
        if (client === null) {
          throw new Error('inventoryOperations.transactionalOperation.addSku getClient Error');
        }
        const invTransactionsDb = client.db(SchemaMap.Inventory_Transactions.dbName);
        const invProductsDb = client.db(SchemaMap.Inventory_Products.dbName);

        const invTransCollection: Collection<InventoryTransaction> = invTransactionsDb.collection(
          SchemaMap.Inventory_Transactions.collectionName,
        );
        const invProductsCollection: Collection<InventoryDbProduct> = invProductsDb.collection(
          SchemaMap.Inventory_Products.collectionName,
        );

        // was unable to use the standard this.getConnection function?

        // update timestamp

        vTransaction.createdOn = new Date();

        // get existing product
        const existingProduct = await invProductsCollection.findOne({
          _id: (vTransaction.transactionData as InventoryTransactionSku).productId,
        });
        if (existingProduct === null || existingProduct === undefined) {
          throw new Error(
            `Transaction Error! Transaction.ReferenceId: ${vTransaction.referenceId}
              Error: ExistingProduct not found
             search values {product} : ${(vTransaction.transactionData as InventoryTransactionSku).productId}
            `,
          );
        }

        // get existing sku if exists
        let existingSku: InventoryDbSKU | null = null;
        // let skuIndex: number = -1;

        if (vTransaction.transactionData.transactionDataType === 'Inventory.Skus.Create') {
          existingSku = (existingProduct.skus ?? []).find(
            sku => sku.skuCode === vTransactionSku.skuCode,
          ) as InventoryDbSKU | null;
          if (!(existingSku === null || existingSku === undefined)) {
            sessionReturn.success = false;
            const estring = `InventoryWriteOperations.skuTransaction ${vTransaction.referenceId}
              SkuCreate Error: Sku already exists`;
            sessionReturn.error = sessionReturn.error ? estring : sessionReturn.error + '/n' + estring;
            return sessionReturn;
          }
        }

        if (vTransaction.transactionData.transactionDataType === 'Inventory.Skus.Modify') {
          existingSku = (existingProduct.skus ?? []).find(
            sku => sku._id.toString() === vTransactionSku._id.toString(),
          ) as InventoryDbSKU | null;
          // get sku index
          // skuIndex = (existingProduct.skus ?? []).findIndex(
          //   sku => sku._id.toString() === vTransactionSku._id.toString(),
          // );
          if (existingSku === null || existingSku === undefined) {
            console.error(`SkuModify Error: Sku doesn't exist`);
            throw new Error(
              `Transaction Error! Transaction.ReferenceId: ${vTransaction.referenceId}
              Error: Existing Sku not found
               search values {sku} : ${vTransactionSku._id.toString()}
              `,
            );
          }
        }

        // Insert the inventory transaction data
        const transactionResult = await invTransCollection.insertOne(vTransaction, { session });
        if (transactionResult.acknowledged) {
          sessionReturn.transactionId = transactionResult.insertedId;
        } else {
          sessionReturn.success = false;
          sessionReturn.error = 'InventoryWriteOperations.skuTransaction invTransCollection.insertOne error';
        }

        if (vTransaction.transactionData.transactionDataType === 'Inventory.Skus.Create') {
          // insert sku into Inventory Products collection
          const filter = {
            _id: (vTransaction.transactionData as InventoryTransactionSku).productId,
          };
          const update = {
            $addToSet: {
              ['skus']: {
                ...vTransactionSku,
              },
            },
          };
          const options = { upsert: false };
          const _updateProductReturnValue = await invProductsCollection.updateOne(filter, update, options);
          if (_updateProductReturnValue.acknowledged) {
            sessionReturn.collectionDocId = _updateProductReturnValue.upsertedId;
          } else {
            sessionReturn.success = false;
            const estring = `InventoryWriteOperations.skuTransaction ${vTransaction.transactionData.transactionDataType} error`;
            sessionReturn.error = sessionReturn.error ? estring : sessionReturn.error + '/n' + estring;
          }
        } else if (vTransaction.transactionData.transactionDataType === 'Inventory.Skus.Modify') {
          // update sku into Inventory Products collection
          //https://www.mongodb.com/docs/manual/reference/operator/update/positional-filtered/
          const filter = {
            '_id': (vTransaction.transactionData as InventoryTransactionSku).productId,
            'skus._id': (vTransaction.transactionData as InventoryTransactionSku)._id,
          };
          const update = {
            $set: {
              'skus.$': {
                ...existingSku,
                // _id: vTransactionSku._id,
                active: vTransactionSku.active,
                skuName: vTransactionSku.skuName,
                // skuCode: vTransactionSku.skuCode
                skuDescription: vTransactionSku.skuDescription,
                // manufacturerId: vTransactionSku.manufacturerId,
                // supplierId: vTransactionSku.supplierId,
                colorId: vTransactionSku.colorId,
                sizeId: vTransactionSku.sizeId,
                cost: vTransactionSku.cost,
                // locations: vTransactionSku.locations,
                weight: vTransactionSku.weight,
                weightUnitsId: vTransactionSku.weightUnitsId,
                dimensions: vTransactionSku.dimensions,
                dimensionsUnitsId: vTransactionSku.dimensionsUnitsId,
              },
            },
          };
          const options = { upsert: false };
          const _updateProductReturnValue = await invProductsCollection.updateOne(filter, update, options);
          if (_updateProductReturnValue.acknowledged) {
            sessionReturn.collectionDocId = _updateProductReturnValue.upsertedId;
          } else {
            sessionReturn.success = false;
            const estring = `InventoryWriteOperations.skuTransaction ${vTransaction.transactionData.transactionDataType} error`;
            sessionReturn.error = sessionReturn.error ? estring : sessionReturn.error + '/n' + estring;
          }
        }

        return sessionReturn;
      });
    } catch (error: unknown) {
      this.logger.error('Transaction error', { error });
      throw error;
    }
  }

  private async locationTransaction(
    validationReturn: validateInventoryTransactionReturn,
  ): Promise<InventoryTransactionResult> {
    const vTransaction = validationReturn.transaction!;
    const vTransactionLocation = validationReturn.inventoryLocation!;

    // Ensuring location is initialized correctly, include ONLY the location data, if extraneous data exists, remove it.
    try {
      // Ensuring data is initialized correctly, include ONLY the item data, if extraneous data exists, remove it.
      return await this.transactionalOperation<InventoryTransactionResult>(async session => {
        // initialize return value
        const sessionReturn: InventoryTransactionResult = { success: false };

        const client = await this.getClient();
        if (client === null) {
          throw new Error('inventoryOperations.transactionalOperation.addLocation getClient Error');
        }
        const invTransactionsDb = client.db(SchemaMap.Inventory_Transactions.dbName);
        const invLocationsDb = client.db(SchemaMap.Inventory_Locations.dbName);

        const invTransCollection: Collection<InventoryTransaction> = invTransactionsDb.collection(
          SchemaMap.Inventory_Transactions.collectionName,
        );
        const invLocationsCollection: Collection<InventoryDbLocation> = invLocationsDb.collection(
          SchemaMap.Inventory_Locations.collectionName,
        );

        // was unable to use the standard this.getConnection function?

        // get existing location if exists
        let existingLocation: InventoryDbLocation | null = null;

        if (vTransaction.transactionData.transactionDataType === 'Inventory.Locations.Create') {
          existingLocation = await invLocationsCollection.findOne({
            siteId: vTransactionLocation.siteId,
            warehouseId: vTransactionLocation.warehouseId,
            descriptors: vTransactionLocation.descriptors,
          });
          if (existingLocation !== null) {
            const estring = `Transaction Error! Transaction.ReferenceId: ${vTransaction.referenceId}
              LocationCreate Error: Location already exists`;
            sessionReturn.error = sessionReturn.error ? estring : sessionReturn.error + '/n' + estring;
            return sessionReturn;
          }
        }
        // update timestamp
        // eslint-disable-next-line require-atomic-updates -- no concerns
        vTransaction.createdOn = new Date();

        // Insert the inventory transaction data
        // Insert the inventory transaction data
        const transactionResult = await invTransCollection.insertOne(vTransaction, { session });
        if (transactionResult.acknowledged) {
          sessionReturn.transactionId = transactionResult.insertedId;
        } else {
          sessionReturn.success = false;
          sessionReturn.error =
            'InventoryWriteOperations.locationTransaction invTransCollection.insertOne error';
        }

        if (vTransaction.transactionData.transactionDataType === 'Inventory.Locations.Create') {
          // insert product into Inventory Products collection

          const _insertLocationReturnValue = await invLocationsCollection.insertOne(vTransactionLocation);
          if (_insertLocationReturnValue.acknowledged) {
            sessionReturn.collectionDocId = _insertLocationReturnValue.insertedId;
          } else {
            sessionReturn.success = false;
            const estring = `InventoryWriteOperations.locationTransaction ${vTransaction.transactionData.transactionDataType} error`;
            sessionReturn.error = sessionReturn.error ? estring : sessionReturn.error + '/n' + estring;
          }
        } else if (vTransaction.transactionData.transactionDataType === 'Inventory.Locations.Modify') {
          //
          const filter = { _id: vTransactionLocation._id };
          const query = {
            $set: {
              ...existingLocation!,
              active: vTransactionLocation.active,
              entityId: vTransactionLocation.entityId,
              descriptors: vTransactionLocation.descriptors,
              siteId: vTransactionLocation.siteId,
              warehouseId: vTransactionLocation.warehouseId,
            },
          };
          const _updateOneReturnValue = await invLocationsCollection.updateOne(filter, query);
          if (_updateOneReturnValue) {
            sessionReturn.collectionDocId = _updateOneReturnValue.upsertedId;
          } else {
            sessionReturn.success = false;
            const estring = `InventoryWriteOperations.locationTransaction ${vTransaction.transactionData.transactionDataType} error`;
            sessionReturn.error = sessionReturn.error ? estring : sessionReturn.error + '/n' + estring;
          }
        }

        // session return
        return sessionReturn;
      });
    } catch (error: unknown) {
      this.logger.error('Transaction error', { error });
      throw error;
    }
  }

  private async stockLocationTransaction(
    validationReturn: validateInventoryTransactionReturn,
  ): Promise<InventoryTransactionResult> {
    const vTransaction = validationReturn.transaction!;
    const vTransactionStockLocation = validationReturn.inventoryStockLocation!;

    // Ensuring data is initialized correctly, include ONLY the location stock data, if extraneous data exists, remove it.
    vTransactionStockLocation.items = [];
    vTransactionStockLocation.stockLocationSum = 0;
    vTransactionStockLocation.active = true;
    vTransactionStockLocation.scheduled = [];

    try {
      // Ensuring data is initialized correctly, include ONLY the item data, if extraneous data exists, remove it.
      return await this.transactionalOperation<InventoryTransactionResult>(async session => {
        // initialize return value
        const sessionReturn: InventoryTransactionResult = { success: false };
        const client = await this.getClient();
        if (client === null) {
          throw new Error(
            'inventoryOperations.transactionalOperation.stockLocationTransaction getClient Error',
          );
        }
        const invTransactionsDb = client.db(SchemaMap.Inventory_Transactions.dbName);
        const invProductsDb = client.db(SchemaMap.Inventory_Products.dbName);

        const invTransCollection: Collection<InventoryTransaction> = invTransactionsDb.collection(
          SchemaMap.Inventory_Transactions.collectionName,
        );
        const invProductsCollection: Collection<InventoryDbProduct> = invProductsDb.collection(
          SchemaMap.Inventory_Products.collectionName,
        );

        const inventoryTransactionStockLocation: InventoryTransactionStockLocation =
          vTransaction.transactionData as InventoryTransactionStockLocation;

        // get existing product if exists
        const existingProduct = await invProductsCollection.findOne({
          _id: inventoryTransactionStockLocation.productId,
        });
        if (existingProduct === null || existingProduct === undefined) {
          throw new Error(
            `Transaction Error! Transaction.ReferenceId: ${vTransaction.referenceId}
              Error: ExistingProduct not found
             search values {product} : ${inventoryTransactionStockLocation.productId}
            `,
          );
        }

        // get existing sku if exists
        const existingSku = existingProduct?.skus?.find(
          sku => sku._id.toString() === inventoryTransactionStockLocation.skuId?.toString(),
        );
        if (existingSku === null || existingSku === undefined) {
          throw new Error(
            `Transaction Error! Transaction.ReferenceId: ${vTransaction.referenceId}
              Error: Existing Sku not found
             search values {sku} : ${inventoryTransactionStockLocation.skuId}
            `,
          );
        }
        // get sku index
        // const skuIndex = (existingProduct.skus ?? []).findIndex(
        //   sku => sku._id.toString() === inventoryTransactionStockLocation.skuId?.toString(),
        // );

        if (existingSku.stockLocations === null || existingSku.stockLocations === undefined) {
          existingSku.stockLocations = [];
        }

        // get existing stockLocation if exists
        const existingStockLocation = existingSku.stockLocations.find(
          stockLocation => stockLocation.locationId === vTransactionStockLocation.locationId,
        );

        // let stockLocationIndex: number = -1;

        if (inventoryTransactionStockLocation.transactionDataType === 'Inventory.StockLocations.Create') {
          if (!(existingStockLocation === null || existingStockLocation === undefined)) {
            const estring = `Transaction Error! Transaction.ReferenceId: ${vTransaction.referenceId}
              StockLocationCreate Error: StockLocation already exists`;
            sessionReturn.error = sessionReturn.error ? estring : sessionReturn.error + '/n' + estring;
            return sessionReturn;
          }
        }
        if (inventoryTransactionStockLocation.transactionDataType === 'Inventory.StockLocations.Modify') {
          if (existingStockLocation === null || existingStockLocation === undefined) {
            throw new Error(
              `Transaction Error! Transaction.ReferenceId: ${vTransaction.referenceId}
              Error: Existing Stock Location not found
               search values {stock location id} : ${vTransactionStockLocation.locationId}
              `,
            );
          }
          // get stockLocation index
          // stockLocationIndex = (existingProduct.skus![skuIndex]?.stockLocations ?? []).findIndex(
          //   stockLocation => stockLocation.locationId === vTransactionStockLocation.locationId,
          // );
        }

        // update timestamp
        // eslint-disable-next-line require-atomic-updates -- no concerns
        vTransaction.createdOn = new Date();

        // Insert the inventory transaction data
        // Insert the inventory transaction data
        const transactionResult = await invTransCollection.insertOne(vTransaction, { session });
        if (transactionResult) {
          sessionReturn.transactionId = transactionResult.insertedId;
        } else {
          sessionReturn.success = false;
          sessionReturn.error = 'InventoryWriteOperations.itemTransaction invTransCollection.insertOne error';
        }

        if (inventoryTransactionStockLocation.transactionDataType === 'Inventory.StockLocations.Create') {
          // insert stockLocation into Inventory Products.sku.location collection
          const filter = {
            '_id': inventoryTransactionStockLocation.productId,
            'skus._id': inventoryTransactionStockLocation.skuId,
            // 'skus.StockLocations': {
            //   $elemMatch: {
            //     locationId: vTransactionStockLocation.locationId,
            //     match2: vTransactionStockLocation.active,
            //   }
            // }
          };

          // using $[<identifier>] and array filters
          // https://www.mongodb.com/docs/manual/reference/method/db.collection.updateOne/
          // https://www.mongodb.com/docs/manual/reference/method/db.collection.updateOne/#std-label-updateOne-arrayFilters

          const update = {
            $push: {
              // 'skus.$[sku].locations.[location]': { ...vTransactionStockLocation },
              'skus.$[skuIdentifier].stockLocations': { ...vTransactionStockLocation },
            },
          };
          const options = {
            upsert: false,
            arrayFilters: [
              {
                'skuIdentifier._id': {
                  $eq: inventoryTransactionStockLocation.skuId,
                },
              },
            ],
          };
          const _updateProductReturnValue = await invProductsCollection.updateOne(filter, update, options);
          if (_updateProductReturnValue) {
            sessionReturn.collectionDocId = _updateProductReturnValue.upsertedId;
          } else {
            sessionReturn.success = false;
            const estring = `InventoryWriteOperations.stockLocationTransaction ${vTransaction.transactionData.transactionDataType} error`;
            sessionReturn.error = sessionReturn.error ? estring : sessionReturn.error + '/n' + estring;
          }
        } else if (
          inventoryTransactionStockLocation.transactionDataType === 'Inventory.StockLocations.Modify'
        ) {
          const filter = {
            '_id': inventoryTransactionStockLocation.productId,
            'skus._id': inventoryTransactionStockLocation.skuId,
            'skus.stockLocations': {
              $elemMatch: {
                locationId: vTransactionStockLocation.locationId,
              },
            },
          };
          const update = {
            $set: {
              'skus.$[skuIdentifier].stockLocations.$[slocIdentifier].active':
                vTransactionStockLocation.active,
              // ...existingStockLocation,
              // active: vTransactionStockLocation.active,
              // threshold: vTransactionStockLocation.threshold,
              // target: vTransactionStockLocation.target,
            },
          };
          const options = {
            arrayFilters: [
              { 'skuIdentifier._id': { $eq: inventoryTransactionStockLocation.skuId } },
              { 'slocIdentifier._id': { $eq: vTransactionStockLocation.locationId } },
            ],
          };

          const _updateProductReturnValue = await invProductsCollection.updateOne(filter, update, options);
          if (_updateProductReturnValue) {
            sessionReturn.collectionDocId = _updateProductReturnValue.upsertedId;
          } else {
            sessionReturn.success = false;
            const estring = `InventoryWriteOperations.stockLocationTransaction ${vTransaction.transactionData.transactionDataType} error`;
            sessionReturn.error = sessionReturn.error ? estring : sessionReturn.error + '/n' + estring;
          }
        }
        // session return
        return sessionReturn;
      });
    } catch (error: unknown) {
      this.logger.error('Transaction error', { error });
      throw error;
    }
  }

  protected async itemTransaction(
    validationReturn: validateInventoryTransactionReturn,
  ): Promise<InventoryTransactionResult> {
    const vTransaction = validationReturn.transaction!;
    const vTransactionItem = validationReturn.inventoryItem!;
    try {
      // Ensuring data is initialized correctly, include ONLY the item data, if extraneous data exists, remove it.
      return await this.transactionalOperation<InventoryTransactionResult>(async session => {
        // initialize return value
        const sessionReturn: InventoryTransactionResult = { success: false };

        const client = await this.getClient();
        if (client === null) {
          throw new Error('inventoryOperations.transactionalOperation.addStockLocation getClient Error');
        }
        const invTransactionsDb = client.db(SchemaMap.Inventory_Transactions.dbName);
        const invProductsDb = client.db(SchemaMap.Inventory_Products.dbName);

        const invTransCollection: Collection<InventoryTransaction> = invTransactionsDb.collection(
          SchemaMap.Inventory_Transactions.collectionName,
        );
        const invProductsCollection: Collection<InventoryDbProduct> = invProductsDb.collection(
          SchemaMap.Inventory_Products.collectionName,
        );

        const inventoryTransactionItem: InventoryTransactionItem =
          vTransaction.transactionData as InventoryTransactionItem;

        // get existing product if exists
        const existingProduct = await invProductsCollection.findOne({
          _id: inventoryTransactionItem.productId,
        });
        if (existingProduct === null || existingProduct === undefined) {
          throw new Error(
            `Transaction Error! Transaction.ReferenceId: ${vTransaction.referenceId}
              Error: ExistingProduct not found
             search values {product} : ${inventoryTransactionItem.productId}
            `,
          );
        }
        // get existing sku if exists
        const existingSku = existingProduct.skus?.find(
          sku => sku._id.toString() === inventoryTransactionItem.skuId?.toString(),
        );
        if (existingSku === undefined || existingSku === null) {
          throw new Error(
            `Transaction Error! Transaction.ReferenceId: ${vTransaction.referenceId}
              Error: ExistingSku not found
             search values {product, sku} : ${inventoryTransactionItem.productId}, ${inventoryTransactionItem.skuId}
            `,
          );
        }
        if (existingSku.stockLocations === undefined || existingSku.stockLocations === null) {
          existingSku.stockLocations = [];
        }

        const originStockLocation = existingSku.stockLocations.find(
          location =>
            location._id.toString() === (inventoryTransactionItem.locationRoute?.origin ?? '').toString(),
        );

        // if transfer, locationRoute is used for both origin and destination, otherwise location is used
        const destinationStockLocationId = inventoryTransactionItem.locationRoute?.destination.toString();
        const destinationStockLocation = existingSku.stockLocations.find(
          location => location._id.toString() === destinationStockLocationId,
        );

        let updatedOriginSum: number;
        let updatedOriginItemArray: Array<InventoryDbItem>;
        let updatedDestinationSum: number;
        let updatedDestinationItemArray: Array<InventoryDbItem>;

        // Clone the sku locations array to avoid mutation
        const updatedSkuLocations: Array<InventoryDbStockLocation> = [...existingSku.stockLocations];

        // Transfer uses LocationRoute object, other types of operations only use location.

        if (inventoryTransactionItem.transactionDataType === 'Inventory.Items.Transfer') {
          if (originStockLocation === undefined || originStockLocation === null) {
            throw new Error(
              `Transaction Error! Transaction.ReferenceId: ${vTransaction.referenceId}
              Error: LocationRoute Origin not found
               search values {product, sku, stockLocation} :${inventoryTransactionItem.productId}, ${inventoryTransactionItem.locationRoute?.origin}
              `,
            );
          } else {
            // generate updated origin Item array
            // need to negate the quantity for origin
            const negatedVTransactionItem = { ...vTransactionItem };
            negatedVTransactionItem.itemSum = negatedVTransactionItem.itemSum * -1;
            updatedOriginItemArray = InventoryWriteOperations.updateStockLocationItemArray(
              originStockLocation.items || [],
              negatedVTransactionItem,
              true,
              existingSku.qty0Tol,
            );
            // generate updated location sum
            updatedOriginSum = updatedOriginItemArray.reduce((sum, item) => sum + item.itemSum, 0);

            // generate the updated location array for the sku
            //Find the index of the matching location
            const originLocationIndex = updatedSkuLocations.findIndex(
              arrayLocation => (arrayLocation._id = originStockLocation._id),
            );
            updatedSkuLocations[originLocationIndex] = {
              ...originStockLocation,
              items: updatedOriginItemArray,
              stockLocationSum: updatedOriginSum,
            };
          }
        }

        if (destinationStockLocation === undefined || destinationStockLocation === null) {
          throw new Error(
            `Transaction Error! Transaction.ReferenceId: ${vTransaction.referenceId}
              Error: Location destination/target not found
            search values {product, sku, stockLocation} :${inventoryTransactionItem.productId}, ${inventoryTransactionItem.skuId},${destinationStockLocationId}
            `,
          );
        } else {
          // generate updated origin Item array
          updatedDestinationItemArray = InventoryWriteOperations.updateStockLocationItemArray(
            destinationStockLocation.items || [],
            vTransactionItem,
            true,
            existingSku.qty0Tol,
          );
          // generate updated location sum
          updatedDestinationSum = updatedDestinationItemArray.reduce((sum, item) => sum + item.itemSum, 0);

          // generate the updated location array for the sku
          //Find the index of the matching location
          const destinationLocationIndex = updatedSkuLocations.findIndex(
            arrayLocation => arrayLocation._id === destinationStockLocation._id,
          );
          updatedSkuLocations[destinationLocationIndex] = {
            ...destinationStockLocation,
            items: updatedDestinationItemArray,
            stockLocationSum: updatedDestinationSum,
          };
        }
        // Generate updated SKU sum
        const updatedSkuSum = updatedSkuLocations.reduce(
          (sum, locations) => sum + locations.stockLocationSum,
          0,
        );

        // Update product.skus
        // Clone the product sku array to avoid mutation
        const updatedProductSkus: Array<InventoryDbSKU> = [...(existingProduct.skus ?? [])];

        //Find the index of the matching sku in products
        const updatedSkuIndex = updatedProductSkus.findIndex(
          arrayLocation => (arrayLocation._id = existingSku._id),
        );

        // update sku in product.skus array with new locations arrays and skuSum
        updatedProductSkus[updatedSkuIndex] = {
          ...existingSku,
          stockLocations: updatedSkuLocations,
          skuSum: updatedSkuSum,
        };

        // Generate updated Product sum
        const updatedProductSum = updatedProductSkus.reduce((sum, skus) => sum + skus.skuSum, 0);

        // Insert the inventory transaction data
        const transactionResult = await invTransCollection.insertOne(vTransaction, { session });
        if (transactionResult) {
          sessionReturn.transactionId = transactionResult.insertedId;
        } else {
          sessionReturn.success = false;
          sessionReturn.error = 'InventoryWriteOperations.itemTransaction invTransCollection.insertOne error';
        }
        // OPTIMIZATION (?) optionally use $[<identifier>] operator to bore down into the locations array, $set that array, then update the sums?
        // that should save writing back all of the non-affected skus.

        // Write back the updated product document
        const productUpdateOneResult = await invProductsCollection.updateOne(
          { _id: inventoryTransactionItem.productId },
          {
            $set: {
              skus: updatedProductSkus,
              productSum: updatedProductSum,
            },
          },
        );

        if (productUpdateOneResult) {
          sessionReturn.collectionDocId = productUpdateOneResult.upsertedId;
        } else {
          sessionReturn.success = false;
          const estring = `InventoryWriteOperations.itemTransaction ${vTransaction.transactionData.transactionDataType} error`;
          sessionReturn.error = sessionReturn.error ? estring : sessionReturn.error + '/n' + estring;
        }

        // session return
        return sessionReturn;
      });
    } catch (error: unknown) {
      this.logger.error('Transaction error', { error });
      throw error;
    }
  }

  /**
   *
   * @param originalItems -- original items array
   * @param itemToTransfer -- positive or negative value
   * @param acceptNegativeQuantity -- will allow the value to go below 0, errors if false and summed value < 0
   * @param qty0Tol -- removes item if projected quantity within 0 +/- qty0Tol
   * @returns -- original items array with itemToTransfer removed if quantity is 0, added if not previously there
   */
  protected static updateStockLocationItemArray(
    originalItems: InventoryDbItem[],
    itemToTransfer: InventoryDbItem,
    acceptNegativeQuantity: boolean,
    qty0Tol: number = 0.0001, // come from sku
  ): InventoryDbItem[] {
    // Helper function to match items based on _id, id1, id2, and id3
    const matchesItem = (arrayItem: InventoryDbItem, targetItem: InventoryDbItem): boolean => {
      return (
        // arrayItem._id === targetItem._id &&
        arrayItem.id1 === targetItem.id1 &&
        (!targetItem.id2 || arrayItem.id2 === targetItem.id2) &&
        (!targetItem.id3 || arrayItem.id3 === targetItem.id3)
      );
    };

    // helper in range function
    function isInRange(value: number, compare: number, tolerance: number): boolean {
      return value >= compare - tolerance && value <= compare + tolerance;
    }

    // Clone the array to avoid mutation
    const updatedItems = [...originalItems];

    // Find the index of the matching item
    const index = updatedItems.findIndex(arrayItem => matchesItem(arrayItem, itemToTransfer));

    if (index === -1) {
      // If the item is not found, add the item to the array
      if (itemToTransfer.itemSum < 0 && acceptNegativeQuantity === false) {
        throw new Error(
          `Error: Projected Quantity Negative && AcceptNegativeQuantity == false
             transfer values {product, sku} :${itemToTransfer.productId}, ${itemToTransfer.skuId}}
            `,
        );
      }
      updatedItems.push(itemToTransfer);
      return updatedItems;
    } else {
      // if item already exists in array, the new qty is sum, else it is transfer qty
      const newQuantity = updatedItems[index]!.itemSum + itemToTransfer.itemSum;
      if (newQuantity && acceptNegativeQuantity === false) {
        throw new Error(
          `Error: Projected Quantity Negative && AcceptNegativeQuantity == false
             transfer values {product, sku} :${itemToTransfer.productId}, ${itemToTransfer.skuId}}
            `,
        );
      }
      if (isInRange(newQuantity, 0, qty0Tol)) {
        // Remove the item if quantity is 0 or less (within given tolerance)
        updatedItems.splice(index, 1);
      } else {
        // Update the quantity
        updatedItems[index]!.itemSum = newQuantity;
      }
      return updatedItems;
    }
  }

  /**
   * Validates any type of Inventory Transaction
   * @param transactionIn
   * @returns if successful, will return the validated transaction as well as the
   * validated product, sku, item, stockLocation objects per type of transaction
   * non-available objects returned undefined
   */
  private validateInventoryTransaction(
    transactionIn: InventoryTransaction,
  ): validateInventoryTransactionReturn {
    // Initialize return value
    const retVal: validateInventoryTransactionReturn = {
      transaction: undefined,
      inventoryItem: undefined,
      inventoryProduct: undefined,
      inventorySku: undefined,
      inventoryStockLocation: undefined,
      inventoryLocation: undefined,
      error: undefined,
    };

    // Validate base transaction

    const transactionValidation = InventoryTransactionSchema.safeParse(transactionIn);
    if (!transactionValidation.success) {
      console.dirxml(inspect(transactionIn));
      throw new Error(
        `Transaction Error! Transaction.ReferenceId: ${transactionIn.referenceId}
        Transaction: InventoryTransaction validation failed: ${JSON.stringify(transactionValidation.error.issues)}`,
      );
    }
    retVal.transaction = transactionValidation.data;

    const keyArray = transactionValidation.data.transactionData.transactionDataType.split('.');
    if (keyArray.length < 3)
      throw new Error(`Transaction Error! Transaction.ReferenceId: ${transactionValidation.data.referenceId}
              transaction.transactionData.transactionDataType invalid`);
    const schemaMapKey = keyArray[0]!.concat('_', keyArray[1]!);

    // create retVal key
    const retValKey = 'inventory' + keyArray[1]?.slice(0, -1);

    const smi = getSmiByKeyString(schemaMapKey);
    if (!smi) {
      throw new Error(`Transaction Error! Transaction.ReferenceId: ${transactionValidation.data.referenceId}
              inventoryOperations.validateInventoryTransaction.transactionRules.smi not valid`);
    }

    const transactionDataParseResult = smi.schema.safeParse(retVal.transaction.transactionData);
    if (!transactionDataParseResult.success) {
      console.dirxml(inspect(transactionIn));
      throw new Error(
        `${transactionDataParseResult.error.message}: ${JSON.stringify(transactionDataParseResult.error.issues)}`,
      );
    }

    // Add timestamp
    retVal.transaction.createdOn = new Date();

    // use a key to dynamically determine actual object in return to place the data into
    // TypeScript doesn't allow arbitrary strings as keys on retVal unless the
    // type explicitly supports it (e.g., with an index signature like [key: string]: any).
    // Since validateInventoryTransactionReturn does not have such an index signature,
    // TypeScript will throw an error when you try to use a dynamic string as a key,
    // even if you know it's valid.

    function isValidRetValKey(key: string): key is keyof validateInventoryTransactionReturn {
      return key in retVal;
    }

    if (isValidRetValKey(retValKey) && Object.prototype.hasOwnProperty.call(retVal, retValKey)) {
      // @ts-ignore -- Safe: validated by Zod, type is context-dependent
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment -- Safe: validated by Zod, type is context-dependent
      retVal[retValKey] = transactionDataParseResult.data;
    } else {
      throw new Error(`Invalid key '${retValKey}' for validateInventoryTransactionReturn`);
    }

    return retVal;
  }
}
