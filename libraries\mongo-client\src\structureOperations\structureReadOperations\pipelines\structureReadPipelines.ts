import { SchemaMap, SchemaMapItemType } from '@tnt/zod-database-schemas';
import { z } from 'zod/v4';

// const startingStructureObjectId = 'WDATSOBJ681d59b1d2c5e833b2fc428a';
// const smiL = SchemaMap.WorkData_StructureLinks;

export const getContainedStructureObjectsPipelineFixed = [
  {
    $graphLookup: {
      from: 'StructureLinks',
      startWith: ['WDATSOBJ681d59b1d2c5e833b2fc428a'],
      connectFromField: 'target',
      connectToField: 'source',
      as: 'links',
      maxDepth: 4,
      depthField: 'depth',
    },
  },
  {
    $addFields: {
      // Get all target IDs from links
      linkTargets: {
        $map: {
          input: '$links',
          as: 'link',
          in: '$$link.target',
        },
      },
    },
  },
  {
    $addFields: {
      // Combine starting ID with all targets
      allIds: {
        $concatArrays: [
          ['WDATSOBJ681d59b1d2c5e833b2fc428a'],
          {
            $ifNull: ['$linkTargets', []],
          },
        ],
      },
    },
  },
  {
    $lookup: {
      from: 'WorkData_StructureObjects',
      localField: 'allIds',
      foreignField: '_id',
      as: 'allObjects',
    },
  },
  // Debug: Check what we actually got
  {
    $addFields: {
      foundObjectsCount: {
        $size: '$allObjects',
      },
      totalIdsToFind: {
        $size: '$allIds',
      },
      firstFewIds: {
        $slice: ['$allIds', 3],
      },
    },
  },
];

export function getContainedStructureLinksPipeline(
  smiL: SchemaMapItemType<z.ZodTypeAny, string>,
  startingStructureObjectId: string,
  depthToRecurse: number = 4,
) {
  return [
    // 1. Recursively fetch all connected links starting from the initial object _id
    {
      $graphLookup: {
        from: smiL.collectionName,
        startWith: [startingStructureObjectId],
        connectFromField: 'target',
        connectToField: 'source',
        as: 'links',
        maxDepth: 4,
        depthField: 'depth',
      },
    },
    // 2. Gather all unique IDs: starting _id + all targets from links
    {
      $addFields: {
        allIds: {
          $setUnion: [
            [startingStructureObjectId],
            {
              $map: {
                input: '$links',
                as: 'link',
                in: '$$link.target',
              },
            },
          ],
        },
      },
    },
  ];
}

export function getContainedStructureObjectsPipeline2(
  smiL: SchemaMapItemType<z.ZodTypeAny, string>,
  startingStructureObjectId: string,
  depthToRecurse: number = 4,
) {
  const testingId = 'WDATSOBJ681d59b1d2c5e833b2fc428a';
  return [
    // 1. Recursively fetch all connected links starting from the initial object _id
    {
      $graphLookup: {
        from: smiL.collectionName,
        startWith: [startingStructureObjectId],
        connectFromField: 'target',
        connectToField: 'source',
        as: 'links',
        maxDepth: 4,
        depthField: 'depth',
      },
    },
    // 2. Gather all unique IDs: starting _id + all targets from links
    {
      $addFields: {
        allIds: {
          $setUnion: [
            [startingStructureObjectId],
            {
              $map: {
                input: '$links',
                as: 'link',
                in: '$$link.target',
              },
            },
          ],
        },
      },
    },
    // 3. Lookup all objects by _id
    {
      $lookup: {
        from: 'StructureObjects',
        localField: 'allIds',
        foreignField: '_id',
        as: 'allObjects',
      },
    },
    // 4. Unwind and flatten
    {
      $unwind: '$allObjects',
    },
    {
      $replaceRoot: {
        newRoot: '$allObjects',
      },
    },
    // 5. Optionally group to remove duplicates
    {
      $group: {
        _id: '$_id',
        doc: {
          $first: '$$ROOT',
        },
      },
    },
    {
      $replaceRoot: {
        newRoot: '$doc',
      },
    },
  ];
}
