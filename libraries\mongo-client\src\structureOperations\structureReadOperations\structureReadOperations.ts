// /**
//  * Library designed to extract structures objects and links providing UI with graph data
//  */

import { inspect } from 'util';
import { getSmiByReferenceIdString, SchemaMap } from '@tnt/zod-database-schemas';

import { baseROperations } from '../../baseOperations/baseROperations';

import { z } from 'zod/v4';
import type { Filter } from 'mongodb';
import type {
  AssemblyDocument,
  AssemblyDocumentTemplate,
  BaseStructureData,
  iamLink,
  ObjectId32,
  SchemaMapItemType,
  StructureLink,
  StructureLinkTemplate,
  StructureObject,
  StructureObjectTemplate,
} from '@tnt/zod-database-schemas';
import { getContainedStructureLinksPipeline } from './pipelines/structureReadPipelines';

export class StructureReadOperations {
  /**
   *
   * @param structureObjectIds typically from the ParentS
   * @returns Object containing all of the structural objects and links in the array passed
   */
  public static async getStructuralContents<
    TObject extends StructureObject | StructureObjectTemplate,
    TLink extends StructureLink | StructureLinkTemplate,
    TAssemblyDoc extends AssemblyDocument | AssemblyDocumentTemplate,
  >(
    // structureObject: StructureObject | StructureObjectTemplate,
    startingStructureObjectId: ObjectId32,
    depthToRecurse: number = 4,
  ): Promise<BaseStructureData<TObject, TLink, TAssemblyDoc>> {
    const smiO = getSmiByReferenceIdString(startingStructureObjectId);
    if (!smiO) throw new Error('No matching schema found, check structureObjectIds prefix');
    let smiL: SchemaMapItemType<z.ZodTypeAny, string>;
    if (smiO.dbId === SchemaMap.WorkData_StructureObjects.dbId) {
      smiL = SchemaMap.WorkData_StructureLinks;
    } else {
      smiL = SchemaMap.ResourceObjects_StructureLinkTemplates;
    }
    let isRoot = false;

    // check if root or not  (references itself as containedById)
    // this will allow us to use a filter vs a graphLookup

    const myOps = new baseROperations(smiO);
    const startingStructureObject = (await myOps.getById(startingStructureObjectId)) as TObject;
    if (startingStructureObject) {
      isRoot = startingStructureObject._id === startingStructureObject.structureRootReference.containedById;
    } else {
      // return empty arrays
      return {
        objects: [] as Array<TObject>,
        links: [] as Array<TLink>,
        assemblyDocuments: [] as Array<TAssemblyDoc>,
        iamLinks: [] as Array<iamLink>,
      };
    }

    let structureObjects: Array<TObject>;
    let structureLinks: Array<TLink>;
    let structureAssemblyDocuments: Array<TAssemblyDoc>;
    let structureIamLinks: Array<iamLink>;
    let structureObjectIds: Array<ObjectId32>;

    if (isRoot && depthToRecurse === 0) {
      // this should be a much faster retrieval, but does retrieve everything
      structureObjects = (await StructureReadOperations.getAllContainedStructureObjectsFromRoot(
        startingStructureObject._id,
      )) as Array<TObject>;

      structureLinks = (await StructureReadOperations.getAllStructureLinksFromRoot(
        startingStructureObject._id,
        smiL,
      )) as Array<TLink>;

      structureObjectIds = structureLinks.map(lnk => lnk.sourceId);
    } else {
      // this is a slower retrieval, but does not retrieve everything, only to depth specified
      // we need to get the links first, then extract the objectIds, then get the objects

      // get all structure links for the structure using mongo graphLookup
      structureLinks = (await StructureReadOperations.getStructureLinks(
        startingStructureObject._id,
        smiL,
        depthToRecurse,
      )) as Array<TLink>;

      // extract all source _id's from the structureLinks which are the structureObjectIds
      structureObjectIds = structureLinks.map(lnk => lnk.sourceId);
      structureObjects = (await StructureReadOperations.getStructureObjects(
        structureObjectIds,
        smiO,
      )) as Array<TObject>;
    }

    // get all assembly documents
    structureAssemblyDocuments = (await StructureReadOperations.getAssemblyDocuments(
      structureObjectIds,
    )) as Array<TAssemblyDoc>;

    // get all iam links
    structureIamLinks = await StructureReadOperations.getIamLinks(structureObjectIds);

    return {
      objects: structureObjects,
      links: structureLinks,
      assemblyDocuments: structureAssemblyDocuments,
      iamLinks: structureIamLinks,
    };
  }

  /**
   *
   * @param rootContainer parent structureObject of type with structureContents array
   * @returns an array of all containers in the rootContainer structureContents array
   *
   */
  private static async getAllContainedStructureObjectsFromRoot(
    structureRootReferenceId: ObjectId32,
  ): Promise<Array<StructureObject> | Array<StructureObjectTemplate>> {
    const smi = getSmiByReferenceIdString(structureRootReferenceId.toString());
    if (!smi) throw new Error('No matching schema found, check structureObjectIds prefix');

    // used to ensure the smi collection for the objectIds that are passed is initialized
    const myOps = new baseROperations(smi);

    const containerFilter: Filter<StructureObject> = {
      structureRootReferenceId: { $eq: structureRootReferenceId },
    };
    // Use getMany with correct type
    const result = await myOps.getMany(containerFilter as Filter<any>);

    return result as Array<StructureObject> | Array<StructureObjectTemplate>;
  }

  private static async getStructureObjects(
    structureObjectIds: Array<ObjectId32>,
    smiO: SchemaMapItemType<z.ZodTypeAny, string>,
  ): Promise<Array<StructureObject> | Array<StructureObjectTemplate>> {
    if (structureObjectIds.length <= 0) {
      return [];
    }
    // used to ensure the smi collection for the objectIds that are passed is initialized
    const myOps = new baseROperations(smiO);
    const containerFilter: Filter<StructureObject> = {
      _id: { $in: structureObjectIds },
    };
    // Use getMany with correct type
    const result = await myOps.getMany(containerFilter as Filter<any>);
    return result as Array<StructureObject> | Array<StructureObjectTemplate>;
  }

  private static async getAllStructureLinksFromRoot(
    structureRootReferenceId: ObjectId32,
    smiL: SchemaMapItemType<z.ZodTypeAny, string>,
  ): Promise<Array<StructureLink> | Array<StructureLinkTemplate>> {
    // used to ensure the smi collection for the objectIds that are passed is initialized
    const myOps = new baseROperations(smiL);
    const containerFilter: Filter<StructureLink> = {
      structureRootReferenceId: { $eq: structureRootReferenceId },
    };
    // Use getMany with correct type
    const result = await myOps.getMany(containerFilter as Filter<any>);
    return result as Array<StructureLink> | Array<StructureLinkTemplate>;
  }

  private static async getStructureLinks(
    startingStructureObjectId: ObjectId32,
    smiL: SchemaMapItemType<z.ZodTypeAny, string>,
    depthToRecurse: number = 4,
  ): Promise<Array<StructureLink> | Array<StructureLinkTemplate>> {
    const myOps = new baseROperations(smiL);
    const pipeline = getContainedStructureLinksPipeline(smiL, startingStructureObjectId, depthToRecurse);

    const result = await myOps.aggregateReadOnly(pipeline);
    if (result.length === 0) {
      return [];
    }

    // eslint-disable-next-line @typescript-eslint/no-unsafe-return -- Safe: validated by Zod, type is context-dependent
    const typedResult = result.map(doc => smiL.schema.parse(doc)) as unknown as
      | Array<StructureLink>
      | Array<StructureLinkTemplate>;

    return typedResult;
  }

  private static async getAssemblyDocuments(
    structureObjectIds: Array<ObjectId32>,
  ): Promise<Array<AssemblyDocument> | Array<AssemblyDocumentTemplate>> {
    if (structureObjectIds.length <= 0) {
      return [];
    }
    const smiO = getSmiByReferenceIdString(structureObjectIds[0]!);
    if (!smiO) throw new Error('No matching schema found, check structureObjectIds prefix');
    let smiA: SchemaMapItemType<z.ZodTypeAny, string>;
    if (smiO.dbId === SchemaMap.WorkData_StructureObjects.dbId) {
      smiA = SchemaMap.WorkData_AssemblyDocuments;
    } else {
      smiA = SchemaMap.ResourceObjects_AssemblyDocumentTemplates;
    }
    // this class may be created with a different smi than needed here
    const myOps = new baseROperations(smiA);
    const linkFilter: Filter<{ _id: ObjectId32 }> = {
      structureReferenceId: { $in: structureObjectIds },
    };
    const result = await myOps.getMany(linkFilter);

    // eslint-disable-next-line @typescript-eslint/no-unsafe-return -- Safe: validated by Zod, type is context-dependent
    const typedResult = result.map(doc => smiA.schema.parse(doc)) as unknown as
      | Array<AssemblyDocument>
      | Array<AssemblyDocumentTemplate>;

    return typedResult;
  }

  private static async getIamLinks(structureObjectIds: Array<ObjectId32>): Promise<Array<iamLink>> {
    if (structureObjectIds.length <= 0) {
      return [];
    }
    const smi = SchemaMap.IAM_AccessLinks;
    // this class may be created with a different smi than needed here
    const myOps = new baseROperations(smi);
    const iamFilter: Filter<{ _id: ObjectId32 }> = {
      $or: [
        { connectFromField: { $in: structureObjectIds } },
        { connectToField: { $in: structureObjectIds } },
      ],
    };
    const result = await myOps.getMany(iamFilter);

    const typedResult = result.map(doc => smi.schema.parse(doc));

    return typedResult;
  }
}
