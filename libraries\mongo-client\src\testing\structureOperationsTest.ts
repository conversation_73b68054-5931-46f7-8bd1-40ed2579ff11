// to execute pnpm -F @tnt/mongo-client exec tsx .\src\testing\structureOperationsTest.ts

import { z } from 'zod/v4';
import { SchemaMap } from '@tnt/zod-database-schemas';
import { inspect } from 'util';
import { StructureReadOperations } from '../structureOperations';
import { CrossDataOperationsAsync } from '../crossDataOperations';

(async () => {
  console.log('Starting StructureOperationsTesting');
  //     const sourceSchemaMap: SchemaMapItemType<ZodTypeAny, string> = SchemaMap.Inventory_Products;
  const smi = SchemaMap.WorkData_StructureObjects;
  type objType = z.infer<typeof smi.schema>;

  // get some data to work with and test
  //   let writeStructureObjectsDb = new baseCRUDOperations(smi);

  const myStruct = await StructureReadOperations.getStructuralContents('WDATSOBJ681d59b1d2c5e833b2fc428a', 4);
  if (myStruct.objects.length === 0) {
    console.log('No objects found by StructureReadOperations.getStructuralContents');
  }
  let mytest = new CrossDataOperationsAsync();
  const promises = myStruct.objects.map(obj => mytest.expandDbObject(obj, 10) as Promise<objType>);
  const newObjects = await Promise.all(promises);
  newObjects.forEach(obj => console.log(inspect(obj)));

  // since the recursive iteration is only called one time, any values it replaces
  // with objects are not then expanded themselves. So to get all of the values
  // checked, you have to call the expandDbObject multiple times on the object.
  // if a values is checked and not found, the returned value indicates that.

  console.log('.... process exiting');
  process.exit(0); // Exit with code 0 (success)
})();
