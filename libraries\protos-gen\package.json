{"name": "@tnt/protos-gen", "private": true, "version": "1.0.0", "main": "lib/index.js", "type": "module", "scripts": {"prebuild": "node ./scripts/generate-index.mjs && tsc", "lint": "eslint ."}, "devDependencies": {"@dotenv-run/load": "1.3.6", "@tnt/typescript-config": "workspace:*", "@types/node": "22.7.4", "grpc-tools": "^1.12.4", "grpc_tools_node_protoc_ts": "5.3.3", "nodemon": "3.1.7", "rimraf": "5", "ts-node": "10.9.2", "ts-proto": "^2.7.5", "tsx": "4.19.2", "typescript": "^5.6.3"}, "dependencies": {"@nestjs/microservices": "10.4.6", "protobufjs": "^7.5.0"}, "peerDependencies": {"@grpc/grpc-js": "1.11.1"}, "files": ["src"]}