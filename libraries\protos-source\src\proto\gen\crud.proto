syntax = "proto3";

package crud.v1;

import "google/protobuf/struct.proto";

// Generic CRUD service that can handle any entity type

// TODO: I believe there needs to be another field to this 'generic' protobuf that 
// provides for some basic options for read requests? probably a google.protobuf.Struct type again.
// the entity type verifies the request type if a handler can support multiple requests, 
// but I believe I will still needs some options such as depth, maxRecords, ... and for list will probably need page size, ...

service CrudService {
  // Create a new entity
  rpc Create(CreateRequest) returns (CreateResponse);
  
  // Read an entity by ID
  rpc Read(ReadRequest) returns (ReadResponse);
  
  // Update an existing entity
  rpc Update(UpdateRequest) returns (UpdateResponse);
  
  // Delete an entity by ID
  rpc Delete(DeleteRequest) returns (DeleteResponse);
  
  // List entities with optional filtering and pagination
  rpc List(ListRequest) returns (ListResponse);
  
  // Batch operations
  rpc BatchCreate(BatchCreateRequest) returns (BatchCreateResponse);
  rpc BatchUpdate(BatchUpdateRequest) returns (BatchUpdateResponse);
  rpc BatchDelete(BatchDeleteRequest) returns (BatchDeleteResponse);
}

// Create operations
message CreateRequest {
  string entity_type = 1;  // e.g., "user", "product", "order"
  google.protobuf.Struct data = 2;  // Your zod-validated data goes here
}

message CreateResponse {
  string id = 1;
  google.protobuf.Struct data = 2;  // Created entity with generated fields
  int64 created_at = 3;  // Unix timestamp
}

// Read operations
message ReadRequest {
  string entity_type = 1;
  string id = 2;
}

message ReadResponse {
  google.protobuf.Struct data = 1;  // The entity data
  bool found = 2;
}

// Update operations
message UpdateRequest {
  string entity_type = 1;
  string id = 2;
  google.protobuf.Struct data = 3;  // Partial or full update data
  bool partial = 4;  // true for PATCH-style, false for PUT-style
}

message UpdateResponse {
  google.protobuf.Struct data = 1;  // Updated entity
  int64 updated_at = 2;  // Unix timestamp
  bool found = 3;
}

// Delete operations
message DeleteRequest {
  string entity_type = 1;
  string id = 2;
}

message DeleteResponse {
  bool deleted = 1;
  bool found = 2;
}

// List operations
message ListRequest {
  string entity_type = 1;
  google.protobuf.Struct filter = 2;  // MongoDB-style filter object
  google.protobuf.Struct sort = 3;    // Sort specification
  int32 limit = 4;
  int32 offset = 5;
  string cursor = 6;  // For cursor-based pagination
}

message ListResponse {
  repeated google.protobuf.Struct items = 1;
  int32 total_count = 2;  // Total items matching filter
  string next_cursor = 3;  // For pagination
  bool has_more = 4;
}

// Batch operations
message BatchCreateRequest {
  string entity_type = 1;
  repeated google.protobuf.Struct items = 2;
}

message BatchCreateResponse {
  repeated CreateResponse results = 1;
  repeated string errors = 2;  // Errors for failed creates (by index)
}

message BatchUpdateRequest {
  string entity_type = 1;
  repeated BatchUpdateItem items = 2;
}

message BatchUpdateItem {
  string id = 1;
  google.protobuf.Struct data = 2;
  bool partial = 3;
}

message BatchUpdateResponse {
  repeated UpdateResponse results = 1;
  repeated string errors = 2;  // Errors for failed updates (by index)
}

message BatchDeleteRequest {
  string entity_type = 1;
  repeated string ids = 2;
}

message BatchDeleteResponse {
  repeated DeleteResponse results = 1;
  repeated string errors = 2;  // Errors for failed deletes (by index)
}

// Common error handling
message ErrorDetails {
  string code = 1;      // Error code (e.g., "VALIDATION_ERROR", "NOT_FOUND")
  string message = 2;   // Human-readable error message
  google.protobuf.Struct metadata = 3;  // Additional error context
}