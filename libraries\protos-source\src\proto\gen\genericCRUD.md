This proto file gives you a fully generic CRUD service that can handle any entity type. Here's how it works with your setup:

## Key Features

1. **Entity Type Discrimination** : The `entity_type` field lets you route to different zod schemas and MongoDB collections
2. **Full CRUD Operations** : Create, Read, Update, Delete, List with filtering and pagination
3. **Batch Operations** : For efficiency when you need to process multiple items
4. **Flexible Filtering** : Use MongoDB-style filter objects in the `filter` field
5. **Cursor Pagination** : Supports both offset and cursor-based pagination

## Usage Example

```
// Your zod validation would happen before/after the RPC call
const createUserRequest = {
  entity_type: "user",
  data: Struct.from<PERSON>son({
    name: "<PERSON>",
    email: "<EMAIL>",
    age: 30
  })
};

// Your service handler would:
// 1. Parse the Struct back to JSON
// 2. Validate with your user zod schema
// 3. Save to MongoDB
// 4. Return the created entity as a Struct
```

This approach gives you maximum flexibility while keeping your proto file simple and maintainable. You can handle any entity type through the same service interface, and all your complex validation logic stays in TypeScript with zod.

TODO: I believe there needs to be another field to this 'generic' protobuf that provides for some basic options for read requests? probably a google.protobuf.Struct type again.
the entity type verifies the request type if a handler can support multiple requests, but I believe I will still needs some options such as depth, maxRecords, ... and for list will probably need page size, ...
