/**
 * Tests for OID Factory
 */

import { describe, it, expect, beforeEach } from '@jest/globals';
import { createOidPreprocessor } from '../oid/factory';
import { z } from 'zod/v4';

describe('OID Factory', () => {
  describe('createOidPreprocessor', () => {
    it('should create a preprocessor with the correct prefix', () => {
      const processor = createOidPreprocessor('TEST');
      expect(processor.prefix).toBe('TEST');
    });
    it('should generate full ObjectIds with correct prefix', () => {
      const processor = createOidPreprocessor('TEST');
      const fullId = processor.full();

      expect(fullId).toMatch(/^TEST[a-zA-Z0-9]{28}$/);
      expect(fullId).toHaveLength(32);
    });

    it('should create ObjectIds with custom suffix', () => {
      const processor = createOidPreprocessor('TEST');
      const customId = processor.with('1234567890123456789012345678');

      expect(customId).toBe('TEST1234567890123456789012345678');
      expect(customId).toHaveLength(32);
    });

    it('should auto-expand prefix-only input', () => {
      const processor = createOidPreprocessor('TEST');
      const schema = z.object({ id: processor });

      const result = schema.parse({ id: 'TEST' });
      expect(result.id).toMatch(/^TEST[a-zA-Z0-9]{28}$/);
      expect(result.id).toHaveLength(32);
    });

    it('should validate existing valid ObjectIds', () => {
      const processor = createOidPreprocessor('TEST');
      const schema = z.object({ id: processor });

      const validId = 'TEST1234567890123456789012345678';
      const result = schema.parse({ id: validId });
      expect(result.id).toBe(validId);
    });

    it('should reject invalid ObjectIds', () => {
      const processor = createOidPreprocessor('TEST');
      const schema = z.object({ id: processor }); // Wrong prefix
      expect(() => schema.parse({ id: 'WRONG123456789012345678901234567' })).toThrow();

      // Wrong length
      expect(() => schema.parse({ id: 'TEST12345' })).toThrow();

      // Invalid characters
      expect(() => schema.parse({ id: 'TEST!@#$%^&*()123456789012345678' })).toThrow();
    });
    it('should handle different prefix lengths', () => {
      const shortProcessor = createOidPreprocessor('AB');
      const longProcessor = createOidPreprocessor('VERYLONGPREFIX');

      const shortId = shortProcessor.full();
      const longId = longProcessor.full();

      expect(shortId).toMatch(/^AB[a-zA-Z0-9]{30}$/);
      expect(longId).toMatch(/^VERYLONGPREFIX[a-zA-Z0-9]{18}$/);
      expect(shortId).toHaveLength(32);
      expect(longId).toHaveLength(32);
    });
  });
});
