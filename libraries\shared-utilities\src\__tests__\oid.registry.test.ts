/**
 * Tests for OID Registry
 */

import { describe, it, expect, beforeEach } from '@jest/globals';
import { OidRegistry, globalOidRegistry, registerOidSchema, migrateFromSchemaMap } from '../oid/registry';

describe('OID Registry', () => {
  let registry: OidRegistry;

  beforeEach(() => {
    registry = new OidRegistry();
  });

  describe('register', () => {
    it('should register a schema with default singular name', () => {
      registry.register('Users', 'USR');
      const preprocessors = registry.getAll();

      expect(preprocessors).toHaveProperty('UserId');
      expect(preprocessors.UserId!.prefix).toBe('USR');
    });

    it('should register a schema with custom singular name', () => {
      registry.register('UserAccounts', 'USR_ACC', 'UserAccount');
      const preprocessors = registry.getAll();

      expect(preprocessors).toHaveProperty('UserAccountId');
      expect(preprocessors.UserAccountId!.prefix).toBe('USR_ACC');
    });

    it('should handle schema names ending with s', () => {
      registry.register('Organizations', 'ORG');
      const preprocessors = registry.getAll();

      expect(preprocessors).toHaveProperty('OrganizationId');
      expect(preprocessors.OrganizationId!.prefix).toBe('ORG');
    });
  });

  describe('get', () => {
    it('should retrieve a specific preprocessor', () => {
      registry.register('Users', 'USR');
      const processor = registry.get('UserId');

      expect(processor).toBeDefined();
      expect(processor!.prefix).toBe('USR');
    });

    it('should return undefined for non-existent preprocessor', () => {
      const processor = registry.get('NonExistentId');
      expect(processor).toBeUndefined();
    });
  });

  describe('generateTypes', () => {
    it('should generate TypeScript type definitions', () => {
      registry.register('Users', 'USR');
      registry.register('Organizations', 'ORG');

      const types = registry.generateTypes();

      expect(types).toContain('export type UserId = `USR${string}`;');
      expect(types).toContain('export type OrganizationId = `ORG${string}`;');
    });
  });

  describe('clear', () => {
    it('should clear all registered schemas', () => {
      registry.register('Users', 'USR');
      registry.register('Organizations', 'ORG');

      expect(Object.keys(registry.getAll())).toHaveLength(2);

      registry.clear();

      expect(Object.keys(registry.getAll())).toHaveLength(0);
    });
  });
});

describe('Global Registry Functions', () => {
  beforeEach(() => {
    globalOidRegistry.clear();
  });

  describe('registerOidSchema', () => {
    it('should register schema with global registry', () => {
      registerOidSchema('Users', 'USR');

      const preprocessors = globalOidRegistry.getAll();
      expect(preprocessors).toHaveProperty('UserId');
    });
  });

  describe('migrateFromSchemaMap', () => {
    it('should migrate legacy schema map format', () => {
      const legacySchemaMap = {
        Users: { referenceId: 'USR', schema: {} },
        Organizations: { referenceId: 'ORG', schema: {} },
      };

      migrateFromSchemaMap(legacySchemaMap);

      const preprocessors = globalOidRegistry.getAll();
      expect(preprocessors).toHaveProperty('UserId');
      expect(preprocessors).toHaveProperty('OrganizationId');
      expect(preprocessors.UserId!.prefix).toBe('USR');
      expect(preprocessors.OrganizationId!.prefix).toBe('ORG');
    });
  });
});
