# OID Scripts Migration Notice

## Migration Complete ✅

The OID preprocessor generation scripts have been successfully migrated to the `@tnt/shared-utilities` package.

### What Changed

- **Before**: Code generation scripts created static `oidPreprocessors.ts` files at build time
- **After**: Runtime registry system in `@tnt/shared-utilities` provides dynamic OID management

### Files Removed

- `generateOidPreprocessors.ts` - Main generation script
- `generateOidPreprocessorsCore.ts` - Core generation logic
- `generateOidPreprocessorsTestable.ts` - Testable generation logic

### Migration Benefits

1. **No Build-Time Dependencies**: No more script execution required during build
2. **Runtime Flexibility**: Dynamic registration and management
3. **Better Reusability**: Any package can use OID functionality
4. **Improved Testing**: Core utilities can be tested independently
5. **Reduced Complexity**: Eliminates file generation complexity

### New Usage

```typescript
// New way - using shared-utilities registry
import { globalOidRegistry } from '@tnt/shared-utilities';

// Registration happens at module load in oidRegistry.ts
const oid = globalOidRegistry.getAll();

// Individual preprocessor creation
import { createOidPreprocessor } from '@tnt/shared-utilities';
const myId = createOidPreprocessor('MY_PREF');
```

### Backward Compatibility

All existing code continues to work unchanged:

```typescript
// This still works exactly the same
import { oid } from '@tnt/zod-database-schemas';
const user = { _id: oid.UserId.full() };
```

### Related Files

- `../common/objectId/oidRegistry.ts` - New registry registration
- `@tnt/shared-utilities/src/oid/` - Core OID functionality
- `@tnt/shared-utilities/src/oid/README.md` - Detailed migration guide
