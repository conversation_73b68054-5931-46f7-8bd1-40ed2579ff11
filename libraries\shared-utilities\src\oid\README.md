# OID Preprocessor Migration Guide

## Overview

This document outlines the migration of ObjectId preprocessor functionality from `@tnt/zod-database-schemas` to `@tnt/shared-utilities` for improved reusability and architectural separation.

## Migration Benefits

- ✅ **Decoupled Architecture**: OID utilities no longer depend on schema definitions
- ✅ **Improved Reusability**: Any package can use OID functionality
- ✅ **Runtime Flexibility**: Dynamic registration replaces static code generation
- ✅ **Better Testing**: Core utilities can be tested independently
- ✅ **Reduced Complexity**: Eliminates build-time script execution

## Before vs After

### Before (Generated Code)

```typescript
// Generated file - DO NOT EDIT
export const oid = {
  IAM_AccessLinkId: Object.assign(createSchemaSpecificPreprocessor('IAM_ALIN'), {
    prefix: 'IAM_ALIN' as const,
    full: (): IAM_AccessLinkId => ('IAM_ALIN' + generateObjectId()) as IAM_AccessLinkId,
    with: (suffix: string): IAM_AccessLinkId => ('IAM_ALIN' + suffix) as IAM_AccessLinkId,
  }),
  // ... 50+ more functions
};
```

### After (Registry-Based)

```typescript
// Runtime registration
import { globalOidRegistry, createOidPreprocessor } from '@tnt/shared-utilities';

globalOidRegistry.register('IAM_AccessLinks', 'IAM_ALIN');
const oid = globalOidRegistry.getAll();

// Or create individual preprocessors
const accessLinkId = createOidPreprocessor('IAM_ALIN');
```

## Migration Steps

### Step 1: Install Dependencies

```bash
# Update package.json dependencies
pnpm add @tnt/shared-utilities
```

### Step 2: Update Imports

```typescript
// Before
import { oid } from '../common/objectId/oidPreprocessors';

// After
import { globalOidRegistry } from '@tnt/shared-utilities';
// Register your schemas
globalOidRegistry.register('MySchema', 'MY_PREF');
const oid = globalOidRegistry.getAll();

// Or for individual preprocessors
import { createOidPreprocessor } from '@tnt/shared-utilities';
const mySchemaId = createOidPreprocessor('MY_PREF');
```

### Step 3: Schema Registration

```typescript
// Register all your schemas at module initialization
import { globalOidRegistry } from '@tnt/shared-utilities';

// Individual registration
globalOidRegistry.register('Users', 'USR');
globalOidRegistry.register('Organizations', 'ORG');

// Or bulk registration from existing schema map
import { migrateFromSchemaMap } from '@tnt/shared-utilities';
migrateFromSchemaMap(existingSchemaMap);
```

### Step 4: Update Schema Definitions

```typescript
// Before
import { oid } from '../common/objectId/oidPreprocessors';

const UserSchema = z.object({
  _id: oid.UserId,
  name: z.string(),
});

// After
import { globalOidRegistry } from '@tnt/shared-utilities';
globalOidRegistry.register('Users', 'USR');
const oid = globalOidRegistry.getAll();

const UserSchema = z.object({
  _id: oid.UserId, // Same API!
  name: z.string(),
});
```

## API Reference

### Core Functions

#### `createOidPreprocessor<TPrefix>(prefix: TPrefix)`

Creates an individual OID preprocessor for a specific prefix.

```typescript
import { createOidPreprocessor } from '@tnt/shared-utilities';

const userId = createOidPreprocessor('USR');

// Usage
const fullId = userId.full(); // 'USR' + 24 random chars
const customId = userId.with('custom123456************'); // 'USRcustom123456************'
const validated = userId.parse('USR12345'); // Auto-expands if needed
```

#### `globalOidRegistry.register(key: string, prefix: string, singularName?: string)`

Registers a schema with the global registry.

```typescript
import { globalOidRegistry } from '@tnt/shared-utilities';

// Register with auto-generated singular name
globalOidRegistry.register('Users', 'USR'); // Creates 'UserId'

// Register with custom singular name
globalOidRegistry.register('UserAccounts', 'USR_ACC', 'UserAccount'); // Creates 'UserAccountId'
```

#### `globalOidRegistry.getAll()`

Returns all registered preprocessors as an object.

```typescript
const oid = globalOidRegistry.getAll();
// Returns: { UserId: preprocessor, OrganizationId: preprocessor, ... }
```

### Type Generation

#### `globalOidRegistry.generateTypes()`

Generates TypeScript type definitions for all registered schemas.

```typescript
const types = globalOidRegistry.generateTypes();
// Returns:
// export type UserId = `USR${string}`;
// export type OrganizationId = `ORG${string}`;
```

## Migration Tools

### Legacy Schema Map Migration

For packages currently using the generated SchemaMap:

```typescript
import { migrateFromSchemaMap } from '@tnt/shared-utilities';

const legacySchemaMap = {
  Users: { referenceId: 'USR', schema: userSchema },
  Organizations: { referenceId: 'ORG', schema: orgSchema },
};

// Automatically registers all schemas
migrateFromSchemaMap(legacySchemaMap);

// Now use the registry
const oid = globalOidRegistry.getAll();
```

### Backward Compatibility

The migration maintains full backward compatibility:

```typescript
// This still works exactly the same
const theme = {
  _id: oid.Application_ThemeId.full(),
  name: 'Dark Theme'
};

const validated = ThemeSchema.parse({
  _id: 'APPSTHEM', // Auto-expands
  name: 'Light Theme'
});
```

## Testing Migration

### Before (Generated Code Tests)

```typescript
// Required mocking file system and import.meta.url
jest.mock('fs');
jest.mock('node:path');
jest.mock('node:url');

// 28 tests for code generation
```

### After (Runtime Tests)

```typescript
// Simple unit tests for core functionality
import { createOidPreprocessor, globalOidRegistry } from '@tnt/shared-utilities';

describe('OID Preprocessor', () => {
  it('should create valid preprocessor', () => {
    const processor = createOidPreprocessor('TEST');
    expect(processor.prefix).toBe('TEST');
    expect(processor.full()).toMatch(/^TEST[a-zA-Z0-9]{24}$/);
  });
});
```

## Performance Considerations

### Memory Usage

- **Before**: Static file with all preprocessors loaded
- **After**: Lazy registration, only load what you use

### Bundle Size

- **Before**: Large generated file included in every build
- **After**: Tree-shakeable, import only needed preprocessors

### Runtime Performance

- **Before**: All preprocessors created at module load
- **After**: Preprocessors created on-demand

## Breaking Changes

### None for End Users

The migration is designed to be completely backward compatible for end users:

```typescript
// This code continues to work unchanged
const user = UserSchema.parse({
  _id: 'USR',
  name: 'John Doe'
});
```

### For Library Maintainers

- Remove script execution from build process
- Update internal imports to use shared-utilities
- Register schemas at module initialization

## Rollback Plan

If issues arise, the migration can be rolled back by:

1. Reverting to generated files
2. Re-enabling script execution in build process
3. Removing shared-utilities dependency

## Timeline

### Week 1: Infrastructure

- Implement core OID utilities in shared-utilities
- Create registry system and migration tools

### Week 2: Migration

- Update zod-database-schemas to use new system
- Maintain backward compatibility layer

### Week 3: Testing

- Comprehensive testing of new system
- Performance validation
- Integration testing

### Week 4: Rollout

- Update consuming packages
- Remove legacy scripts
- Documentation updates

## Support

For questions or issues during migration:

1. Check this migration guide
2. Review the test suite for usage examples
3. Contact the architecture team
4. Open an issue in the shared-utilities repository

## Success Metrics

- ✅ Zero breaking changes for end users
- ✅ Reduced build time (no script execution)
- ✅ Improved test coverage and reliability
- ✅ Better separation of concerns
- ✅ Increased reusability across packages
