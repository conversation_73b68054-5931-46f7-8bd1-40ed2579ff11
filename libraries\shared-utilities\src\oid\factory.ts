/**
 * OID Preprocessor Factory
 */

import { z } from 'zod/v4';
import { generateObjectId } from '../generateObjectId';
import type { OidPreprocessor, BrandedObjectId } from './types';

export function createOidPreprocessor<TPrefix extends string>(prefix: TPrefix): OidPreprocessor<TPrefix> {
  const validator = z
    .string()
    .length(32, { message: 'ObjectId32 must be exactly 32 characters long' })
    .refine(val => val.startsWith(prefix), {
      message: `ObjectId32 must start with prefix: ${prefix}`,
    })
    .refine(val => /^[a-zA-Z0-9]+$/.test(val), {
      message: 'ObjectId32 must contain only alphanumeric characters',
    });

  const preprocessor = z.preprocess((input: unknown) => {
    if (typeof input !== 'string') return input;
    if (input.length === prefix.length && input === prefix) {
      const suffixLength = 32 - prefix.length;
      return input + generateObjectId(suffixLength);
    }
    return input;
  }, validator) as any;

  return Object.assign(preprocessor, {
    prefix,
    full: () => {
      const suffixLength = 32 - prefix.length;
      return `${prefix}${generateObjectId(suffixLength)}` as BrandedObjectId<TPrefix>;
    },
    with: (suffix: string) => `${prefix}${suffix}` as BrandedObjectId<TPrefix>,
  });
}
