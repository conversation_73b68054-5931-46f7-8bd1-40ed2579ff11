/**
 * OID (ObjectId) utilities for shared use across all packages
 *
 * This module provides:
 * - Individual OID preprocessor creation
 * - Global registry for schema-based OID management
 * - Migration utilities for legacy systems
 * - Type-safe branded ObjectId types
 */

export * from './types';
export * from './factory';
export * from './registry';

// Re-export the generateObjectId for convenience
export { generateObjectId } from '../generateObjectId';
