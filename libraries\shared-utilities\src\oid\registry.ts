/**
 * OID Registry System
 */

import { createOidPreprocessor } from './factory';
import type { OidConfig, OidPreprocessor, LegacySchemaMap } from './types';

export class OidRegistry {
  private configs = new Map<string, OidConfig>();
  private preprocessors = new Map<string, OidPreprocessor<any>>();

  register(key: string, prefix: string, singularName?: string, name?: string) {
    const config: OidConfig = {
      prefix,
      singularName: singularName || key.replace(/s$/, ''),
      name: name || key,
    };

    this.configs.set(key, config);
    const preprocessorKey = `${config.singularName}Id`;
    this.preprocessors.set(preprocessorKey, createOidPreprocessor(prefix));
  }

  get<TPrefix extends string>(name: string): OidPreprocessor<TPrefix> | undefined {
    return this.preprocessors.get(name);
  }

  getAll(): Record<string, OidPreprocessor<any>> {
    return Object.fromEntries(this.preprocessors.entries());
  }

  getAllConfigs(): Record<string, OidConfig> {
    return Object.fromEntries(this.configs.entries());
  }

  generateTypes(): string {
    const types = Array.from(this.configs.values())
      .map(config => `export type ${config.singularName}Id = \`${config.prefix}\${string}\`;`)
      .join('\n');

    return types;
  }

  clear(): void {
    this.configs.clear();
    this.preprocessors.clear();
  }
}

export const globalOidRegistry = new OidRegistry();

/**
 * Helper function for easy registration
 */
export function registerOidSchema(key: string, prefix: string, singularName?: string, name?: string): void {
  globalOidRegistry.register(key, prefix, singularName, name);
}

/**
 * Migration helper for legacy schema maps
 */
export function migrateFromSchemaMap(schemaMap: LegacySchemaMap): void {
  for (const [key, entry] of Object.entries(schemaMap)) {
    globalOidRegistry.register(key, entry.referenceId);
  }
}

/**
 * Generate legacy oid object for backward compatibility
 */
export function generateLegacyOidObject(): Record<string, any> {
  return globalOidRegistry.getAll();
}
