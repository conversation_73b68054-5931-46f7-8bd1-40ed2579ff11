/**
 * Core types for OID (ObjectId) functionality
 */

export type ObjectId32 = string & { __brand: 'ObjectId32' };
export type OidPrefix = string & { __brand: 'OidPrefix' };

export interface OidConfig {
  prefix: string;
  singularName: string;
  name: string;
}

export type BrandedObjectId<TPrefix extends string> = `${TPrefix}${string}`;

export interface OidPreprocessor<TPrefix extends string> {
  prefix: TPrefix;
  full(): BrandedObjectId<TPrefix>;
  with(suffix: string): BrandedObjectId<TPrefix>;
  parse(input: unknown): BrandedObjectId<TPrefix>;
}

export interface LegacySchemaMapEntry {
  referenceId: string;
  schema?: any;
}

export type LegacySchemaMap = Record<string, LegacySchemaMapEntry>;
