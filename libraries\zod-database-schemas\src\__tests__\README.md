# Jest Test Suite for OID Registry Integration

This directory contains comprehensive Jest tests for validating the migrated OID (ObjectId) functionality that now uses a runtime registry system from `@tnt/shared-utilities`.

## Migration Status: COMPLETE ✅

The OID preprocessor functionality has been **fully migrated** from static generation scripts to runtime registry system:

- ✅ **Zero Breaking Changes**: All existing code continues to work unchanged
- ✅ **Runtime Registration**: Dynamic schema registration replaces static file generation
- ✅ **Better Architecture**: Improved separation of concerns and reusability
- ✅ **Enhanced Testing**: Core utilities are tested independently in `@tnt/shared-utilities`
- ✅ **Legacy Scripts Removed**: All old generation scripts have been cleaned up

## Test Overview

The test suite validates the integration with the new OID system including:

### Migration Validation

- ✅ **Backward Compatibility**: Ensures existing API continues to work
- ✅ **Registry Integration**: Validates global OID registry functionality
- ✅ **Schema Registration**: Tests all database schemas are properly registered
- ✅ **Preprocessor Generation**: Verifies dynamic preprocessor creation

### Integration Testing

- ✅ **ID Generation**: Tests full ObjectId generation with correct prefixes
- ✅ **Custom Suffixes**: Validates custom ObjectId creation with `.with()` method
- ✅ **Zod Integration**: Tests schema validation with auto-expansion
- ✅ **Type Safety**: Ensures TypeScript branded types work correctly

## Test Structure

```text
src/__tests__/
├── oidMigration.test.ts   # Integration tests for migrated system
└── README.md              # This documentation

src/common/objectId/
├── oidRegistry.ts         # Runtime schema registration
└── index.ts               # Updated exports using new registry
```

## Architecture Changes

### Before (Static Generation)

```typescript
// Generated file - DO NOT EDIT MANUALLY
export const oid = {
  IAM_AccessLinkId: Object.assign(createSchemaSpecificPreprocessor('IAM_ALIN'), {
    prefix: 'IAM_ALIN' as const,
    // ... 50+ more static functions
  }),
};
```

### After (Runtime Registry)

```typescript
// Runtime registration
import { globalOidRegistry } from '@tnt/shared-utilities';
globalOidRegistry.register('IAM_AccessLinks', 'IAM_ALIN');
export const oid = globalOidRegistry.getAll();
```

## Running Tests

### Run All Tests

```bash
pnpm test
```

### Run Tests in Watch Mode

```bash
pnpm test:watch
```

### Generate Coverage Report

```bash
pnpm test:coverage
```

### Run Specific Test File

```bash
pnpm test oidMigration.test.ts
```

## Test Configuration

### Jest Config (`jest.config.cjs`)

- **Preset**: `ts-jest/presets/default-esm` for ESM TypeScript support
- **Environment**: Node.js environment
- **Module Mapping**: Maps `@tnt/shared-utilities` to source files for testing
- **Test Match**: All `.test.ts` files in `src/__tests__/`
- **Transform Patterns**: Handles workspace dependencies correctly

### Coverage Focus

The tests focus on integration and migration validation rather than unit test coverage of individual functions, since the core OID functionality is now tested in `@tnt/shared-utilities`.

## Test Categories

### 1. Migration Integration Tests (5 tests)

Tests in `oidMigration.test.ts`:

#### Export Validation

- Verifies `oid` object is properly exported
- Checks all expected preprocessors are available
- Tests key schema preprocessors exist:
  - `IAM_AccessLinkId`
  - `Application_ThemeId`
  - `Inventory_ProductId`
  - `WorkData_WorkEntrieId`

#### Backward Compatibility

- Tests preprocessor API remains unchanged
- Validates `prefix`, `full()`, and `with()` methods
- Ensures ID generation patterns are correct
- Verifies 32-character ObjectId length requirement

#### Zod Schema Integration

- Tests auto-expansion of prefix-only inputs
- Validates existing ObjectId parsing
- Ensures schema validation works correctly
- Tests type safety is maintained

#### Registry System

- Validates all schemas are registered in global registry
- Tests configuration structure integrity
- Verifies prefix mappings are correct
- Ensures registry state is consistent

#### Type Generation

- Tests TypeScript type generation functionality
- Validates branded type definitions
- Ensures type exports are correct

## Expected Test Output

### Success Case

```text
PASS src/__tests__/oidMigration.test.ts
  OID Migration Tests
    ✓ should export oid object with all expected preprocessors
    ✓ should maintain backward compatibility with preprocessor API
    ✓ should work with Zod schemas for validation
    ✓ should have all expected schema mappings registered
    ✓ should generate proper TypeScript types

Test Suites: 1 passed, 1 total
Tests:       5 passed, 5 total
```

## Registered Schemas

The migration test validates that all these schemas are properly registered:

### Access Control

- `IAM_AccessLinks` → `IAM_ALIN`

### Application Settings

- `Application_Theme` → `APPSTHEM`

### Inventory

- `Inventory_Products` → `INV_PROD`
- `Inventory_Transactions` → `INV_TRAN`
- `Inventory_Locations` → `INV_LOCA`
- `Inventory_StockLocations` → `INV_SLOC`
- `Inventory_Skus` → `INV_SKUS`
- `Inventory_Items` → `INV_ITEM`

### Resource Objects

- `ResourceObjects_Addresses` → `ROBJADDR`
- `ResourceObjects_ApiCalls` → `ROBJAPIC`
- `ResourceObjects_ApiHostConnections` → `ROBJAPIH`
- `ResourceObjects_AssemblyDocumentTemplates` → `ROBJASMD`
- `ResourceObjects_Contacts` → `ROBJCONT`
- `ResourceObjects_Documents` → `ROBJDOCU`
- `ResourceObjects_Entities` → `ROBJENTI`
- `ResourceObjects_Equipment` → `ROBJEQUI`
- `ResourceObjects_Groups` → `ROBJGROU`
- `ResourceObjects_ItemTypes` → `ROBJITYP`
- `ResourceObjects_StructureLinkTemplates` → `ROBJSLNT`
- `ResourceObjects_StructureObjectTemplates` → `ROBJSOBT`
- `ResourceObjects_StructureSchemeItems` → `ROBJSSCH`
- `ResourceObjects_Racis` → `ROBJRACI`
- `ResourceObjects_Requests` → `ROBJREQU`

### Work Data

- `WorkData_AssemblyDocuments` → `WDATASMD`
- `WorkData_ResourceEntries` → `WDATREEN`
- `WorkData_StructureLinks` → `WDATSLNK`
- `WorkData_StructureObjects` → `WDATSOBJ`
- `WorkData_WorkEntries` → `WDATWENT`

## Migration Benefits

### Architectural Improvements

- **Separation of Concerns**: OID utilities no longer depend on schema definitions
- **Improved Reusability**: Any package can use OID functionality independently
- **Runtime Flexibility**: Dynamic registration replaces static code generation
- **Better Testing**: Core utilities can be tested independently
- **Reduced Complexity**: Eliminates build-time script execution

### Development Benefits

- **No Build Dependencies**: No more script execution required during build
- **Tree-shakeable**: Import only needed preprocessors
- **Dynamic Registration**: Runtime schema registration instead of generation
- **Enhanced Maintainability**: Better separation of concerns

### Performance Benefits

- **Bundle Size**: Tree-shakeable, smaller bundles possible
- **Build Time**: No script execution during build
- **Runtime**: On-demand preprocessor creation
- **Memory Usage**: Only load what you use

## Integration with CI/CD

These tests integrate seamlessly with continuous integration to validate the migration:

```yaml
# Example GitHub Actions step
- name: Run Migration Tests
  run: pnpm test oidMigration.test.ts

- name: Validate Build
  run: pnpm prebuild
```

## Troubleshooting

### Common Issues

#### Import Resolution Errors

- Ensure `@tnt/shared-utilities` is properly mapped in Jest config
- Check workspace dependencies are correctly installed

#### Registry Registration Issues

- Verify all schemas are registered in `oidRegistry.ts`
- Check prefix mappings match expected values

#### Type Validation Errors

- Ensure TypeScript types are properly generated
- Verify branded types are correctly exported

### Debug Tips

1. **Registry Inspection**: Log `globalOidRegistry.getAllConfigs()` to see registered schemas
2. **Preprocessor Testing**: Test individual preprocessors in isolation
3. **Import Tracing**: Verify import paths are resolving correctly
4. **Schema Validation**: Check that Zod schemas work with new preprocessors

## Migration Status

✅ **Migration Complete**: All functionality has been successfully migrated to `@tnt/shared-utilities`

✅ **Backward Compatible**: No changes required for existing consumers

✅ **Fully Tested**: Integration tests validate all critical functionality

✅ **Production Ready**: Ready for use across all TNT workspace packages
