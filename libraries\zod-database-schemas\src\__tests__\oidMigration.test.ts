/**
 * Tests for OID Migration and Registry Integration
 */

import { describe, it, expect } from '@jest/globals';
import { oid, globalOidRegistry } from '../common/objectId/oidRegistry';

describe('OID Migration Tests', () => {
  it('should export oid object with all expected preprocessors', () => {
    expect(oid).toBeDefined();
    expect(typeof oid).toBe('object');

    // Test some key preprocessors exist
    expect(oid).toHaveProperty('IAM_AccessLinkId');
    expect(oid).toHaveProperty('Application_ThemeId');
    expect(oid).toHaveProperty('Inventory_ProductId');
    expect(oid).toHaveProperty('WorkData_WorkEntrieId');
  });

  it('should maintain backward compatibility with preprocessor API', () => {
    // Test that preprocessors have the expected methods
    expect(oid.IAM_AccessLinkId).toBeDefined();
    expect(oid.IAM_AccessLinkId!.prefix).toBe('IAM_ALIN');
    expect(typeof oid.IAM_AccessLinkId!.full).toBe('function');
    expect(typeof oid.IAM_AccessLinkId!.with).toBe('function');

    // Test ID generation
    const fullId = oid.IAM_AccessLinkId!.full();
    expect(fullId).toMatch(/^IAM_ALIN[a-zA-Z0-9]{24}$/);
    expect(fullId).toHaveLength(32);

    const customId = oid.IAM_AccessLinkId!.with('1234567890123456789012345678');
    expect(customId).toBe('IAM_ALIN1234567890123456789012345678');
  });

  it('should work with Zod schemas for validation', () => {
    const { z } = require('zod/v4');

    const TestSchema = z.object({
      _id: oid.Application_ThemeId,
      name: z.string(),
    });

    // Test auto-expansion
    const result1 = TestSchema.parse({
      _id: 'APPSTHEM',
      name: 'Test Theme',
    });
    expect(result1._id).toMatch(/^APPSTHEM[a-zA-Z0-9]{24}$/);

    // Test valid existing ID
    const validId = 'APPSTHEM123456789012345678901234';
    const result2 = TestSchema.parse({
      _id: validId,
      name: 'Test Theme',
    });
    expect(result2._id).toBe(validId);
  });

  it('should have all expected schema mappings registered', () => {
    const allConfigs = globalOidRegistry.getAllConfigs();

    // Check that key schemas are registered
    expect(allConfigs).toHaveProperty('IAM_AccessLinks');
    expect(allConfigs).toHaveProperty('Application_Theme');
    expect(allConfigs).toHaveProperty('Inventory_Products');
    expect(allConfigs).toHaveProperty('WorkData_WorkEntries');

    // Verify config structure
    expect(allConfigs.IAM_AccessLinks!.prefix).toBe('IAM_ALIN');
    expect(allConfigs.Application_Theme!.prefix).toBe('APPSTHEM');
  });

  it('should generate proper TypeScript types', () => {
    const types = globalOidRegistry.generateTypes();

    expect(types).toContain('export type IAM_AccessLinkId = `IAM_ALIN${string}`;');
    expect(types).toContain('export type Application_ThemeId = `APPSTHEM${string}`;');
    expect(types).toContain('export type Inventory_ProductId = `INV_PROD${string}`;');
  });
});
