import { z } from 'zod/v4';
import { AUTH_LEVEL, AUTH_PREDICATE } from '../common/enums';

import { preprocessObjectId32 } from '../common';

// IAM access can be granted/passed through multiple objects including groups, users, containers.
export const IamLinkSchema = z.object({
  _id: preprocessObjectId32,
  connectFromFieldName: z.string(), // used for debug/test
  connectFromFieldId: preprocessObjectId32, // object
  predicate: AUTH_PREDICATE.default('connectFrom_connectTo'),
  connectToFieldName: z.string(), // used for debug/test
  connectToFieldId: preprocessObjectId32, // subject
  authorizationLevel: AUTH_LEVEL.default('READ'),
  profileId: preprocessObjectId32.optional().nullable().default(null), // this would be time/location/status... based limitations on the authorization level

  // structureId: preprocessObjectId32.optional().nullable().default(null), // valid if part of a structure, equal to the structure Start ObjectId
  // structureLinkData: z
  //   .object({
  //     edgeType: EDGE_TYPE.default(EDGE_TYPE.Values.oneWayArrow), // display usage only
  //     relationship: PRECEDENCE_TYPE.default(PRECEDENCE_TYPE.Values.FINISH_TO_START), // display usage only, is this required to help define the visual type of path?
  //     drawPath: z.array(z.string()).optional().nullable().default(null),
  //   })
  //   .optional().nullable().default(null),
});

export type IamLink = z.infer<typeof IamLinkSchema>;
