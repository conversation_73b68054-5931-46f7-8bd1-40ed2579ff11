import { z } from 'zod/v4';

import { preprocessDate, preprocessObjectId32 } from '../../common';

// concept underlying RegexRuleSchema is a database to store regex strings for standardization of naming conventions.
// an example would be inventory items such as product names/ids, skus, item names, location names,
// even project names, ... could all be standardized

// TODO M-1: review RegexRuleSchema
// BRYAN review please, is there a better way to achieve this? i had considered embedding into the actual parent collection but
// then didn't have a way to incorporate a rule for the top level
// after your review, if this stands, I will build up the rest of the required schemaMap stuff.
export const RegexRuleSchema = z.object({
  _id: preprocessObjectId32,
  isActive: z.boolean(),
  validFrom: preprocessDate,
  validTo: preprocessDate,
  modifiedById: preprocessObjectId32,
  // propertyPath should be a dot separated string denoting property.property.property ... down into a nested collection
  appliesTo: z.array(z.object({ database: z.string(), collection: z.string(), propertyPath: z.string() })),
  // define the number of fields, length of each field, and then the rule for each field, this should be easier to implement
  // as the user creates the rules and as the code validates the inputs
  // allowed field separators
  fieldSeparator: z.enum(['_', '-', '.', ':', '|']),
  fieldRules: z.array(
    z.object({
      fieldNumber: z.number().int(),
      numberOfCharacters: z.number().int(),
      literals: z.array(z.string()),
      regexRule: z.string(),
    }),
  ),
});
