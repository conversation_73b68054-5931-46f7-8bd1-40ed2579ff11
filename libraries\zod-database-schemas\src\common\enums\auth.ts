import { z } from 'zod/v4';

// standard rules for predicates
// is <PERSON><PERSON><PERSON> included in AUTH_PERMISSONS?
// how do you implement time/location/.. profiles and limitations

// const AUTH_PREDICATE = z.enum(['IS_PART_OF', 'HAS_TEAM', 'IS_CREATED_BY', 'HAS_ACCESS_TO', 'IS_STORED_IN']);
const AUTH_PREDICATE = z.enum(['IS_PART_OF', 'HAS_ACCESS_TO', 'connectFrom_connectTo']);

// taken from NTFS basic permissions
const AUTH_LEVEL = z.enum(['FULL_CONTROL', 'MODIFY', 'READ_AND_EXECUTE', 'READ', 'WRITE']);

export { AUTH_PREDICATE, AUTH_LEVEL };
