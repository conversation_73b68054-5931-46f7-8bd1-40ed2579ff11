import z from 'zod/v4';

/*
https://www.answeroverflow.com/m/1127608401761935511
// Single source of truth:
const fishes = ["Salmon", "Tuna", "Trout"] as const
// Use in a Zod Schema:
const fishesSchema = z.enum(fishes)
// Type to use in the code:
// = "Salmon" | "Tuna" | "Trout"
type FishType = (typeof fishes)[number];
*/

const GENETIC_TYPE = z.enum([
  'NONE',
  'INHERIT', // take same permission as parent
  'HEREDITY', // give same permission to children
  'BOTH',
]);

const CONNECTION_MODE = z.enum(['READ', 'WRITE', 'READ_WRITE']);

const CONNECTION_TYPE = z.enum(['READ', 'WRITE', 'READ_WRITE']);

const AUTHENTICATION_TYPE = z.enum(['NONE', 'Basic', 'OAUTH2', 'API_KEY', 'BEARER_TOKEN']);

const HTTP_CALL = z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']);

export { GENETIC_TYPE, CONNECTION_MODE, CONNECTION_TYPE, AUTHENTICATION_TYPE, HTTP_CALL };
