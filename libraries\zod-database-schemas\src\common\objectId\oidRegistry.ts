/**
 * OID Schema Registration for Database Schemas
 * This file registers all database schemas with the global OID registry
 */

import { globalOidRegistry } from '@tnt/shared-utilities';

// Register all schemas with the global OID registry
// This will be called at module load to ensure all schemas are available

// Access Control
globalOidRegistry.register('IAM_AccessLinks', 'IAM_ALIN', 'IAM_AccessLink');

// Application Settings
globalOidRegistry.register('Application_Theme', 'APPSTHEM', 'Application_Theme');

// Inventory
globalOidRegistry.register('Inventory_Products', 'INV_PROD', 'Inventory_Product');
globalOidRegistry.register('Inventory_Transactions', 'INV_TRAN', 'Inventory_Transaction');
globalOidRegistry.register('Inventory_Locations', 'INV_LOCA', 'Inventory_Location');
globalOidRegistry.register('Inventory_StockLocations', 'INV_SLOC', 'Inventory_StockLocation');
globalOidRegistry.register('Inventory_Skus', 'INV_SKUS', 'Inventory_Sku');
globalOidRegistry.register('Inventory_Items', 'INV_ITEM', 'Inventory_Item');

// Resource Objects
globalOidRegistry.register('ResourceObjects_Addresses', 'ROBJADDR', 'ResourceObjects_Addresse');
globalOidRegistry.register('ResourceObjects_ApiCalls', 'ROBJAPIC', 'ResourceObjects_ApiCall');
globalOidRegistry.register(
  'ResourceObjects_ApiHostConnections',
  'ROBJAPIH',
  'ResourceObjects_ApiHostConnection',
);
globalOidRegistry.register(
  'ResourceObjects_AssemblyDocumentTemplates',
  'ROBJASMD',
  'ResourceObjects_AssemblyDocumentTemplate',
);
globalOidRegistry.register('ResourceObjects_Contacts', 'ROBJCONT', 'ResourceObjects_Contact');
globalOidRegistry.register('ResourceObjects_Documents', 'ROBJDOCU', 'ResourceObjects_Document');
globalOidRegistry.register('ResourceObjects_Entities', 'ROBJENTI', 'ResourceObjects_Entitie');
globalOidRegistry.register('ResourceObjects_Equipment', 'ROBJEQUI', 'ResourceObjects_Equipment');
globalOidRegistry.register('ResourceObjects_Groups', 'ROBJGROU', 'ResourceObjects_Group');
globalOidRegistry.register('ResourceObjects_ItemTypes', 'ROBJITYP', 'ResourceObjects_ItemType');
globalOidRegistry.register(
  'ResourceObjects_StructureLinkTemplates',
  'ROBJSLNT',
  'ResourceObjects_StructureLinkTemplate',
);
globalOidRegistry.register(
  'ResourceObjects_StructureObjectTemplates',
  'ROBJSOBT',
  'ResourceObjects_StructureObjectTemplate',
);
globalOidRegistry.register(
  'ResourceObjects_StructureSchemeItems',
  'ROBJSSCH',
  'ResourceObjects_StructureSchemeItem',
);
globalOidRegistry.register('ResourceObjects_Racis', 'ROBJRACI', 'ResourceObjects_Raci');
globalOidRegistry.register('ResourceObjects_Requests', 'ROBJREQU', 'ResourceObjects_Request');

// Work Data
globalOidRegistry.register('WorkData_AssemblyDocuments', 'WDATASMD', 'WorkData_AssemblyDocument');
globalOidRegistry.register('WorkData_ResourceEntries', 'WDATREEN', 'WorkData_ResourceEntrie');
globalOidRegistry.register('WorkData_StructureLinks', 'WDATSLNK', 'WorkData_StructureLink');
globalOidRegistry.register('WorkData_StructureObjects', 'WDATSOBJ', 'WorkData_StructureObject');
globalOidRegistry.register('WorkData_WorkEntries', 'WDATWENT', 'WorkData_WorkEntrie');

// Export the registered oid object for backward compatibility
export const oid = globalOidRegistry.getAll();

// Export types for enhanced schemas
export type WithOid<T, K extends keyof T, TOidType> = Omit<T, K> & Record<K, TOidType>;

// Export utilities from shared-utilities
export {
  createOidPreprocessor,
  globalOidRegistry,
  generateObjectId,
  type OidPreprocessor,
  type BrandedObjectId,
} from '@tnt/shared-utilities';
