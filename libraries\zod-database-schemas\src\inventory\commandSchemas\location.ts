// use entity from resources for warehouses, branches, ...
import { z } from 'zod/v4';

import { preprocessObjectId32 } from '../../common';

// distinctive organization that also requires an separate address
// typically used for a separate warehouse, branch, or an external organization
export const InventoryDbLocationSchema = z.object({
  _id: preprocessObjectId32,
  active: z.boolean().default(true),
  entityId: preprocessObjectId32, // should reference ROBJENTI
  siteId: preprocessObjectId32.optional().nullable().default(null), // should reference ROBJENTI
  warehouseId: preprocessObjectId32.optional().nullable().default(null), // should reference ROBJENTI
  descriptors: z.array(preprocessObjectId32), // should be an array of ROBJITYPs of GroupName "InventoryLocationDescriptor"
  // example descriptors [level1, aisle2, rack3, shelf4, bin8]
});

export type InventoryDbLocation = z.infer<typeof InventoryDbLocationSchema>;

// used to delineate a structure id in inventory operations
export const StructureLocationSchema = z.object({ structureId: preprocessObjectId32 });
