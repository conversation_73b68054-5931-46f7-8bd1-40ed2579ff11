import { z } from 'zod/v4';

import { preprocessDate, preprocessObjectId, preprocessObjectId32 } from '../../common';

export const ScheduledTransactionSchema = z.object({
  _id: preprocessObjectId,
  active: z.boolean(),
  dateScheduled: preprocessDate,
  quantity: z.number(),
  // units: preprocessObjectId32, // this should be in whatever the standard item units are, therefore shouldn't be needed here?
  // structureReferenceId, externalReferenceId should be one or the other, or both?
  structureReferenceId: preprocessObjectId32.optional().nullable().default(null), // should reference structure object that will issue (+/-) the item,
  externalReferenceId: preprocessObjectId32.optional().nullable().default(null), // should external (to this application), purchase order, ...
  createdOn: preprocessDate, //
  createdById: preprocessObjectId32, // resource that affected the change, user, group, container, ...
});

//  REVIEW Scheduled Transactions vs ReservedItems (assignedStructureObjects)
/**
 * Although similar in that they both affect inventory sums, are the differences in these enough such that
 * they should be stored separately?
 * Both reflect an add/remove of items from inventory at some future point in time
 * ReservedItems represent individual items (batches, lots, or serial numbers) that have a reservation
 * in a project or process timeline
 * Schedule transactions represent stockLocation transfers that occur due to an external process
 * From an inventory reporting standpoint, we should be able to view reservations as an distinct summed quantity
 * whereas scheduled transactions should be only viewed in a timeline?
 */

// REVIEW scheduled transactions are available for only?
// how to deal with ItemIds that need to be assigned to specific structure objects?
// how to deal with skus coming in with no itemId infomation? same as always,
//  itemId1 = skuCode if not used, skuCode = productCode if not used

// scheduled transactions may only schedule inventory transaction types
//        InventoryTransactionItemTransferSchema,
//        InventoryTransactionItemIssueSchema,
//

// REVIEW this needs to be expanded to include inventory transaction as well as assignedStructureObject transactions?
// where should the actual transaction and/or scheduled transaction be stored? since assignedStructureObject is a
// inventory transaction, should this be stored under it? this may simply end up being an extension of a standard
// transaction schema with the scheduled information on it?
