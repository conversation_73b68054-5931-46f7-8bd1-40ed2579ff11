import { z } from 'zod/v4';

import { preprocessObjectId, preprocessObjectId32 } from '../../common';

import { InventoryPriceSchema } from './price';
import { InventoryDbStockLocationSchema } from './stockLocation';

/**
 * a "SKU" (Stock Keeping Unit) refers to a unique code and designed for internal use only
 * and assigned to each individual product variation, including different colors and sizes,
 * allowing retailers to accurately track inventory for each specific type of item within their stock;
 * essentially, each color and size combination of a clothing item would have its own distinct SKU number
 * think about the information required to individualize products with what the customer would choose
 * this type of tracking does not necessarily track item individually, how to designate that differential?
 *
 */
export const InventoryDbSKUSchema = z.object({
  _id: preprocessObjectId, // since this is NOT a base collection document, it does NOT user a preprocessObjectID32!
  active: z.boolean(), // used to deactivate old SKUs
  manufacturerId: preprocessObjectId32.optional().nullable().default(null), // EntitySchema.optional(),
  manufacturerPartNumber: z.string().optional().nullable().default(null),
  accountingCrossReference: z.string().optional().nullable().default(null),

  GTIN: z.string().optional().nullable().default(null),
  UPC: z.string().optional().nullable().default(null),
  EAN: z.string().optional().nullable().default(null),
  ISBN: z.string().optional().nullable().default(null),
  // ASIN:z.string().optional().nullable().default(null),  // amazon specific
  // FNSKU:z.string().optional().nullable().default(null),  // amazon specific

  skuName: z.string().optional().nullable().default(null),
  skuCode: z.string(), // required unique?  if non-existing use product _id?
  skuDescription: z.string().optional().nullable().default(null),
  tags: z.array(z.string().optional().nullable().default(null)).optional().nullable().default([]),

  // we need a method to allow for user defined codes (in ROBJITYP?) to be used at different levels
  // as well as user defined variant properties (also in ROBJITYP?)
  // difference is a code is unique across an inventory level (product, sku, item)
  // a variant propery is not, e.g. two unique skus could both have the property of color: blue
  // how do we create a variable array that includes indexed/unique identifiers?
  // I do have a method for creating indexes from code on a database.
  // see schemaMap.ts schemaMapStructure for fixed collection indexes
  // and resources/structureTemplates/structureSchema.ts for dynamically creating them
  // REVIEW how to allow builder to create their own identifiers?
  variantIdentifiers: z.array(preprocessObjectId32).optional().nullable().default(null), // should be an array of ROBJITYPs of GroupName "InventoryLocationDescriptor"

  colorId: preprocessObjectId32.optional().nullable().default(null), // ItemTypeSchema.optional()
  sizeId: preprocessObjectId32.optional().nullable().default(null), // ItemTypeSchema.optional()
  cost: InventoryPriceSchema.optional().nullable().default(null),
  stockLocations: z.array(InventoryDbStockLocationSchema).optional().nullable().default(null),
  weight: z.number().optional().nullable().default(null),
  weightUnitsId: preprocessObjectId32.optional().nullable().default(null), // ItemTypeSchema.optional()
  dimensions: z
    .object({ height: z.number(), width: z.number(), length: z.number() })
    .optional()
    .nullable()
    .default(null),
  dimensionsUnitsId: preprocessObjectId32.optional().nullable().default(null), // ItemTypeSchema.optional()
  qty0Tol: z.number().default(0.0001),
  skuSum: z.number().readonly().default(0),
  skuReservedSum: z.number().readonly().default(0),
  referenceDocs: z
    .array(z.object({ docTypeId: preprocessObjectId32, docId: preprocessObjectId32 }))
    .optional()
    .nullable()
    .default(null), // this would reference drawing, images, ..., in the format {ROBJITYP, ROBJDOCU}...
});

export type InventoryDbSKU = z.infer<typeof InventoryDbSKUSchema>;
