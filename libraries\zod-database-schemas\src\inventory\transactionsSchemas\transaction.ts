import { z } from 'zod/v4';

import { preprocessDate, preprocessObjectId, preprocessObjectId32 } from '../../common';
import { InventoryDbItemSchema } from '../commandSchemas/item';
import { InventoryDbLocationSchema } from '../commandSchemas/location';
import { InventoryDbProductSchema } from '../commandSchemas/product';
import { InventoryDbSKUSchema } from '../commandSchemas/sku';
import { InventoryDbStockLocationSchema } from '../commandSchemas/stockLocation';

// import {
//   InventoryTransactionProductCreateSchema,
//   InventoryTransactionProductModifySchema,
// } from './transactionProduct';
// import { InventoryTransactionSkuCreateSchema, InventoryTransactionSkuModifySchema } from './transactionSku';
// import {
//   InventoryTransactionLocationCreateSchema,
//   InventoryTransactionLocationModifySchema,
// } from './transactionLocation';
// import {
//   InventoryTransactionItemAdjustSchema,
//   InventoryTransactionItemIssueSchema,
//   InventoryTransactionItemTransferSchema,
// } from './transactionItem';
// import {
//   InventoryTransactionStockLocationCreateSchema,
//   InventoryTransactionStockLocationModifySchema,
// } from './transactionStockLocation';

export const LocationRouteSchema = z.object({
  origin: preprocessObjectId.optional(), // InventoryStockLocation if a location is needed, origin would not be needed for location add/modify
  destination: preprocessObjectId, // InventoryStockLocation if a location is needed, destination is always available
});

export const InventoryTransactionItemTransferSchema = InventoryDbItemSchema.extend({
  transactionDataType: z.literal('Inventory.Items.Transfer'),
  locationRoute: LocationRouteSchema,
});

// since an issue is a creation of stock item from '??? somewhere' we need to track where it is from
// where can inventory stock originate from?
// it was manufactured in this system, need to track where it came from, RAF.line item entry - report as finished
//
export const InventoryTransactionItemIssueSchema = InventoryDbItemSchema.extend({
  transactionDataType: z.literal('Inventory.Items.Issue'),
  locationRoute: LocationRouteSchema, // defines where it is going
});

// TODO M-1:  REVIEW USAGE - considered logging inventory as resource to project, not across projects?
export const ResourceEntryInventoryTransactionItemSchema = InventoryTransactionItemIssueSchema.extend({
  transactionDataType: z.literal('inventoryIssue'),
});

export const InventoryTransactionItemAdjustSchema = InventoryDbItemSchema.extend({
  transactionDataType: z.literal('Inventory.Items.Adjust'),
  locationRoute: LocationRouteSchema,
});
export const InventoryTransactionItemSchema = InventoryDbItemSchema.extend({
  transactionDataType: z.enum([
    'Inventory.Items.Transfer',
    'Inventory.Items.Issue',
    'Inventory.Items.Adjust',
  ]),
  locationRoute: LocationRouteSchema.optional(),
});
// this is used for typing transactions and extracting data of any type of item transaction
export type InventoryTransactionItem = z.infer<typeof InventoryTransactionItemSchema>;

export const InventoryTransactionLocationCreateSchema = InventoryDbLocationSchema.extend({
  transactionDataType: z.literal('Inventory.Locations.Create'),
});
export const InventoryTransactionLocationModifySchema = InventoryDbLocationSchema.extend({
  transactionDataType: z.literal('Inventory.Locations.Modify'),
});
export const InventoryTransactionLocationSchema = InventoryDbLocationSchema.extend({
  transactionDataType: z.enum(['Inventory.Locations.Create', 'Inventory.Locations.Modify']),
});
// this is used for typing transactions and extracting data of any type of item transaction
export type InventoryTransactionLocation = z.infer<typeof InventoryTransactionLocationSchema>;

export const InventoryTransactionStockLocationCreateSchema = InventoryDbStockLocationSchema.extend({
  transactionDataType: z.literal('Inventory.StockLocations.Create'),
  productId: preprocessObjectId32,
  skuId: preprocessObjectId,
});
export const InventoryTransactionStockLocationModifySchema = InventoryDbStockLocationSchema.extend({
  transactionDataType: z.literal('Inventory.StockLocations.Modify'),
  productId: preprocessObjectId32,
  skuId: preprocessObjectId,
});
export const InventoryTransactionStockLocationSchema = InventoryDbStockLocationSchema.extend({
  transactionDataType: z.enum(['Inventory.StockLocations.Create', 'Inventory.StockLocations.Modify']),
  productId: preprocessObjectId32,
  skuId: preprocessObjectId,
});
// this is used for typing transactions and extracting data of any type of stockLocation transaction
export type InventoryTransactionStockLocation = z.infer<typeof InventoryTransactionStockLocationSchema>;

export const InventoryTransactionSkuCreateSchema = InventoryDbSKUSchema.extend({
  transactionDataType: z.literal('Inventory.Skus.Create'),
  productId: preprocessObjectId32,
});
export const InventoryTransactionSkuModifySchema = InventoryDbSKUSchema.extend({
  transactionDataType: z.literal('Inventory.Skus.Modify'),
  productId: preprocessObjectId32,
});
export const InventoryTransactionSkuSchema = InventoryDbSKUSchema.extend({
  transactionDataType: z.enum(['Inventory.Skus.Create', 'Inventory.Skus.Modify']),
  productId: preprocessObjectId32.optional(),
});
// this is used for typing transactions and extracting data of any type of sku transaction
export type InventoryTransactionSku = z.infer<typeof InventoryTransactionSkuSchema>;

export const InventoryTransactionProductCreateSchema = InventoryDbProductSchema.extend({
  transactionDataType: z.literal('Inventory.Products.Create'),
  skus: z.array(InventoryDbSKUSchema).default([]), // overwrite skus array so an empty array is not required.
});
// .omit({ skus: true });

export const InventoryTransactionProductModifySchema = InventoryDbProductSchema.extend({
  transactionDataType: z.literal('Inventory.Products.Modify'),
  skus: z.array(InventoryDbSKUSchema).optional(),
});
export const InventoryTransactionProductSchema = InventoryDbProductSchema.extend({
  transactionDataType: z.discriminatedUnion('transactionDataType', [
    InventoryTransactionProductCreateSchema,
    InventoryTransactionProductModifySchema,
  ]),
});
// this is used for typing transactions and extracting data of any type of product transaction
export type InventoryTransactionProduct = z.infer<typeof InventoryTransactionProductSchema>;

// InventoryItemSchema;

// export const TransactionStatusSchema = z.object({
//   status: z.union([z.literal('created'), z.literal('cancelled'), z.literal('executed')]),
//   datetime: preprocessDate,
//   createdBy: preprocessObjectId32, // user/object/... that created the status change
// });
// export type TransactionStatus = z.infer<typeof TransactionStatusSchema>;

export const InventoryTransactionSchema = z.object({
  _id: preprocessObjectId32, // ObjectID as string
  referenceId: preprocessObjectId32, // user, authuser
  transactionReference: z.string().optional().nullable().default(null), // external referenceID
  structureRootReferenceId: preprocessObjectId32.optional().nullable().default(null), // this should be the main process/project
  structureReferenceId: preprocessObjectId32.optional().nullable().default(null), // this is the specific structure container in that main process/project

  // statusChanges (schedule transactions only?) - if a user wants to 'modify' or 'deactivate' a transaction,
  // they should cancel it, and create a new one when ready
  // statusChanges: z.array(TransactionStatusSchema),
  createdOn: preprocessDate.default(null),
  createdById: preprocessObjectId32, // resource that affected the change, user, group, container, ...
  transactionData: z.discriminatedUnion('transactionDataType', [
    InventoryTransactionProductCreateSchema,
    InventoryTransactionProductModifySchema,
    InventoryTransactionSkuCreateSchema,
    InventoryTransactionSkuModifySchema,
    InventoryTransactionLocationCreateSchema,
    InventoryTransactionLocationModifySchema,
    InventoryTransactionItemTransferSchema,
    InventoryTransactionItemIssueSchema,
    InventoryTransactionItemAdjustSchema,
    InventoryTransactionStockLocationCreateSchema,
    InventoryTransactionStockLocationModifySchema,
  ]),
});

export type InventoryTransaction = z.infer<typeof InventoryTransactionSchema>;
