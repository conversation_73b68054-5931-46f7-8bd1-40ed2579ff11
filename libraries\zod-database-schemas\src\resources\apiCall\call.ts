// includes user api interface, api calls, ...
import { z } from 'zod/v4';
import { HTTP_CALL } from '../../common/enums';

import { preprocessObjectId32 } from '../../common';

export const ApiCallSchema = z.object({
  _id: preprocessObjectId32.optional(),
  isActive: z.number(),
  apiHostId: preprocessObjectId32, // link to host id
  httpCall: HTTP_CALL,
  endpoint: z.string(),
  headers: z.array(z.tuple([z.string(), z.string()])), // array of key value pair of headers
  // TODO M-1:  there is more, see appsmith datasources/restful api
});

export type ApiCall = z.infer<typeof ApiCallSchema>;
