import { z } from 'zod/v4';

import { preprocessObjectId32 } from '../../common';
import { EventSchema } from './event';

// https://lldcoding.com/design-google-calendar-database-model

export const CalendarSchema = z.object({
  // Define your schema here
  _id: preprocessObjectId32,
  name: z.string(),
  date: z.date(),
  events: z.array(EventSchema),
});

export type Calendar = z.infer<typeof CalendarSchema>;
