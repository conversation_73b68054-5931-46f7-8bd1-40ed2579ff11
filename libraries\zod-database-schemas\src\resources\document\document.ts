import { z } from 'zod/v4';

import { preprocessDate, preprocessObjectId32 } from '../../common';

import { DocumentVersionSchema } from './documentVersion';

export const DocumentSchema = z.object({
  _id: preprocessObjectId32,
  isActive: z.boolean(),
  name: z.string(),
  description: z.string(),
  tags: z.array(z.string()),
  createdById: preprocessObjectId32,
  createdOn: preprocessDate,
  documentVersions: z.array(DocumentVersionSchema),
});

export type Document = z.infer<typeof DocumentSchema>;
