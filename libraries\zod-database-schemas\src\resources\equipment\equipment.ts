import { z } from 'zod/v4';

import { preprocessDate, preprocessObjectId32 } from '../../common';

// Define the Equipment schema
export const EquipmentSchema = z.object({
  _id: preprocessObjectId32,
  name: z.string().min(1),
  description: z.string(),
  tags: z.array(z.string()).optional().nullable().default(null),
  typeId: preprocessObjectId32,
  status: z.enum(['active', 'inactive', 'maintenance', 'broken', 'lost', 'destroyed']),
  RACIId: preprocessObjectId32,
  purchaseDate: preprocessDate,
  lastServiceDate: preprocessDate,
});

// Define the Equipment type
export type Equipment = z.infer<typeof EquipmentSchema>;
