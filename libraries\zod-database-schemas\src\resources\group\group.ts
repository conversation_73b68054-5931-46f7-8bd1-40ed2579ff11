import { z } from 'zod/v4';

import { preprocessObjectId32 } from '../../common';
import { ContactSchema } from '../contact/contact';

/*
The group is the basis of organization in TNT for all resources.
Resources cannot exist outside of a group, this enables a standardization of usage and control as well as templating
Resources may include people, equipment, documents, data, checklists,
and should be organized by desired scope

There is a difference between groups and personas,
personas are made up only of users,
groups can be used to aggregate users, contacts, and/or entities
*/

export const GroupSchema = z.object({
  _id: preprocessObjectId32,
  isActive: z.boolean(),
  accountNum: z.string(),
  name: z.string(),
  tagIds: z.array(preprocessObjectId32), // Department, Team, ...
  description: z.string(),

  // membership resides in accessControl.iamObject database.collection by creating links
  // members: z.object({
  //   entities: z.array(EntitySchema),
  //   workers: z.array(ContactSchema),
  //   apis: z.array(z.array(z.tuple([ApiHostConnectionSchema, ApiCallSchema]))), //include applications and api calls
  // }),
});

// Historical Schemas are used when non-fixed organzational data (groups) are used to track changes during
// a projects lifetime yet when those tasks are complete, the data is desired to be locked and archived
// for future reference

export const GroupHistoricalSchema = z.object({
  name: z.string(),
  description: z.string(),
  accountNum: z.string(),
  tags: z.array(z.string()), // Department, Team, ...
  members: z.array(ContactSchema),
});

export type Group = z.infer<typeof GroupSchema>;
export type GroupHistorical = z.infer<typeof GroupHistoricalSchema>;
