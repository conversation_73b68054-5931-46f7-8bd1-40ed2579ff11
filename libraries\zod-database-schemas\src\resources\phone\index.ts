import { z } from 'zod/v4';

import { preprocessDate, preprocessObjectId32 } from '../../common';

export const PhoneSchema = z.object({
  // _id: objectIdFullyDescribed,
  number: z
    .string()
    .min(10, { message: 'Must be a valid phone number' })
    .max(14, { message: 'Must be a valid phone number' }), // or should this simply be a number of a specific length?
  typeId: preprocessObjectId32, //cell, office, fax, home  ref itemTypeSchema
  description: z.string(), // user description field
  isActive: z.boolean().default(true),
  validFrom: preprocessDate,
  validTo: preprocessDate,
});

//https://stackoverflow.com/questions/74193093/zod-validation-for-phone-numbers
// https://gitlab.com/catamphetamine/libphonenumber-js

export type Phone = z.infer<typeof PhoneSchema>;
