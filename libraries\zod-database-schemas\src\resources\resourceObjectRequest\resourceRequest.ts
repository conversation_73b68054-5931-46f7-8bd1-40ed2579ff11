import { z } from 'zod/v4';

import { preprocessDate, preprocessObjectId32 } from '../../common';
import { DocumentVersionSchema } from '../document';

// request used to allow builder to create templated questionairres for data entry/document submittal/...
// default is for entry through base UI
export const ResourceRequestSchema = z.object({
  _id: preprocessObjectId32,
  isActive: z.boolean(),
  // consider incorporating UI windows name in the base UI entry?
  requestString: z.string().default('Data entered through base UI'), // user display request string
  resourceObject: DocumentVersionSchema, // should use OR for all applicable resource schemas
  searchTags: z.array(z.string()),
  createdById: preprocessObjectId32,
  createdOn: preprocessDate,
  iamLinksToAdd: z.array(preprocessObjectId32),
});

export type ResourceRequest = z.infer<typeof ResourceRequestSchema>;
