import { z } from 'zod/v4';

import { StructureObjectBaseSchema } from '../../../../workData';

export const StructureObjectBaseTemplateSchema = StructureObjectBaseSchema.extend({
  structureObjectType: z.literal('structureObjectBaseTemplate'),
}).omit({
  statusId: true,
  // statusValue: true,
  isCompleted: true,
  completedTimeStamp: true,
  isSchedulingCurrent: true,
  schedule: true,
  sourceTemplateId: true,
  isTrackingSourceTemplate: true,
  isHold: true,
  holdOwners: true,
  RACIHistorical: true,
});

export type StructureObjectBaseTemplate = z.infer<typeof StructureObjectBaseTemplateSchema>;
