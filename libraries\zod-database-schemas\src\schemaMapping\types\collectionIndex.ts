// import { z } from 'zod/v4';
// import { JsonObjectSchema } from '../../common';

// // Mongo allows these values for index directions
// export const MongoIndexDirectionSchema = z.union([
//   z.literal(1),
//   z.literal(-1),
//   z.literal('2d'),
//   z.literal('2dsphere'),
//   z.literal('text'),
//   z.literal('hashed'),
//   z.literal('geoHaystack'),
// ]);

// // The "key" field: field names mapped to valid directions
// // export const MongoIndexKeySchema = z.record(z.string(), MongoIndexDirectionSchema);
// export const MongoIndexKeySchema = z.object({
//   field1: MongoIndexDirectionSchema,
//   field2: MongoIndexDirectionSchema,
// });

// // Common index options, with optional catch-all
// export const MongoIndexOptionsSchema = z.object({
//   name: z.string().optional(),
//   unique: z.boolean().optional(),
//   sparse: z.boolean().optional(),
//   expireAfterSeconds: z.number().optional(),
//   background: z.boolean().optional(),
// });

// // The full index definition
// export const MongoIndexDefinitionSchema = z.object({
//   key: MongoIndexKeySchema,
//   options: MongoIndexOptionsSchema.optional(),
// });

// export type MongoIndexDefinition = z.infer<typeof MongoIndexDefinitionSchema>;
