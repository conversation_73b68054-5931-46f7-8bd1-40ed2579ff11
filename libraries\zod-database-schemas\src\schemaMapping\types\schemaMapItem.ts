import type { z } from 'zod/v4';
import { JsonObject } from '@bufbuild/protobuf';

// export type SchemaMapItemType<T extends z.ZodTypeAny = z.ZodTypeAny> = {
//   referenceString: string; // used as filename for backup/restore
//   schema: T; // zod schema name
//   dbName: string; // database name in data server
//   dbId: string; // used as first prefix for objectId
//   collectionName: string; // collection name in database
//   collectionId: string; // used as second prefix for objectId
//   //dataGroupName: string | null;
//   serverUri: string;
//   // serverConfig?: {
//   //   host: string;
//   //   port: number;
//   //   user: string;
//   //   password: string;
//   //   connectionLimit: number;
//   //   trace: boolean;
//   // };
//   indexes?: Array<CollectionIndex>;
// };

export type SchemaMapItemType<T extends z.ZodTypeAny, R extends string> = {
  referenceString: string;
  referenceId: R;
  dbName: string;
  dbId: string;
  collectionName: string;
  collectionId: string;
  schema: T;
  // indexes: MongoIndexDefinition[] | [];
  indexes: JsonObject[] | [];
  serverUri: string;
};

// export type InferredSchemaMapItemType<T extends SchemaMapItemType<any>> = z.infer<T['schema']>;
