import { z } from 'zod/v4';

import { preprocessDate, preprocessObjectId32 } from '../common';

export const EnumTestSchema = z.object({
  _id: preprocessObjectId32,
  isActive: z.boolean().default(true),
  validFrom: preprocessDate,
  validTo: preprocessDate,
  name: z.string(),
  description: z.string(),
  tags: z.array(z.string()),
  costRate: z.number(),
  validDays: z.enum(['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']),
  validTimeStart: z.date(),
  validTimeStop: z.date(),
  // INFO would need to install package such as date-holidays,public-holidays
  includesHolidays: z.boolean().default(true),
});
