import { z } from 'zod';
import { ProductSchema } from './testSub1/myproduct';
import { AddressSchema } from './testSub1/testSub2/address';

// Simple base schemas for testing
export const UserSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string(),
});

// Test union with identifier references (our main fix)
export const UserProductUnionSchema = z.union([UserSchema, ProductSchema]);

// Test intersection with identifier references
export const UserAddressIntersectionSchema = z.intersection(UserSchema, AddressSchema);

// Test discriminated union with nested extend
// this fails as it does not extract this out due to the nested effects not being
// seen by the schemaWalker.
// export const UserProductDiscriminatedUnionSchema = z.discriminatedUnion('type', [
//   UserSchema.extend({ type: z.literal('user') }),
//   ProductSchema.extend({ type: z.literal('product') }),
// ]);

// this works, use this pattern.
export const discriminatedUserSchema = UserSchema.extend({ type: z.literal('user') });
export const discriminatedProductSchema = ProductSchema.extend({ type: z.literal('product') });

export const UserProductDiscriminatedUnion2Schema = z.discriminatedUnion('type', [
  discriminatedUserSchema,
  discriminatedProductSchema,
]);

// Test extend operation
export const UserExtendedSchema = UserSchema.extend({
  age: z.number(),
  isActive: z.boolean(),
});

// Test pick/omit operations
export const UserPickNameEmailSchema = UserSchema.pick({
  name: true,
  email: true,
});

export const UserOmitEmailSchema = UserSchema.omit({
  email: true,
});
