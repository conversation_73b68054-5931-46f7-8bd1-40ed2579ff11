// Do we need the protobuf Timestamp type here?
// De we have a site id, or a client id?
import { z } from 'zod/v4';

import { JsonSchema } from '../../common/json';
import { JsonValueSchema } from '../../common/functions';
import { LayoutSchema } from './layout';
import { preprocessObjectId32 } from '../../common';

export const ThemeSchema = z.object({
  _id: preprocessObjectId32,
  themeName: z.string(),
  layout: LayoutSchema,
  overrides: z.array(z.string()).optional(),
  palette: z.array(JsonSchema),
  typography: z.array(JsonValueSchema).optional(),
  spacing: z.number().optional(),
  zones: JsonSchema.optional(),
});

export type Theme = z.infer<typeof ThemeSchema>;
