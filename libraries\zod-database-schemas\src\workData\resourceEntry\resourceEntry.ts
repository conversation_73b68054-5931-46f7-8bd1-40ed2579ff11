import { z } from 'zod/v4';

import { preprocessObjectId32 } from '../../common';
// import { ResourceEntryDocumentSchema } from '../../document';
import { ResourceEntryInventoryTransactionItemSchema } from '../../inventory';
import { ResourceEntryDocumentSchema } from '../../resources/document/documentVersion';

// import { ResourceRequestSchema } from '../../resources/resourceObjectRequest';

// resources that may be entered would include non-inventory objects (documents, drawings, files, images, ...) entered from UI base or from container in a project
// therefore all resources entered will receive a log entry and the entry into the appropriate
// resource database:collection.
export const ResourceEntrySchema = z.object({
  _id: preprocessObjectId32,
  structureRootReferenceId: preprocessObjectId32.optional().nullable().default(null), // this should be the main process/project
  structureReferenceId: preprocessObjectId32.optional().nullable().default(null), // this is the specific structure container in that main process/project
  resourceId: preprocessObjectId32, // reference to the actual db _id of the resource/inventory item
  resourceEntered: z.discriminatedUnion('transactionDataType', [
    ResourceEntryDocumentSchema,
    ResourceEntryInventoryTransactionItemSchema,
  ]),
  // ROBJITEM itemGroup = WorkEntryStatus
  ResourceEntryStatusId: preprocessObjectId32,
  tags: z.array(z.string()),
});

export type ResourceEntry = z.infer<typeof ResourceEntrySchema>;
