import { z } from 'zod/v4';

import { preprocessObjectId32 } from '../../common';
import { AuthUserSchema } from '../../authentication';
//
export const WorkEntrySchema = z.object({
  _id: preprocessObjectId32,
  // to track and link a resource log entry we need to log the container it was entered under
  // and/or the contact/group/entity/.../resource who entered it
  structureRootReferenceId: preprocessObjectId32.optional().nullable(), // this should be the main process/project
  structureReferenceId: preprocessObjectId32.optional().nullable(), // this is the specific structure container in that main process/project
  resourceId: preprocessObjectId32, // what is this for?

  dateTimeStart: z.string().datetime({ offset: true }),
  dateTimeEnd: z.string().datetime({ offset: true }),
  weeks: z.number(),
  days: z.number(),
  time: z.string().time(),
  // costEa: z.number(),
  // units: z.number(),
  workBillingCode: z.string(),
  workAccomplished: z.string(),
  workIssues: z.string(),
  workNotes: z.string(),
  workRating: z.number().int(), // impression of work done by user entering data, typically a 1-5 (frown face --> happy face)
  workEntryStatusId: preprocessObjectId32,
  createdDateTime: z.date(),
  createdBy: AuthUserSchema,
  modifiedDateTime: z.date(),
  modifiedBy: AuthUserSchema,
});

export type WorkEntry = z.infer<typeof WorkEntrySchema>;
