{"name": "@tnt/core", "private": true, "scripts": {"build": "turbo run build --concurrency=20", "cleanUp": "powershell -Command \"Get-ChildItem -Recurse -Directory | Where-Object { $_.Name -in @('node_modules', 'dist', 'lib', 'build', '__generated__', '.turbo') } | Remove-Item -Recurse -Force\"", "cleanUp:bash": "find . -type d \\( -name 'node_modules' -o -name 'dist' -o -name 'lib' -o -name 'build' -o -name '__generated__' -o -name '.turbo' \\) -exec rm -rf {} +", "createEnv": "turbo run createEnv", "dev": "turbo dev --concurrency=20", "dev:appsmith": "turbo run dev --filter=@tnt/appsmith", "dev:servers": "turbo run dev --filter=./servers/* --concurrency=20", "dev:websites": "turbo run dev --filter=@tnt/app-web --concurrency 20", "eliza:dev": "turbo run dev --filter=@tnt/mongo-db --filter=@tnt/eliza-grpc --filter=@tnt/eliza-say", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "lint": "turbo run lint --continue", "prebuild": "turbo run prebuild", "start-service": "turbo run start-service", "stop-service": "turbo run stop-service", "test": "turbo run test --continue", "test:ci": "pnpm run lint build test", "tnt": "pnpm --filter @tnt/cli run build && node tools/tnt-cli/dist/index.js", "tnt:build": "turbo run prebuild && turbo run build --filter=@tnt/application-server --filter=@tnt/app-web", "tnt:dev": "turbo run dev --filter=@tnt/application-server --filter=@tnt/app-web", "tnt:lint": "turbo run lint --filter=@tnt/application-server --filter=@tnt/app-web  --continue", "tnt:seed": "pnpm -F @tnt/mongo-client createEnv && pnpm run -F @tnt/mongo-dbmaint dev:restore", "tnt:websites": "turbo run dev --filter=@tnt/app-web --filter=@tnt/www-web", "tnt:servers": "turbo run dev --filter=@tnt/application-server --filter=@tnt/auth-api"}, "packageManager": "pnpm@9.12.1", "devDependencies": {"@bufbuild/buf": "^1.54.0", "@bufbuild/protoc-gen-es": "^2.6.0", "@tnt/eslint-config": "workspace:*", "eslint": "^9.0.0", "jest": "29.7.0", "jiti": "^2.4.2", "prettier": "^3.4.2", "syncpack": "13.0.0", "turbo": "^2.2.3"}, "dependencies": {"@bufbuild/buf": "^1.54.0", "@bufbuild/protobuf": "^2.6.0", "@connectrpc/connect": "^2.0.2", "@bufbuild/protoc-gen-es": "^2.6.0", "@tnt/eslint-config": "workspace:*", "@tnt/protos-source": "workspace:*", "nodemon": "3.1.7", "yaml": "2.8.0"}, "engines": {"node": ">=22.13.0"}, "volta": {"node": "22.13.0", "pnpm": "9.12.1"}}