{"name": "@tnt/fsl-structures-service", "version": "1.0.0", "description": "Basic user management microservice using fastify-server-lib", "type": "module", "main": "./lib/index.js", "types": "./lib/index.d.ts", "scripts": {"build": "tsc", "dev": "nodemon", "start": "node ./lib/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "env:create": "tsx ./scripts/createEnv.ts", "type-check": "tsc --noEmit"}, "dependencies": {"@tnt/env-loader": "workspace:*", "@tnt/error-logger": "workspace:*", "@tnt/fastify-server-lib": "workspace:*", "@tnt/mongo-client": "workspace:*", "@tnt/protos-gen": "workspace:*", "@tnt/zod-client-schemas": "workspace:*", "@tnt/zod-database-schemas": "workspace:*", "bcrypt": "^5.1.0", "jsonwebtoken": "^9.0.0", "zod": "^3.25.74"}, "devDependencies": {"@tnt/eslint-config": "workspace:*", "@tnt/jest-presets": "workspace:*", "@tnt/typescript-config": "workspace:*", "@types/bcrypt": "^5.0.0", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.0", "@types/node": "^22.8.7", "jest": "^29.7.0", "nodemon": "^3.0.0", "ts-jest": "^29.2.5", "tsx": "^4.0.0", "typescript": "^5.6.3"}, "keywords": ["microservice", "fastify", "connect-rpc", "user-management", "tnt"], "author": "TNT Team", "repository": {"type": "git", "url": "https://github.com/your-org/tnt-microservices"}}