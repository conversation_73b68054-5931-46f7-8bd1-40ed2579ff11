import { env } from './environment.js';

// Service Constants
export const SERVICE_NAME = 'fsl-structures';
export const SERVICE_VERSION = '1.0.0';

// Server Configuration
export const SERVER_CONFIG = {
  HOST: env.SERVICE_HOST,
  PORT: env.SERVICE_PORT,
  ENV: env.NODE_ENV,
} as const;

// Database Configuration
// export const DATABASE_CONFIG = {
//   // URI: env.MONGO_URI_DEFAULT,
//   // NAME: env.MONGO_DATABASE_DEFAULT,
// } as const;

// Security Configuration
export const SECURITY_CONFIG = {
  JWT_SECRET: env.JWT_SECRET,
  JWT_EXPIRES_IN: env.JWT_EXPIRES_IN,
  PASSWORD_SALT_ROUNDS: env.PASSWORD_SALT_ROUNDS,
  MAX_LOGIN_ATTEMPTS: env.MAX_LOGIN_ATTEMPTS,
  ACCOUNT_LOCKOUT_TIME: env.ACCOUNT_LOCKOUT_TIME,
  API_KEY: env.API_KEY,
} as const;

// External Services
export const EXTERNAL_SERVICES = {
  AUTH_SERVICE_URL: env.AUTH_SERVICE_URL,
} as const;

// Observability Configuration
export const OBSERVABILITY_CONFIG = {
  LOG_LEVEL: env.LOG_LEVEL,
  ENABLE_TRACING: env.ENABLE_TRACING,
  METRICS_PORT: env.METRICS_PORT,
} as const;

// Default values and limits
export const DEFAULTS = {
  PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  REQUEST_TIMEOUT: 30000, // 30 seconds
  CONNECTION_TIMEOUT: 5000, // 5 seconds
} as const;

// Error messages
export const ERROR_MESSAGES = {
  STRUCTURE_NOT_FOUND: 'Structure Object not found',
  STRUCTURE_ALREADY_EXISTS: 'Structure Object already exists',
  INVALID_CREDENTIALS: 'Invalid credentials',
  ACCOUNT_LOCKED: 'Account is locked due to too many failed login attempts',
  VALIDATION_ERROR: 'Validation error',
  INTERNAL_SERVER_ERROR: 'Internal server error',
  UNAUTHORIZED: 'Unauthorized access',
  FORBIDDEN: 'Forbidden access',
  BAD_REQUEST: 'Bad request',
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  STRUCTURE_CREATED: 'Structure Object created successfully',
  STRUCTURE_UPDATED: 'Structure Object updated successfully',
  STRUCTURE_DELETED: 'Structure Object deleted successfully',
  LOGIN_SUCCESSFUL: 'Login successful',
  LOGOUT_SUCCESSFUL: 'Logout successful',
} as const;
