import { z } from 'zod';

const environmentSchema = z.object({
  // Server
  SERVICE_HOST: z.string().default('localhost'),
  SERVICE_PORT: z.string().default('8080').transform(Number),
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),

  // Database
  // MONGO_URI_DEFAULT: z.string().url(),
  // MONGO_DATABASE_DEFAULT: z.string(),

  // Security
  JWT_SECRET: z.string().min(32, 'JWT secret must be at least 32 characters'),
  API_KEY: z.string().optional(),

  // External Services
  AUTH_SERVICE_URL: z.string().url(),

  // Observability
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  ENABLE_TRACING: z
    .string()
    .default('false')
    .transform(val => val === 'true'),
  METRICS_PORT: z.string().default('9090').transform(Number),

  // Feature Flags
  FEATURE_ADVANCED_AUTH: z
    .string()
    .default('false')
    .transform(val => val === 'true'),
  FEATURE_CACHING: z
    .string()
    .default('false')
    .transform(val => val === 'true'),
  FEATURE_RATE_LIMITING: z
    .string()
    .default('true')
    .transform(val => val === 'true'),
  FEATURE_MONITORING: z
    .string()
    .default('true')
    .transform(val => val === 'true'),
  FEATURE_CIRCUIT_BREAKER: z
    .string()
    .default('true')
    .transform(val => val === 'true'),

  // User Service Specific
  PASSWORD_SALT_ROUNDS: z.string().default('12').transform(Number),
  JWT_EXPIRES_IN: z.string().default('24h'),
  MAX_LOGIN_ATTEMPTS: z.string().default('5').transform(Number),
  ACCOUNT_LOCKOUT_TIME: z.string().default('15m'),
});

export type Environment = z.infer<typeof environmentSchema>;

export function validateEnvironment(): Environment {
  const result = environmentSchema.safeParse(process.env);

  if (!result.success) {
    console.error('❌ Environment validation failed:');
    console.error(result.error.issues);
    process.exit(1);
  }

  return result.data;
}

export const env = validateEnvironment();
