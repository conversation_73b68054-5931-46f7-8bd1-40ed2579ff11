import { Logger } from '@tnt/error-logger';
import { StructureReadOperations } from '@tnt/mongo-client';
import { ERROR_MESSAGES } from '../../config/constants';
import {
  ListRequest,
  ListResponse,
  ReadRequest,
  ReadResponse,
} from '@tnt/protos-gen/src/__generated__/gen/crud_pb';

const logger = new Logger({ serviceName: 'basic-structure-service:get-structure' });

export async function getStructure(request: ReadRequest, context: any): Promise<ReadResponse> {
  const startTime = Date.now();

  try {
    // Validate request  ??
    if (request.entityType !== 'Structure') throw new Error('Invalid entity type');
    const structureObjectId = request.id;

    logger.info('Getting structure', {
      structureId: request.id,
      requestId: context.requestId,
      operation: 'getStructure',
    });

    const structure = await StructureReadOperations.getStructuralContents(structureObjectId, 4);

    if (!structure) {
      const duration = Date.now() - startTime;

      logger.warn('Structure not found', {
        structureId: request.id,
        requestId: context.requestId,
        duration,
      });

      throw new Error(ERROR_MESSAGES.STRUCTURE_NOT_FOUND);
    }

    const duration = Date.now() - startTime;

    if (structure.objects.length === 0) {
      logger.warn('Structure has no objects', {
        structureId: request.id,
        requestId: context.requestId,
        duration,
      });
    } else {
      logger.info('Structure retrieved successfully', {
        structureObjectsFound: structure.objects.length || 0,
        structureLinksFound: structure.links.length || 0,
        structureAssemblyDocumentsFound: structure.assemblyDocuments.length || 0,
        structureIamLinksFound: structure.iamLinks.length || 0,
        requestId: context.requestId,
        duration,
      });
    }

    // Convert structure to plain JSON object for protobuf Struct compatibility
    // guarantee your object is JSON-safe (e.g., to strip out Dates, Maps, etc.)
    // before passing it to the RPC framework.
    const plainStructure = JSON.parse(JSON.stringify(structure));
    return {
      $typeName: 'crud.v1.ReadResponse',
      data: plainStructure,
      found: true,
    };
  } catch (error) {
    const duration = Date.now() - startTime;

    logger.error('Failed to get structure', {
      error: error.message,
      stack: error.stack,
      structureId: request.id,
      requestId: context.requestId,
      duration,
      operation: 'getStructure',
    });

    throw new Error(`Failed to get structure: ${error.message}`);
  }
}

export async function listStructures(request: ListRequest, context: any): Promise<ListResponse> {
  const reg: ListResponse = {
    $typeName: 'crud.v1.ListResponse',
    items: [],
    totalCount: 0,
    nextCursor: '',
    hasMore: false,
  };
  return reg;
}
