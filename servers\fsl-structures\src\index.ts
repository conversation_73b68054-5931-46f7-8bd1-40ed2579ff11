// to build
// pnpm -F @tnt/fsl-structures build
// to start
// pnpm -F @tnt/fsl-structures dev
// to test
// health curl http://localhost:8446/health
// to test structure read endpoint
// curl -X POST http://localhost:8446/rpc/StructureService.Read -H "Content-Type: application/json" -d "{\"entityType\":\"Structure\",\"id\":\"WDATSOBJ681d59b1d2c5e833b2fc428a\"}"
// id for testing: WDATSOBJ681d59b1d2c5e833b2fc428a

import { loadConfig } from '@tnt/env-loader';

// Load environment configuration first
loadConfig();

// Import and start the server
import './server.js';

// Graceful shutdown handling
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Don't exit process - let the application handle it
});

process.on('uncaughtException', error => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Export types and handlers for testing
export * from './routes/routes.js';
export * from './handlers/index.js';
export type * from './types/index.js';
