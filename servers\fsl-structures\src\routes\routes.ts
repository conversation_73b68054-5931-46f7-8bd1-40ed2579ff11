import { CrudService } from '@tnt/protos-gen/src/__generated__/gen/crud_pb';
import type { ConnectRouter } from '@connectrpc/connect';
import { ConnectError, Code } from '@connectrpc/connect';

// Note: In a real implementation, these would be imported from @tnt/protos-gen
// For this example, we'll define them inline or use placeholder services

export function notImplementedRoutes(router: ConnectRouter): void {
  router.service(CrudService, {
    async read() {
      throw new ConnectError('Not implemented', Code.Unimplemented);
    },
    async create() {
      throw new ConnectError('Not implemented', Code.Unimplemented);
    },
    async update() {
      throw new ConnectError('Not implemented', Code.Unimplemented);
    },
    async delete() {
      throw new ConnectError('Not implemented', Code.Unimplemented);
    },
    async list() {
      throw new ConnectError('Not implemented', Code.Unimplemented);
    },
  });
}
