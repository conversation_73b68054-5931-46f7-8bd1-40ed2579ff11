import { RouteDefinition } from '@tnt/fastify-server-lib';
import { CrudService } from '@tnt/protos-gen/src/__generated__/gen/crud_pb';
import { getStructure, listStructures } from '../handlers';

export const structureRouteDefinitions: RouteDefinition[] = [
  {
    serviceName: 'CrudService',
    serviceType: CrudService,
    methodName: 'read',
    handler: async (request, context) => await getStructure(request, context),
    metadata: {
      description: 'Get structure by ID',
      tags: ['structure', 'read'],
      version: '1.0.0',
    },
  },
  //   {
  //     serviceName: 'CrudService',
  //     serviceType: CrudService,
  //     methodName: 'list',
  //     handler: async (request, context) => await listStructures(request, context),
  //     metadata: {
  //       description: 'List structures',
  //       tags: ['structure', 'list'],
  //       version: '1.0.0',
  //     },
  //   },
];
