import { CrudService } from '@tnt/protos-gen/src/__generated__/gen/crud_pb';
import type { ConnectRouter } from '@connectrpc/connect';
import { getStructure, listStructures } from '../handlers/structures/get';
import { RouteHandler } from '@tnt/fastify-server-lib';

// const structureReadService = CrudService.methods.filter(method => method.name === 'read');

/**
 * Registers Structure CRUD ConnectRPC routes using the generic CRUD protobuf service.
 */
export function createStructureRoutes(): RouteHandler {
  return (router: ConnectRouter): void => {
    router.service(CrudService, {
      async read(request, context) {
        // Delegate to your handler (get.ts)
        return await getStructure(request, context);
      },
      async list(request, context) {
        // Delegate to your handler (get.ts)
        return await listStructures(request, context);
      },
      // You can add other CRUD methods here (create, update, delete, list)
    });
  };
}

// export const structureRoutesArray = [
//  // Each function registers a complete service
//   (router: ConnectRouter) => {
//     router.service(CrudService, {
//       async read(request, context) { return await getStructure(request, context); },
//       async list(request, context) { return await listStructures(request, context); },
//     });
//   },
// ]

// export function structureRoutes(router: ConnectRouter): void {
//   router.service(CrudService, {
//     async read(request, context) {
//       // Delegate to your handler (get.ts)
//       return await getStructure(request, context);
//     },
//     // Add other CRUD methods here as needed
//   });
// }

// export function structureRoutes(router: ConnectRouter): void {
//   console.log('[RPC] Registered endpoint route:??');
//   // @ts-ignore
//   router.service(CrudService as any, {
//     // @ts-ignore
//     async read(request, context) {
//       return await getStructure(request, context);
//     },
//     // ... other methods
//   });
// }
