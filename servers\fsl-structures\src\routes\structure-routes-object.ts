import { CrudService, ReadRequest } from '@tnt/protos-gen/src/__generated__/gen/crud_pb';
import type { ConnectRouter, HandlerContext } from '@connectrpc/connect';
import { getStructure } from '../handlers/structures/get';

/**
 * Registers Structure CRUD ConnectRPC routes using the generic CRUD protobuf service.
 */
// export function createStructureRoutes() {
//   return (router: ConnectRouter): void => {
//     router.service(CrudService, {
//       async read(request, context) {
//         // Delegate to your handler (get.ts)
//         return await getStructure(request, context);
//       },
//       // You can add other CRUD methods here (create, update, delete, list)
//     });
//   };
// }

export function structureRoutes(router: ConnectRouter): void {
  router.service(CrudService, {
    async read(request, context) {
      // Delegate to your handler (get.ts)
      return await getStructure(request, context);
    },
    // Add other CRUD methods here as needed
  });
}

const structureRoutesObject = [
  {
    serviceDefinition: CrudService,
    implementation: {
      async read(request: ReadRequest, context: HandlerContext) {
        return await getStructure(request, context);
      },
      // ...other methods
    },
  },
  // ...other services
];
