import { z } from 'zod';
import { StructureDataType } from '@tnt/zod-database-schemas';

// Common schemas
export const idSchema = z.string().uuid();
export const emailSchema = z.string().email().max(255);
export const passwordSchema = z.string().min(8).max(100);
export const nameSchema = z.string().min(1).max(100);

// User role and status schemas
export const userRoleSchema = z.enum(['user', 'admin', 'moderator']);
export const userStatusSchema = z.enum(['active', 'inactive', 'suspended', 'pending']);

// Pagination schemas
export const paginationSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

// Request metadata schema
export const requestMetadataSchema = z
  .object({
    requestId: z.string().optional(),
    userId: z.string().optional(),
    timestamp: z.string().optional(),
    userAgent: z.string().optional(),
    ip: z.string().optional(),
  })
  .optional();

// User request schemas
export const createUserSchema = z.object({
  email: emailSchema,
  name: nameSchema,
  password: passwordSchema,
  role: userRoleSchema.default('user'),
  metadata: requestMetadataSchema,
});

export const getUserSchema = z.object({
  id: idSchema,
  metadata: requestMetadataSchema,
});

export const updateUserSchema = z.object({
  id: idSchema,
  updates: z.object({
    email: emailSchema.optional(),
    name: nameSchema.optional(),
    role: userRoleSchema.optional(),
    status: userStatusSchema.optional(),
    emailVerified: z.boolean().optional(),
  }),
  metadata: requestMetadataSchema,
});

export const deleteUserSchema = z.object({
  id: idSchema,
  metadata: requestMetadataSchema,
});

export const listUsersSchema = z.object({
  filters: z
    .object({
      email: z.string().optional(),
      name: z.string().optional(),
      role: userRoleSchema.optional(),
      status: userStatusSchema.optional(),
      emailVerified: z.boolean().optional(),
      createdAfter: z.string().datetime().optional(),
      createdBefore: z.string().datetime().optional(),
    })
    .optional(),
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
  metadata: requestMetadataSchema,
});

// Auth request schemas
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1), // Don't validate password complexity on login
  metadata: requestMetadataSchema,
});

export const logoutSchema = z.object({
  token: z.string().min(1),
  metadata: requestMetadataSchema,
});

export const validateTokenSchema = z.object({
  token: z.string().min(1),
  metadata: requestMetadataSchema,
});

// Health check schemas
export const healthCheckSchema = z.object({
  detailed: z.boolean().default(false),
  metadata: requestMetadataSchema,
});

// Export inferred types
export type CreateUserRequest = z.infer<typeof createUserSchema>;
export type GetUserRequest = z.infer<typeof getUserSchema>;
export type UpdateUserRequest = z.infer<typeof updateUserSchema>;
export type DeleteUserRequest = z.infer<typeof deleteUserSchema>;
export type ListUsersRequest = z.infer<typeof listUsersSchema>;
export type LoginRequest = z.infer<typeof loginSchema>;
export type LogoutRequest = z.infer<typeof logoutSchema>;
export type ValidateTokenRequest = z.infer<typeof validateTokenSchema>;
export type HealthCheckRequest = z.infer<typeof healthCheckSchema>;
export type PaginationParams = z.infer<typeof paginationSchema>;
export type RequestMetadata = z.infer<typeof requestMetadataSchema>;
