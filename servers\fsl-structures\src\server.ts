import {
  createMicroservice,
  createServerConfig,
  createHealthCheckH<PERSON><PERSON>,
  setupGracefulShutdown,
  type HttpRoute,
  RouteHandler,
} from '@tnt/fastify-server-lib';

import type { FastifyInstance } from 'fastify';
import { inspect } from 'util';
import { env, logFeatureStatus } from './config/index';
import { combineRoutes } from '@tnt/fastify-server-lib';
import { createStructureRoutes } from './routes/structure-routes-fns';
import { notImplementedRoutes } from './routes/routes';
import { serviceMonitorRoutes } from './routes/service/monitor';
import { baseDbOperations } from '@tnt/mongo-client';
import { SchemaMap } from '@tnt/zod-database-schemas';
import { ConnectRouter } from '@connectrpc/connect';
import { structureRouteDefinitions } from './routes/structure-route-definitions';

// Log feature status on startup
logFeatureStatus();

// Combine ConnectRPC routes (structure + other service routes)
const rpcRoutes: RouteHandler = combineRoutes(createStructureRoutes(), notImplementedRoutes);
console.log(`[DEBUG] rpcRoutes: ${createStructureRoutes.toString()}`);

// Create server configuration
const config = createServerConfig('fsl-structures-service', {
  host: env.SERVICE_HOST,
  port: env.SERVICE_PORT,
  enableLogging: true,
  loggerConfig: {
    level: env.LOG_LEVEL,
  },
});

// Create the microservice
const server = createMicroservice({
  config,
  routes: createStructureRoutes,
  rpcRouteDefinitions: structureRouteDefinitions,

  // CORS configuration
  cors: {
    origin: env.NODE_ENV === 'production' ? ['https://your-domain.com'] : '*',
    credentials: true,
    allowedHeaders: ['Authorization', 'Content-Type'],
    exposedHeaders: ['X-Request-ID'],
    maxAge: 2 * 60 * 60, // 2 hours
  },

  // Custom HTTP routes
  httpRoutes: serviceMonitorRoutes,

  // Pre-start setup
  beforeStart: async () => {
    console.log('🔧 Setting up Structure Service...');
    if (env.NODE_ENV === 'development') {
      console.log('🌱 validate database connection...');
      const myOps = new baseDbOperations(SchemaMap.WorkData_StructureObjects);
      const response = await myOps.initCheckConnection();
      if (!response) {
        throw new Error(`❌ Failed to connect to database: ${SchemaMap.WorkData_StructureObjects.dbName}`);
      }
    }
    console.log('✅ Structure Service configured and ready to start');
  },

  // Post-start setup
  afterStart: async (fastify: FastifyInstance, address: string) => {
    console.log(`🚀 Basic Structure Service running on ${address}`);
    console.log(`📊 Health check available at ${address}/health`);
    // fastify.printRoutes(); // <-- This will print all registered routes
    if (env.NODE_ENV === 'development') {
      // fastify.printRoutes(); // <-- This will print all registered routes
      // console.log(`🛠️  Development endpoints:`);
      // console.log(`   • Seed data: POST ${address}/dev/seed`);
      // console.log(`   • Clear data: POST ${address}/dev/clear`);
    }
    setupGracefulShutdown({
      stop: async () => {
        await fastify.close();
      },
    });
  },
});

// Start the server
(async () => {
  try {
    const address = await server.start();
    console.log(`✅ Structure Service started successfully on ${address}`);
  } catch (error) {
    console.error('❌ Failed to start Structure Service:', error);
    process.exit(1);
  }
})();

export { server };

// function getConnectRPCEndpoints(serviceDefinition: ServiceType): string[] {
//   const serviceName = serviceDefinition.typeName; // e.g., "mypackage.TestService"
//   const methods = Object.keys(serviceDefinition.methods);

//   return methods.map(method => `POST /${serviceName}/${method}`);
// }
