import { fastifyConnectPlugin } from '@connectrpc/connect-fastify';

// Create a Custom Service Registry
// Create a custom registry that integrates with your service registration
export class ConnectRPCServiceRegistry {
  private services = new Map<string, any>();

  register(server: any, serviceDefinition: ServiceType, implementation: any) {
    const serviceName = serviceDefinition.typeName;
    const methods = Object.keys(serviceDefinition.methods);

    // Store service info
    this.services.set(serviceName, {
      definition: serviceDefinition,
      implementation,
      methods,
      endpoints: methods.map(method => `POST /${serviceName}/${method}`),
    });

    // Register with ConnectRPC
    return server.register(fastifyConnectPlugin, {
      routes: router => {
        router.service(serviceDefinition, implementation);
      },
    });
  }

  getServices() {
    return Array.from(this.services.values());
  }

  getEndpoints() {
    return this.getServices().flatMap(service => service.endpoints);
  }

  getService(serviceName: string) {
    return this.services.get(serviceName);
  }
}

// // Usage
// const registry = new ConnectRPCServiceRegistry();

// // Register services through the registry
// await registry.register(server, TestService, implementation);

// // Get all registered services
// const allServices = registry.getServices();
// const allEndpoints = registry.getEndpoints();
