import { Logger } from '@tnt/error-logger';
import type {
  HealthCheck,
  HealthStatus,
  PaginationOptions,
  PaginatedResult,
} from '../types/common.service.types.js';
import {
  CreateRequest,
  CreateResponse,
  ReadRequest,
  ReadResponse,
  UpdateRequest,
  UpdateResponse,
  DeleteRequest,
  DeleteResponse,
  ListRequest,
  ListResponse,
} from '@tnt/protos-gen/src/__generated__/gen/crud_pb';
import { ERROR_MESSAGES } from '../config/constants.js';

const logger = new Logger({ serviceName: 'fsl-structures-service:structure-service' });

export class StructureService {
  constructor(private rpcClient: any) {} // Accept your RPC client instance

  async createStructure(data: any, context: any): Promise<any> {
    logger.debug('StructureService.createStructure called', { requestId: context.requestId });
    try {
      const request: CreateRequest = {
        $typeName: 'crud.v1.CreateRequest',
        entityType: 'Structure',
        data,
      };
      const response: CreateResponse = await this.rpcClient.create(request);
      logger.info('Structure created successfully', {
        structureId: response.id,
        requestId: context.requestId,
      });
      return response.data;
    } catch (error) {
      logger.error('Failed to create structure', { error: error.message, requestId: context.requestId });
      throw error;
    }
  }

  async getStructureById(id: string, context: any): Promise<any | null> {
    logger.debug('StructureService.getStructureById called', {
      structureId: id,
      requestId: context.requestId,
    });
    try {
      const request: ReadRequest = {
        $typeName: 'crud.v1.ReadRequest',
        entityType: 'Structure',
        id,
      };
      const response: ReadResponse = await this.rpcClient.read(request);
      if (!response.found || !response.data) {
        logger.warn('Structure not found', { structureId: id, requestId: context.requestId });
        return null;
      }
      return response.data;
    } catch (error) {
      logger.error('Failed to get structure by ID', {
        error: error.message,
        structureId: id,
        requestId: context.requestId,
      });
      throw error;
    }
  }

  async updateStructure(id: string, updates: any, context: any): Promise<any | null> {
    logger.debug('StructureService.updateStructure called', {
      structureId: id,
      updates,
      requestId: context.requestId,
    });
    try {
      const request: UpdateRequest = {
        $typeName: 'crud.v1.UpdateRequest',
        entityType: 'Structure',
        id,
        data: updates,
        partial: true,
      };
      const response: UpdateResponse = await this.rpcClient.update(request);
      if (!response.found || !response.data) {
        logger.warn('Structure not found for update', {
          structureId: id,
          requestId: context.requestId,
        });
        return null;
      }
      logger.info('Structure updated successfully', {
        structureId: id,
        requestId: context.requestId,
      });
      return response.data;
    } catch (error) {
      logger.error('Failed to update structure', {
        error: error.message,
        structureId: id,
        requestId: context.requestId,
      });
      throw error;
    }
  }

  async deleteStructure(id: string, context: any): Promise<boolean> {
    logger.debug('StructureService.deleteStructure called', {
      structureId: id,
      requestId: context.requestId,
    });
    try {
      const request: DeleteRequest = {
        $typeName: 'crud.v1.DeleteRequest',
        entityType: 'Structure',
        id,
      };
      const response: DeleteResponse = await this.rpcClient.delete(request);
      if (!response.found || !response.deleted) {
        logger.warn('Structure not found for delete', {
          structureId: id,
          requestId: context.requestId,
        });
        return false;
      }
      logger.info('Structure deleted successfully', {
        structureId: id,
        requestId: context.requestId,
      });
      return true;
    } catch (error) {
      logger.error('Failed to delete structure', {
        error: error.message,
        structureId: id,
        requestId: context.requestId,
      });
      throw error;
    }
  }

  async listStructures(
    filters?: any,
    pagination?: PaginationOptions,
    context?: any,
  ): Promise<PaginatedResult<any>> {
    logger.debug('StructureService.listStructures called', {
      filters,
      pagination,
      requestId: context?.requestId,
    });
    try {
      const request: ListRequest = {
        $typeName: 'crud.v1.ListRequest',
        entityType: 'Structure',
        filter: filters || {},
        sort: pagination?.sortBy ? { [pagination.sortBy]: pagination.sortOrder || 'asc' } : {},
        limit: pagination?.limit || 20,
        offset: ((pagination?.page || 1) - 1) * (pagination?.limit || 20),
        cursor: '',
      };
      const response: ListResponse = await this.rpcClient.list(request);
      logger.debug('Structures retrieved successfully', {
        count: response.items.length,
        total: response.totalCount,
        requestId: context?.requestId,
      });
      return {
        data: response.items,
        pagination: {
          page: pagination?.page || 1,
          limit: pagination?.limit || 20,
          total: response.totalCount,
          totalPages: Math.ceil(response.totalCount / (pagination?.limit || 20)),
          hasNext: response.hasMore,
          hasPrev: (pagination?.page || 1) > 1,
        },
      };
    } catch (error) {
      logger.error('Failed to list structures', {
        error: error.message,
        requestId: context?.requestId,
      });
      throw error;
    }
  }

  // Password and account lock/unlock logic is not relevant for Structure entity
}

// Usage in Fastify setup (e.g. in server.ts):
// import { StructureService } from './services/structure.service';
// const structureService = new StructureService(connectRpcClient);
