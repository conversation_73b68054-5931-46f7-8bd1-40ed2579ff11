import type { HealthStatus } from './common.service.types.js';

// Common API request metadata
export interface RequestMetadata {
  requestId?: string;
  userId?: string;
  timestamp?: string;
  userAgent?: string;
  ip?: string;
}

// Standard API response structure
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message: string;
  errors?: ValidationError[];
  metadata?: ResponseMetadata;
}

export interface ResponseMetadata {
  requestId?: string;
  timestamp: string;
  version: string;
  processingTime?: number;
}

export interface ValidationError {
  field: string;
  message: string;
  code?: string;
}

// Pagination types
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Auth API types
export interface LoginRequest {
  email: string;
  password: string;
  metadata?: RequestMetadata;
}

export interface LoginResponse {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface LogoutRequest {
  token: string;
  metadata?: RequestMetadata;
}

export interface LogoutResponse extends ApiResponse<null> {}

export interface ValidateTokenRequest {
  token: string;
  metadata?: RequestMetadata;
}

export interface ValidateTokenResponse {
  valid: boolean;
  user?: string;
  expiresAt?: Date;
  scope?: string[];
}

// Health check types
export interface HealthCheckRequest {
  detailed?: boolean;
  metadata?: RequestMetadata;
}

export interface HealthCheckResponse extends ApiResponse<HealthStatus> {}

// Re-export service types
export type { HealthStatus } from './common.service.types.js';
