import { ConnectRouter } from '@connectrpc/connect';
import { CrudService } from '@tnt/protos-gen/src/__generated__/gen/crud_pb';
import type { ServiceImpl } from '@connectrpc/connect';
import { getStructure } from '../../handlers';

// Cast CrudService to any compatible service type
// Provide a custom implementation that injects the entityType
// Register it under whatever endpoint path you want
// ConnectRPC allows you to register any service implementation
// against any service definition, as long as the method signatures match.
// Even simpler: Direct service casting with entity injection
export function registerEntityService<T extends DescService>(
  router: ConnectRouter,
  entityType: string,
  serviceDefinition: T,
  crudImpl: ServiceImpl<T>,
): void {
  const entityServiceImpl: ServiceImpl<T> = {
    async create(request: any, context?: any) {
      return await crudImpl.create(create(CreateRequestSchema, { ...request, entityType }), context);
    },

    async read(request: any, context?: any) {
      return await crudImpl.read(create(ReadRequestSchema, { ...request, entityType }), context);
    },

    async get(request: any, context?: any) {
      return await crudImpl.read(create(ReadRequestSchema, { ...request, entityType }), context);
    },

    async update(request: any, context?: any) {
      return await crudImpl.update(create(UpdateRequestSchema, { ...request, entityType }), context);
    },

    async delete(request: any, context?: any) {
      return await crudImpl.delete(create(DeleteRequestSchema, { ...request, entityType }), context);
    },

    async list(request: any, context?: any) {
      return await crudImpl.list(create(ListRequestSchema, { ...request, entityType }), context);
    },

    async batchCreate(request: any, context?: any) {
      return await crudImpl.batchCreate({ ...request, entityType }, context);
    },

    async batchUpdate(request: any, context?: any) {
      return await crudImpl.batchUpdate({ ...request, entityType }, context);
    },

    async batchDelete(request: any, context?: any) {
      return await crudImpl.batchDelete({ ...request, entityType }, context);
    },
  } as ServiceImpl<T>;

  // Cast the service definition and register
  router.service(serviceDefinition, entityServiceImpl);
}

// export function structureRoutesDirect(router: ConnectRouter): void {
//   router.service(CrudService, {
//     async read(request, context) {
//       // Delegate to your handler (get.ts)
//       return await getStructure(request, context);
//     },
//     // Add other CRUD methods here as needed
//   });
// }

// export function structureRoutes(router: ConnectRouter, crudImpl: ServiceImpl<typeof CrudService>): void {
//   console.log('[RPC] Registered endpoint: POST /rpc/StructureService.Read');
//   //
//   router.service(CrudService as any, {
//     // @ts-ignore
//     async read(request, context) {
//       return await crudImpl.read({ ...request, entityType: 'structure' }, context);
//     },
//     // @ts-ignore
//     async create(request, context) {
//       return await crudImpl.create({ ...request, entityType: 'structure' }, context);
//     },
//     // ... other methods
//   });
// }

// export function structureRoutes2(router: ConnectRouter): void {
//   console.log('[RPC] Registered endpoint: POST /rpc/StructureService.Read');
//   // @ts-ignore
//   router.service(CrudService as any, {
//     // @ts-ignore
//     async read(request, context) {
//       return await getStructure(request, context);
//     },
//     // ... other methods
//   });
// }

/*
// For full automation, you could even do:
const entities = ['user', 'product', 'structure'];
entities.forEach(entityType => {
  router.service(CrudService, createEntityImpl(entityType, crudImpl));
});
*/
