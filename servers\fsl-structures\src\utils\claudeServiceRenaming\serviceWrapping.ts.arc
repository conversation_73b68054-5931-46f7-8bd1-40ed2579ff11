// service-mapper.ts - Automated service mapping utility
import { ConnectRouter } from '@connectrpc/connect';
import { CrudService } from './gen/crud_pb';
import type { ServiceImpl } from '@connectrpc/connect';
import { create } from '@bufbuild/protobuf';
import {
  CreateRequestSchema,
  ReadRequestSchema,
  UpdateRequestSchema,
  DeleteRequestSchema,
  ListRequestSchema,
} from './gen/crud_pb';

// Generic function to create entity-specific service mappings
export function createEntityServiceMapping<T extends Record<string, any>>(
  entityType: string,
  serviceDefinition: T,
  crudImpl: ServiceImpl<typeof CrudService>,
): ServiceImpl<T> {
  const mapping: any = {};

  // Map common CRUD operations
  const operationMap = {
    create: 'create',
    read: 'read',
    get: 'read',
    update: 'update',
    delete: 'delete',
    list: 'list',
    batchCreate: 'batchCreate',
    batchUpdate: 'batchUpdate',
    batchDelete: 'batchDelete',
  };

  // Automatically map methods based on naming conventions
  for (const [methodName, methodDef] of Object.entries(serviceDefinition.methods || {})) {
    const lowerMethodName = methodName.toLowerCase();

    // Find matching CRUD operation
    const crudOperation = Object.entries(operationMap).find(([key]) => lowerMethodName.includes(key))?.[1];

    if (crudOperation && crudImpl[crudOperation]) {
      mapping[methodName] = async (request: any, context?: any) => {
        // Inject entity_type into request
        const crudRequest = { ...request, entityType };
        return await crudImpl[crudOperation](crudRequest, context);
      };
    }
  }

  return mapping;
}

// Even simpler: Direct service casting with entity injection
export function registerEntityService<T>(
  router: ConnectRouter,
  entityType: string,
  serviceDefinition: T,
  crudImpl: ServiceImpl<typeof CrudService>,
): void {
  const entityServiceImpl: ServiceImpl<T> = {
    async create(request: any, context?: any) {
      return await crudImpl.create(create(CreateRequestSchema, { ...request, entityType }), context);
    },

    async read(request: any, context?: any) {
      return await crudImpl.read(create(ReadRequestSchema, { ...request, entityType }), context);
    },

    async get(request: any, context?: any) {
      return await crudImpl.read(create(ReadRequestSchema, { ...request, entityType }), context);
    },

    async update(request: any, context?: any) {
      return await crudImpl.update(create(UpdateRequestSchema, { ...request, entityType }), context);
    },

    async delete(request: any, context?: any) {
      return await crudImpl.delete(create(DeleteRequestSchema, { ...request, entityType }), context);
    },

    async list(request: any, context?: any) {
      return await crudImpl.list(create(ListRequestSchema, { ...request, entityType }), context);
    },

    async batchCreate(request: any, context?: any) {
      return await crudImpl.batchCreate({ ...request, entityType }, context);
    },

    async batchUpdate(request: any, context?: any) {
      return await crudImpl.batchUpdate({ ...request, entityType }, context);
    },

    async batchDelete(request: any, context?: any) {
      return await crudImpl.batchDelete({ ...request, entityType }, context);
    },
  } as ServiceImpl<T>;

  // Cast the service definition and register
  router.service(serviceDefinition, entityServiceImpl);
}

// Usage examples:

// 1. Manual registration (what you wanted)
export function structureRoutes(router: ConnectRouter, crudImpl: ServiceImpl<typeof CrudService>): void {
  console.log('[RPC] Registered endpoint: POST /rpc/StructureService.Read');

  // Cast CrudService as StructureService and register with entity injection
  router.service(CrudService as any, {
    async create(request, context) {
      return await crudImpl.create(
        create(CreateRequestSchema, { ...request, entityType: 'structure' }),
        context,
      );
    },

    async read(request, context) {
      return await crudImpl.read(create(ReadRequestSchema, { ...request, entityType: 'structure' }), context);
    },

    async update(request, context) {
      return await crudImpl.update(
        create(UpdateRequestSchema, { ...request, entityType: 'structure' }),
        context,
      );
    },

    async delete(request, context) {
      return await crudImpl.delete(
        create(DeleteRequestSchema, { ...request, entityType: 'structure' }),
        context,
      );
    },

    async list(request, context) {
      return await crudImpl.list(create(ListRequestSchema, { ...request, entityType: 'structure' }), context);
    },

    // Add other methods as needed
  });
}

// 2. Automated registration
export function setupEntityRoutes(router: ConnectRouter, crudImpl: ServiceImpl<typeof CrudService>): void {
  // Register multiple entities automatically
  const entities = ['user', 'product', 'order', 'structure'];

  entities.forEach(entityType => {
    registerEntityService(router, entityType, CrudService, crudImpl);
  });
}

// 3. Dynamic service creation from config
interface EntityConfig {
  name: string;
  endpoint: string;
  methods?: string[];
}

export function createEntityServicesFromConfig(
  router: ConnectRouter,
  crudImpl: ServiceImpl<typeof CrudService>,
  entities: EntityConfig[],
): void {
  entities.forEach(entity => {
    const entityServiceImpl: any = {};

    // Default methods if not specified
    const methods = entity.methods || ['create', 'read', 'update', 'delete', 'list'];

    methods.forEach(method => {
      entityServiceImpl[method] = async (request: any, context?: any) => {
        const crudRequest = { ...request, entityType: entity.name };
        return await crudImpl[method](crudRequest, context);
      };
    });

    // Register with custom endpoint
    router.service(CrudService, entityServiceImpl);
  });
}

// 4. Runtime service generation with file manipulation
import { writeFileSync, readFileSync, unlinkSync } from 'fs';
import { join } from 'path';

export function generateTempServiceFile(entityType: string, originalServicePath: string): string {
  const originalContent = readFileSync(originalServicePath, 'utf8');

  // Simple string replacement
  const newContent = originalContent
    .replace(/CrudService/g, `${entityType.charAt(0).toUpperCase() + entityType.slice(1)}Service`)
    .replace(/crud\.v1/g, `${entityType}.v1`)
    .replace(/package crud\.v1/g, `package ${entityType}.v1`);

  const tempPath = join(__dirname, `temp_${entityType}_service.proto`);
  writeFileSync(tempPath, newContent);

  return tempPath;
}

export function cleanupTempFiles(entityTypes: string[]): void {
  entityTypes.forEach(entityType => {
    const tempPath = join(__dirname, `temp_${entityType}_service.proto`);
    try {
      unlinkSync(tempPath);
    } catch (error) {
      // File doesn't exist, ignore
    }
  });
}

// 5. Complete automated setup
export function setupAutomatedCrudServices(
  router: ConnectRouter,
  crudImpl: ServiceImpl<typeof CrudService>,
): void {
  const entities = ['user', 'product', 'order', 'structure'];

  entities.forEach(entityType => {
    console.log(`[RPC] Setting up ${entityType} service endpoints`);

    // Option A: Direct casting and registration
    const serviceImpl: ServiceImpl<typeof CrudService> = {
      async create(request, context) {
        return await crudImpl.create({ ...request, entityType }, context);
      },
      async read(request, context) {
        return await crudImpl.read({ ...request, entityType }, context);
      },
      async update(request, context) {
        return await crudImpl.update({ ...request, entityType }, context);
      },
      async delete(request, context) {
        return await crudImpl.delete({ ...request, entityType }, context);
      },
      async list(request, context) {
        return await crudImpl.list({ ...request, entityType }, context);
      },
      async batchCreate(request, context) {
        return await crudImpl.batchCreate({ ...request, entityType }, context);
      },
      async batchUpdate(request, context) {
        return await crudImpl.batchUpdate({ ...request, entityType }, context);
      },
      async batchDelete(request, context) {
        return await crudImpl.batchDelete({ ...request, entityType }, context);
      },
    };

    // Register under the same service definition but with entity-specific implementation
    router.service(CrudService, serviceImpl);
  });
}

// Usage in your main server file:
export function setupServer(crudImpl: ServiceImpl<typeof CrudService>) {
  const router = ConnectRouter();

  // Register generic CRUD service
  router.service(CrudService, crudImpl);

  // Register entity-specific services automatically
  setupAutomatedCrudServices(router, crudImpl);

  return router;
}
