import { randomUUID } from 'crypto';
import type { RequestMetadata } from '../types/api.types.js';

/**
 * Creates a service context from request metadata
 */
// export function createServiceContext(metadata?: RequestMetadata): ServiceContext {
//   return {
//     requestId: metadata?.requestId || randomUUID(),
//     userId: metadata?.userId,
//     timestamp: new Date(),
//     metadata: metadata ? { ...metadata } : undefined,
//   };
// }

/**
 * Extracts user context from service context
 */
// export function getUserContext(context: ServiceContext): { userId?: string; userRole?: string } {
//   return {
//     userId: context.userId,
//     userRole: context.metadata?.userRole,
//   };
// }

/**
 * Creates request metadata for service calls
 */
export function createRequestMetadata(
  requestId: string,
  userId?: string,
  additional?: Record<string, any>,
): RequestMetadata {
  return {
    requestId,
    userId,
    timestamp: new Date().toISOString(),
    ...additional,
  };
}
