// 1. Access ConnectRPC Plugin State
function getRegisteredConnectRPCServices(server: any) {
  // ConnectRPC plugin stores registered services in the server context
  // The exact path may vary depending on the plugin version
  const connectContext = server.connectrpc || server.connect;

  if (!connectContext) {
    console.warn('ConnectRPC plugin not found or not registered');
    return [];
  }

  // Extract service information from the plugin state
  const services = [];
  if (connectContext.services) {
    for (const [serviceName, serviceInfo] of Object.entries(connectContext.services)) {
      services.push({
        serviceName,
        typeName: serviceInfo.typeName,
        methods: Object.keys(serviceInfo.methods || {}),
        endpoints: Object.keys(serviceInfo.methods || {}).map(
          method => `POST /${serviceInfo.typeName}/${method}`,
        ),
      });
    }
  }

  return services;
}

// Usage
// const registeredServices = getRegisteredConnectRPCServices(server);
// console.log('Registered ConnectRPC services:', registeredServices);

// 2. Hook into Route Registration
// Intercept the route registration process
function createConnectRPCRouteTracker() {
  const registeredServices = new Map();

  return {
    trackService(serviceDefinition: ServiceType, implementation: any) {
      const serviceName = serviceDefinition.typeName;
      const methods = Object.keys(serviceDefinition.methods);

      registeredServices.set(serviceName, {
        definition: serviceDefinition,
        implementation,
        methods,
        endpoints: methods.map(method => `POST /${serviceName}/${method}`),
      });

      return { serviceDefinition, implementation };
    },

    getRegisteredServices() {
      return Array.from(registeredServices.values());
    },

    getServiceEndpoints() {
      const allEndpoints = [];
      for (const service of registeredServices.values()) {
        allEndpoints.push(...service.endpoints);
      }
      return allEndpoints;
    },
  };
}

// Usage
// const routeTracker = createConnectRPCRouteTracker();

// await server.register(fastifyConnectPlugin, {
//   routes: (router) => {
//     // Track and register services
//     const { serviceDefinition, implementation } = routeTracker.trackService(TestService, {
//       async someMethod(request) {
//         return { /* response */ };
//       }
//     });

//     router.service(serviceDefinition, implementation);
//   }
// });

// Get registered services
// const registeredServices = routeTracker.getRegisteredServices();

// 3. Inspect Fastify's Internal Route Table
function extractConnectRPCRoutesFromFastify(server: any) {
  const routes = [];

  // Access Fastify's internal route table
  const routeTable = server.fastify?.routes || server.routes;

  if (routeTable) {
    for (const route of routeTable) {
      // ConnectRPC routes typically have specific patterns
      if (route.method === 'POST' && route.path.includes('/')) {
        const pathParts = route.path.split('/');
        if (pathParts.length >= 3) {
          // Likely format: /package.ServiceName/MethodName
          const serviceName = pathParts[1];
          const methodName = pathParts[2];

          if (serviceName && methodName) {
            routes.push({
              serviceName,
              methodName,
              fullPath: route.path,
              method: route.method,
            });
          }
        }
      }
    }
  }

  return routes;
}

// 4. Use Fastify Hooks to Track Registration
function setupConnectRPCTracking(server: any) {
  const registeredServices = new Map();

  // Hook into route registration
  server.addHook('onRoute', routeOptions => {
    // Check if this looks like a ConnectRPC route
    if (routeOptions.method === 'POST' && routeOptions.path.includes('/')) {
      const pathParts = routeOptions.path.split('/');
      if (pathParts.length >= 3) {
        const serviceName = pathParts[1];
        const methodName = pathParts[2];

        if (!registeredServices.has(serviceName)) {
          registeredServices.set(serviceName, {
            serviceName,
            methods: [],
            endpoints: [],
          });
        }

        const service = registeredServices.get(serviceName);
        service.methods.push(methodName);
        service.endpoints.push(`${routeOptions.method} ${routeOptions.path}`);
      }
    }
  });

  return {
    getRegisteredServices() {
      return Array.from(registeredServices.values());
    },
  };
}

// Setup before registering services
// const serviceTracker = setupConnectRPCTracking(server);

// // Register your services...
// await server.register(fastifyConnectPlugin, {
//   routes: (router) => {
//     router.service(TestService, implementation);
//   }
// });

// Get registered services
// const registeredServices = serviceTracker.getRegisteredServices();

// 5. Direct Plugin State Access (Most Reliable)

async function getConnectRPCServicesFromPlugin(server: any) {
  // Wait for server to be ready
  await server.ready();

  // Access the ConnectRPC plugin's internal state
  // This might vary based on the plugin version
  const pluginState = server.connectrpc || server.connect || server.plugins?.connectrpc;

  if (!pluginState) {
    throw new Error('ConnectRPC plugin not found');
  }

  // Extract service information
  const services = [];

  // Check different possible locations for service registry
  const serviceRegistry = pluginState.serviceRegistry || pluginState.services || pluginState.handlers;

  if (serviceRegistry) {
    for (const [path, handler] of Object.entries(serviceRegistry)) {
      const pathParts = path.split('/');
      if (pathParts.length >= 3) {
        const serviceName = pathParts[1];
        const methodName = pathParts[2];

        services.push({
          serviceName,
          methodName,
          path,
          handler: typeof handler,
        });
      }
    }
  }

  return services;
}

// Usage
// try {
//   const services = await getConnectRPCServicesFromPlugin(server);
//   console.log('Registered ConnectRPC services:', services);
// } catch (error) {
//   console.error('Failed to get services:', error.message);
// }

// 6. Create a Custom Service Registry
// Create a custom registry that integrates with your service registration
class ConnectRPCServiceRegistry {
  private services = new Map<string, any>();

  register(server: any, serviceDefinition: ServiceType, implementation: any) {
    const serviceName = serviceDefinition.typeName;
    const methods = Object.keys(serviceDefinition.methods);

    // Store service info
    this.services.set(serviceName, {
      definition: serviceDefinition,
      implementation,
      methods,
      endpoints: methods.map(method => `POST /${serviceName}/${method}`),
    });

    // Register with ConnectRPC
    return server.register(fastifyConnectPlugin, {
      routes: router => {
        router.service(serviceDefinition, implementation);
      },
    });
  }

  getServices() {
    return Array.from(this.services.values());
  }

  getEndpoints() {
    return this.getServices().flatMap(service => service.endpoints);
  }

  getService(serviceName: string) {
    return this.services.get(serviceName);
  }
}

// // Usage
// const registry = new ConnectRPCServiceRegistry();

// // Register services through the registry
// await registry.register(server, TestService, implementation);

// // Get all registered services
// const allServices = registry.getServices();
// const allEndpoints = registry.getEndpoints();
