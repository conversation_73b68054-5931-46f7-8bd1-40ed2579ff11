{
  "extends": "@tnt/typescript-config/base.json",
  "compilerOptions": {
    "allowJs": true,
    "esModuleInterop": true,
    "isolatedModules": true,
    "forceConsistentCasingInFileNames": true,
    "lib": [
      "dom",
      "dom.iterable",
      "ES2022"
    ],
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "outDir": "./dist",
    "rootDir": "./src",
    "resolveJsonModule": true,
    "skipLibCheck": true,
    "sourceMap": true,
    "strict": true,
    "target": "ES2022",
    "types": [
      "node",
      "jest"
    ],
  },
  "exclude": [
    "dist",
    "lib",
    "build",
    "node_modules",
    "scripts"
  ],
  "include": [
    "./src/**/*.ts",
    "__generated__/**/*.ts",
  ],
  "ts-node": {
    "esm": true,
    "experimentalSpecifierResolution": "node"
  }
}