const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('=== ZOD VERSION ANALYSIS ===\n');

// Check what Zod v4 actually is
try {
  const zodV4Info = execSync('npm view zod@^4.0.0-beta dist-tags', { encoding: 'utf-8' });
  console.log('Zod v4 Beta Info:', zodV4Info);
} catch (e) {
  console.log('Zod v4 Beta not available as ^4.0.0-beta');
}

// Check specific beta version
try {
  const latestBeta = execSync('npm view zod@4.0.0-beta.20250505T195954 version', { encoding: 'utf-8' });
  console.log('Latest Zod 4.0 Beta Version:', latestBeta.trim());
} catch (e) {
  console.log('Could not fetch specific beta version');
}

// Check latest stable
try {
  const latestStable = execSync('npm view zod@latest version', { encoding: 'utf-8' });
  console.log('Latest Zod Stable Version:', latestStable.trim());
} catch (e) {
  console.log('Could not fetch latest stable version');
}

console.log('\n=== RECOMMENDATIONS ===');
console.log('Current state:');
console.log('- Most projects use: ^3.25.67');
console.log('- zod-forms uses: ^3.25.74 (latest stable)');
console.log('- Code uses imports from "zod/v4" which works in v3.25+');
console.log('\nRecommendations:');
console.log('1. STAY WITH ZOD v3: Update all to ^3.25.74 (latest stable)');
console.log('2. MOVE TO ZOD v4: Use 4.0.0-beta.20250505T195954 (very risky for production)');
