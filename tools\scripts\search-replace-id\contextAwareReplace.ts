import fs from 'fs';
import path from 'path';
import glob from 'glob';
import readline from 'readline';
import renames from './field-renames.json';

const workspaceDir = path.resolve(__dirname, '../../../..');
const confirmMode = !process.argv.includes('--donotconfirm');

function askUser(question) {
  const rl = readline.createInterface({ input: process.stdin, output: process.stdout });
  return new Promise(resolve =>
    rl.question(question, answer => {
      rl.close();
      resolve(answer.trim().toLowerCase());
    }),
  );
}

glob(`${workspaceDir}/**/*.{ts,js,tsx,jsx}`, { ignore: '**/node_modules/**' }, async (err, files) => {
  if (err) throw err;

  for (const file of files) {
    let content = fs.readFileSync(file, 'utf8');
    let originalContent = content;
    let changed = false;

    for (const { oldName, newName } of renames) {
      // Simple context-aware pattern: whole word
      const regex = new RegExp(`\\b${oldName}\\b`, 'g');
      let match;
      while ((match = regex.exec(content)) !== null) {
        // Show context around the match
        const start = Math.max(0, match.index - 30);
        const end = Math.min(content.length, match.index + oldName.length + 30);
        const context = content.substring(start, end);

        let doReplace = true;
        if (confirmMode) {
          const answer = await askUser(
            `Replace "${oldName}" with "${newName}" in:\n...${context}...\n(Y/n)? `,
          );
          doReplace = answer === '' || answer === 'y';
        }

        if (doReplace) {
          content =
            content.substring(0, match.index) + newName + content.substring(match.index + oldName.length);
          changed = true;
          // Move regex lastIndex forward to avoid infinite loop
          regex.lastIndex = match.index + newName.length;
        } else {
          // Move regex lastIndex forward to avoid infinite loop
          regex.lastIndex = match.index + oldName.length;
        }
      }
    }

    if (changed) {
      fs.writeFileSync(file, content, 'utf8');
      console.log(`Updated: ${file}`);
    }
  }

  console.log('Context-aware replacements complete.');
});
