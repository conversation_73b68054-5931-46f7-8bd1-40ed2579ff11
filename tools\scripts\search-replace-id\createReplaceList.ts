// to execute script pnpm exec tsx ./tools/scripts/search-replace-id/createReplaceList.ts

import fs from 'fs';
import glob from 'glob';

const regex = /^(?!\s*_id\s*:)(?!\s*[a-zA-Z0-9_]+Id\s*:)\s*([a-zA-Z0-9_]+)\s*:\s*preprocessObjectId32\b/m;
//
// this is vs code regex search field
// ^(?!\s*_id\s*:)(?!\s*[a-zA-Z0-9_]+Id\s*:)\s*([a-zA-Z0-9_]+)\s*:\s*preprocessObjectId32\b
// files to include
// zod-database-schemas/src/**/*.{ts,tsx}

const schemaDir = 'libraries/zod-database-schemas/src'; // adjust as needed

glob(`${schemaDir}/**/*.ts`, {}, (err, files) => {
  if (err) throw err;
  const results: Array<{ file: string; line: number; oldName: string; newName: string }> = [];
  files.forEach(file => {
    const content = fs.readFileSync(file, 'utf8');
    content.split('\n').forEach((line, idx) => {
      const match = line.match(regex);
      if (match) {
        const oldName = match[1];
        results.push({
          file,
          line: idx + 1,
          oldName,
          newName: `${oldName}Id`,
        });
      }
    });
  });
  fs.writeFileSync('./tools/scripts/search-replace-id/field-renames.json', JSON.stringify(results, null, 2));
  console.log('Done! See ./tools/scripts/search-replace-id/field-renames.json');
});
