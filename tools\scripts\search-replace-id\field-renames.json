[{"file": "libraries/zod-database-schemas/src/accessControl/iamLink.ts", "line": 10, "oldName": "connectFromField", "newName": "connectFromFieldId"}, {"file": "libraries/zod-database-schemas/src/accessControl/iamLink.ts", "line": 13, "oldName": "connectToField", "newName": "connectToFieldId"}, {"file": "libraries/zod-database-schemas/src/accessControl/iamLink.ts", "line": 15, "oldName": "profile", "newName": "profileId"}, {"file": "libraries/zod-database-schemas/src/application/dbAdmin/regexRules.ts", "line": 18, "oldName": "modifiedBy", "newName": "modifiedById"}, {"file": "libraries/zod-database-schemas/src/inventory/commandSchemas/item.ts", "line": 18, "oldName": "id1Name", "newName": "id1NameId"}, {"file": "libraries/zod-database-schemas/src/inventory/commandSchemas/item.ts", "line": 20, "oldName": "id2Name", "newName": "id2NameId"}, {"file": "libraries/zod-database-schemas/src/inventory/commandSchemas/item.ts", "line": 22, "oldName": "id3Name", "newName": "id3NameId"}, {"file": "libraries/zod-database-schemas/src/inventory/commandSchemas/item.ts", "line": 25, "oldName": "units", "newName": "unitsId"}, {"file": "libraries/zod-database-schemas/src/inventory/commandSchemas/item.ts", "line": 26, "oldName": "qualityControlStatus", "newName": "qualityControlStatusId"}, {"file": "libraries/zod-database-schemas/src/inventory/commandSchemas/location.ts", "line": 11, "oldName": "entity", "newName": "entityId"}, {"file": "libraries/zod-database-schemas/src/inventory/commandSchemas/price.ts", "line": 7, "oldName": "currency", "newName": "currencyId"}, {"file": "libraries/zod-database-schemas/src/inventory/commandSchemas/scheduledTransaction.ts", "line": 15, "oldName": "created<PERSON>y", "newName": "createdById"}, {"file": "libraries/zod-database-schemas/src/inventory/commandSchemas/sku.ts", "line": 47, "oldName": "color", "newName": "colorId"}, {"file": "libraries/zod-database-schemas/src/inventory/commandSchemas/sku.ts", "line": 48, "oldName": "size", "newName": "sizeId"}, {"file": "libraries/zod-database-schemas/src/inventory/commandSchemas/sku.ts", "line": 52, "oldName": "weightUnits", "newName": "weightUnitsId"}, {"file": "libraries/zod-database-schemas/src/inventory/commandSchemas/sku.ts", "line": 58, "oldName": "dimensionsUnits", "newName": "dimensionsUnitsId"}, {"file": "libraries/zod-database-schemas/src/inventory/transactionsSchemas/transaction.ts", "line": 152, "oldName": "created<PERSON>y", "newName": "createdById"}, {"file": "libraries/zod-database-schemas/src/resources/address/index.ts", "line": 11, "oldName": "type", "newName": "typeId"}, {"file": "libraries/zod-database-schemas/src/resources/apiCall/call.ts", "line": 10, "oldName": "apiHost", "newName": "apiHostId"}, {"file": "libraries/zod-database-schemas/src/resources/calendar/calendar.ts", "line": 10, "oldName": "id", "newName": "idId"}, {"file": "libraries/zod-database-schemas/src/resources/calendar/event.ts", "line": 11, "oldName": "calendar_id", "newName": "calendar_idId"}, {"file": "libraries/zod-database-schemas/src/resources/contact/contact.ts", "line": 21, "oldName": "organization", "newName": "organizationId"}, {"file": "libraries/zod-database-schemas/src/resources/contact/contact.ts", "line": 22, "oldName": "department", "newName": "departmentId"}, {"file": "libraries/zod-database-schemas/src/resources/contact/contact.ts", "line": 23, "oldName": "team", "newName": "teamId"}, {"file": "libraries/zod-database-schemas/src/resources/document/document.ts", "line": 13, "oldName": "created<PERSON>y", "newName": "createdById"}, {"file": "libraries/zod-database-schemas/src/resources/document/documentVersion.ts", "line": 11, "oldName": "fileType", "newName": "fileTypeId"}, {"file": "libraries/zod-database-schemas/src/resources/document/documentVersion.ts", "line": 13, "oldName": "created<PERSON>y", "newName": "createdById"}, {"file": "libraries/zod-database-schemas/src/resources/entity/entity.ts", "line": 15, "oldName": "type", "newName": "typeId"}, {"file": "libraries/zod-database-schemas/src/resources/entity/entity.ts", "line": 16, "oldName": "parent", "newName": "parentId"}, {"file": "libraries/zod-database-schemas/src/resources/entity/entity.ts", "line": 17, "oldName": "address", "newName": "addressId"}, {"file": "libraries/zod-database-schemas/src/resources/entity/entity.ts", "line": 20, "oldName": "created<PERSON>y", "newName": "createdById"}, {"file": "libraries/zod-database-schemas/src/resources/equipment/equipment.ts", "line": 11, "oldName": "type", "newName": "typeId"}, {"file": "libraries/zod-database-schemas/src/resources/equipment/equipment.ts", "line": 13, "oldName": "RACI", "newName": "RACIId"}, {"file": "libraries/zod-database-schemas/src/resources/phone/index.ts", "line": 11, "oldName": "type", "newName": "typeId"}, {"file": "libraries/zod-database-schemas/src/resources/resourceObjectRequest/resourceRequest.ts", "line": 15, "oldName": "created<PERSON>y", "newName": "createdById"}, {"file": "libraries/zod-database-schemas/src/workData/assemblyDocument/bomItem.ts", "line": 35, "oldName": "item", "newName": "itemId"}, {"file": "libraries/zod-database-schemas/src/workData/resourceEntry/resourceEntry.ts", "line": 23, "oldName": "ResourceEntryStatus", "newName": "ResourceEntryStatusId"}, {"file": "libraries/zod-database-schemas/src/workData/structures/structureLink.ts", "line": 19, "oldName": "source", "newName": "sourceId"}, {"file": "libraries/zod-database-schemas/src/workData/structures/structureLink.ts", "line": 22, "oldName": "target", "newName": "targetId"}, {"file": "libraries/zod-database-schemas/src/workData/structures/subSchemas/base.ts", "line": 49, "oldName": "schemeItem", "newName": "schemeItemId"}, {"file": "libraries/zod-database-schemas/src/workData/structures/subSchemas/base.ts", "line": 90, "oldName": "status", "newName": "statusId"}, {"file": "libraries/zod-database-schemas/src/workData/structures/subSchemas/base.ts", "line": 111, "oldName": "sourceTemplate", "newName": "sourceTemplateId"}, {"file": "libraries/zod-database-schemas/src/workData/structures/subSchemas/base.ts", "line": 120, "oldName": "RACI", "newName": "RACIId"}, {"file": "libraries/zod-database-schemas/src/workData/structures/subSchemas/base.ts", "line": 126, "oldName": "agileTaskName", "newName": "agileTaskNameId"}, {"file": "libraries/zod-database-schemas/src/workData/structures/subSchemas/base.ts", "line": 132, "oldName": "apiPreExecutionCallDefinition", "newName": "apiPreExecutionCallDefinitionId"}, {"file": "libraries/zod-database-schemas/src/workData/structures/subSchemas/base.ts", "line": 133, "oldName": "apiPostExecutionCallDefinition", "newName": "apiPostExecutionCallDefinitionId"}, {"file": "libraries/zod-database-schemas/src/workData/structures/subSchemas/start.ts", "line": 18, "oldName": "title", "newName": "titleId"}, {"file": "libraries/zod-database-schemas/src/workData/structures/subSchemas/start.ts", "line": 26, "oldName": "title", "newName": "titleId"}, {"file": "libraries/zod-database-schemas/src/workData/workEntry/workEntry.ts", "line": 26, "oldName": "workEntryStatus", "newName": "workEntryStatusId"}]