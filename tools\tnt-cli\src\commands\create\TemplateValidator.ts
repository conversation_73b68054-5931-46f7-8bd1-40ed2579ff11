/**
 * Template Valida<PERSON> - <PERSON>les template validation and selection
 *
 * This class is responsible for:
 * - Validating template existence and compatibility
 * - Interactive template selection when not provided
 * - Template configuration retrieval
 *
 * @since 2025-07-02 - Extracted from create.ts during refactoring
 */

import inquirer from 'inquirer';
import chalk from 'chalk';
import type { TemplateConfig } from '../../types.js';
import { BUILT_IN_TEMPLATES } from '../../templates.js';

export class TemplateValidator {
  private readonly availableTemplates: TemplateConfig[];

  constructor() {
    this.availableTemplates = BUILT_IN_TEMPLATES;
  }

  /**
   * Gets a template either from input or interactive selection
   *
   * @param templateName - Optional template name from command line
   * @returns Promise resolving to selected template config
   */
  async getTemplate(templateName?: string): Promise<TemplateConfig> {
    let template = templateName;

    // Interactive template selection if not provided
    if (!template) {
      template = await this.selectTemplateInteractively();
    }

    return this.validateAndGetTemplate(template);
  }

  /**
   * Validates that a template exists and returns its configuration
   *
   * @param templateName - Name of the template to validate
   * @returns Template configuration
   * @throws Error if template is not found
   */
  validateAndGetTemplate(templateName: string): TemplateConfig {
    const selectedTemplate = this.availableTemplates.find(t => t.name === templateName);

    if (!selectedTemplate) {
      this.printAvailableTemplates();
      throw new Error(`Template "${templateName}" not found.`);
    }

    return selectedTemplate;
  }

  /**
   * Presents interactive template selection to user
   *
   * @returns Promise resolving to selected template name
   */
  private async selectTemplateInteractively(): Promise<string> {
    const templateChoices = this.availableTemplates.map((t: TemplateConfig) => ({
      name: `${t.name} - ${t.description}`,
      value: t.name,
    }));

    const answers = await inquirer.prompt([
      {
        type: 'list',
        name: 'template',
        message: 'Select a template:',
        choices: templateChoices,
      },
    ]);

    return answers.template;
  }

  /**
   * Prints available templates to console
   */
  private printAvailableTemplates(): void {
    console.log(chalk.blue('Available templates:'));
    this.availableTemplates.forEach((t: TemplateConfig) => {
      console.log(chalk.blue(`  - ${t.name}: ${t.description}`));
    });
  }

  /**
   * Gets all available templates
   *
   * @returns Array of available template configurations
   */
  getAvailableTemplates(): TemplateConfig[] {
    return [...this.availableTemplates];
  }
}
