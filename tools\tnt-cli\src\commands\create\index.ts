/**
 * Create Command Module - Exports for the create command
 *
 * This module provides a clean interface for importing all create command
 * related classes and types. It serves as the main entry point for the
 * refactored create command functionality.
 *
 * @since 2025-07-02 - Created during create command refactoring
 */

// Types
export type { TemplateVariables, GenerationContext, GenerationResult, FileConfig } from './types.js';

// Core classes
export { ProjectCreator } from './ProjectCreator.js';
export { TemplateValidator } from './TemplateValidator.js';
export { ProjectNameValidator } from './ProjectNameValidator.js';
export { DirectoryManager } from './DirectoryManager.js';
export { ConfigurationManager } from './ConfigurationManager.js';
export { ProjectSetupManager } from './ProjectSetupManager.js';

// Generator classes
export { BaseProjectGenerator } from './BaseProjectGenerator.js';
export { ServiceProjectGenerator } from './ServiceProjectGenerator.js';
export { LibraryProjectGenerator } from './LibraryProjectGenerator.js';
