/**
 * Types specific to the create command module
 *
 * This module contains interfaces and types used throughout the create command
 * refactoring to support better type safety and code organization.
 *
 * @since 2025-07-02 - Initial creation during create command refactoring
 */

import type { CreateOptions } from '../../types.js';

/**
 * Variables used during template processing
 */
export interface TemplateVariables {
  projectName: string;
  projectNameKebab: string;
  projectNamePascal: string;
  projectNameCamel: string;
  packageName: string;
  description: string;
  type: string;
  serverPort: string;
  serverHost: string;
  enableAuth: string;
  enableDatabase: string;
  enableGrpc: string;
  useServerLib: string;
  logLevel: string;
}

/**
 * Context object passed to generators
 */
export interface GenerationContext {
  targetPath: string;
  variables: TemplateVariables;
  options: CreateOptions;
  workspaceRoot: string;
}

/**
 * Result of project generation
 */
export interface GenerationResult {
  success: boolean;
  projectPath: string;
  projectName: string;
  templateType: string;
  message?: string;
  error?: Error;
}

/**
 * Configuration for different file types
 */
export interface FileConfig {
  filename: string;
  content: string | object;
  isJson?: boolean;
}
