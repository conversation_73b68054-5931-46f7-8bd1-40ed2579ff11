/**
 * Doctor Command - Refactored workspace health checking command
 *
 * This file has been refactored from a monolithic 200+ line file into
 * a clean, object-oriented structure with the following benefits:
 *
 * REFACTORING CHANGES (2025-07-02):
 * ===================================
 *
 * 1. EXTRACTED CLASSES:
 *    - DoctorCommandManager: Main orchestrator class
 *    - BaseHealthChecker: Abstract base for all health checkers
 *    - WorkspaceHealthChecker: Workspace root validation
 *    - PackageManagerHealthChecker: Package manager validation
 *    - NodeVersionHealthChecker: Node.js version validation
 *    - TurborepoHealthChecker: Turborepo configuration validation
 *    - PackageStructureHealthChecker: Workspace structure validation
 *    - TemplatesHealthChecker: Template availability validation
 *    - HealthCheckReporter: Result formatting and reporting
 *
 * 2. BENEFITS:
 *    - Single Responsibility Principle: Each checker has one clear purpose
 *    - Open/Closed Principle: Easy to extend with new health checks
 *    - Dependency Injection: Clean separation of concerns
 *    - Better testability: Each checker can be unit tested
 *    - Improved maintainability: Changes localized to specific checkers
 *    - Enhanced reporting: Detailed summaries and categorization
 *
 * 3. REVERT INSTRUCTIONS:
 *    To revert these changes:
 *    - Restore the original doctor.ts from git history
 *    - Delete the /doctor directory and all its files
 *    - Remove the import from './doctor/index.js'
 *
 * 4. TECHNICAL DEBT ADDRESSED:
 *    - ❌ 200+ line function with mixed concerns (now ~50 lines)
 *    - ❌ Hard to add new health checks
 *    - ❌ Difficult to test individual checks
 *    - ❌ No categorization or detailed reporting
 *    - ❌ Poor extensibility for custom checks
 *
 * The command interface remains exactly the same for backward compatibility,
 * with additional options for enhanced functionality.
 */

import type { CommandModule } from 'yargs';
import chalk from 'chalk';
import { DoctorCommandManager, type DoctorOptions } from './doctor/index.js';

interface DoctorCommandArgs {
  verbose?: boolean;
  category?: string;
  detailed?: boolean;
}

export const doctorCommand: CommandModule<object, DoctorCommandArgs> = {
  command: 'doctor',
  describe: 'Check workspace health and configuration',
  builder: yargs => {
    return yargs
      .option('verbose', {
        describe: 'Show verbose output with additional details',
        type: 'boolean',
        default: false,
      })
      .option('category', {
        alias: 'c',
        describe: 'Run checks for specific category only',
        type: 'string',
        choices: ['workspace', 'dependencies', 'configuration', 'structure'],
      })
      .option('detailed', {
        alias: 'd',
        describe: 'Show detailed summary with category breakdown',
        type: 'boolean',
        default: false,
      });
  },
  handler: async argv => {
    try {
      const options: DoctorOptions = {
        verbose: argv.verbose,
        category: argv.category,
        detailed: argv.detailed,
      };

      const doctorManager = new DoctorCommandManager();
      await doctorManager.runHealthChecks(options);
    } catch (error) {
      console.error(chalk.red('Unexpected error during health check:'), error);
      process.exit(1);
    }
  },
};
