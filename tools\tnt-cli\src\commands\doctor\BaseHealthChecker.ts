/**
 * Base Health Checker - Abstract base class for all health checks
 *
 * This abstract class provides the common interface and shared functionality
 * for all health checkers. Specific checkers extend this class to implement
 * check-specific logic.
 *
 * @since 2025-07-02 - Extracted from doctor.ts during refactoring
 */

import type { CheckResult, HealthCheckConfig, HealthCheckContext } from './types.js';

export abstract class BaseHealthChecker {
  protected readonly config: HealthCheckConfig;

  constructor(config: HealthCheckConfig) {
    this.config = config;
  }

  /**
   * Performs the health check
   *
   * @param context - Health check context containing workspace information
   * @returns Promise resolving to check result
   */
  abstract check(context: HealthCheckContext): Promise<CheckResult>;

  /**
   * Gets the configuration for this health checker
   *
   * @returns Health check configuration
   */
  getConfig(): HealthCheckConfig {
    return { ...this.config };
  }

  /**
   * Gets the name of this health checker
   *
   * @returns Checker name
   */
  getName(): string {
    return this.config.name;
  }

  /**
   * Gets the category of this health checker
   *
   * @returns Checker category
   */
  getCategory(): string {
    return this.config.category;
  }

  /**
   * Gets the priority of this health checker
   *
   * @returns Checker priority
   */
  getPriority(): string {
    return this.config.priority;
  }

  /**
   * Creates a successful check result
   *
   * @param message - Success message
   * @returns Successful check result
   */
  protected createSuccessResult(message: string): CheckResult {
    return {
      passed: true,
      message,
    };
  }

  /**
   * Creates a failed check result
   *
   * @param message - Failure message
   * @param suggestion - Optional suggestion for fixing the issue
   * @returns Failed check result
   */
  protected createFailureResult(message: string, suggestion?: string): CheckResult {
    return {
      passed: false,
      message,
      suggestion,
    };
  }

  /**
   * Checks if workspace root is available
   *
   * @param context - Health check context
   * @returns True if workspace root is available, false otherwise
   */
  protected hasWorkspaceRoot(context: HealthCheckContext): boolean {
    return context.workspaceRoot !== null;
  }

  /**
   * Creates a result for when workspace root is not available
   *
   * @returns Failed check result for missing workspace
   */
  protected createNoWorkspaceResult(): CheckResult {
    return this.createFailureResult(
      `Cannot check ${this.config.name.toLowerCase()} - no workspace found`,
      'Run this command from within a TNT monorepo directory',
    );
  }
}
