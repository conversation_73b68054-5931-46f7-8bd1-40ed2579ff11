/**
 * Doctor Command Manager - Main orchestrator for health checks
 *
 * This class coordinates the entire health checking process by:
 * - Managing the collection of health checkers
 * - Orchestrating check execution
 * - Providing different execution modes (quick, detailed, etc.)
 *
 * @since 2025-07-02 - Extracted from doctor.ts during refactoring
 */

import type { HealthCheckContext } from './types.js';
import { <PERSON>HealthChecker } from './BaseHealthChecker.js';
import { WorkspaceHealthChecker } from './WorkspaceHealthChecker.js';
import { PackageManagerHealthChecker } from './PackageManagerHealthChecker.js';
import { NodeVersionHealthChecker } from './NodeVersionHealthChecker.js';
import { TurborepoHealthChecker } from './TurborepoHealthChecker.js';
import { PackageStructureHealthChecker } from './PackageStructureHealthChecker.js';
import { TemplatesHealthChecker } from './TemplatesHealthChecker.js';
import { HealthCheckReporter } from './HealthCheckReporter.js';
import { findWorkspaceRoot } from '../../utils.js';

export interface DoctorOptions {
  verbose?: boolean;
  category?: string;
  detailed?: boolean;
}

export class DoctorCommandManager {
  private readonly checkers: BaseHealthChecker[];
  private readonly reporter: HealthCheckReporter;

  constructor() {
    this.checkers = this.initializeCheckers();
    this.reporter = new HealthCheckReporter();
  }

  /**
   * Runs all health checks
   *
   * @param options - Doctor command options
   */
  async runHealthChecks(options: DoctorOptions = {}): Promise<void> {
    const context = this.createHealthCheckContext(options);
    const checkersToRun = this.filterCheckers(options);

    this.reporter.clearResults();

    for (const checker of checkersToRun) {
      const result = await checker.check(context);
      this.reporter.addResult(checker.getConfig(), result);
    }

    // Display results
    this.reporter.displayResults();

    if (options.detailed) {
      this.reporter.displayDetailedSummary();
    } else {
      this.reporter.displaySummary();
    }
  }

  /**
   * Gets available health check categories
   *
   * @returns Array of available categories
   */
  getAvailableCategories(): string[] {
    const categories = new Set(this.checkers.map(checker => checker.getCategory()));
    return Array.from(categories).sort();
  }

  /**
   * Gets health checkers by category
   *
   * @param category - Category to filter by
   * @returns Array of health checkers in the category
   */
  getCheckersByCategory(category: string): BaseHealthChecker[] {
    return this.checkers.filter(checker => checker.getCategory() === category);
  }

  /**
   * Initializes all health checkers
   *
   * @returns Array of health checker instances
   */
  private initializeCheckers(): BaseHealthChecker[] {
    return [
      new WorkspaceHealthChecker(),
      new PackageManagerHealthChecker(),
      new NodeVersionHealthChecker(),
      new TurborepoHealthChecker(),
      new PackageStructureHealthChecker(),
      new TemplatesHealthChecker(),
    ];
  }

  /**
   * Creates health check context
   *
   * @param options - Doctor command options
   * @returns Health check context
   */
  private createHealthCheckContext(options: DoctorOptions): HealthCheckContext {
    return {
      workspaceRoot: findWorkspaceRoot(),
      verbose: options.verbose || false,
    };
  }

  /**
   * Filters checkers based on options
   *
   * @param options - Doctor command options
   * @returns Array of filtered health checkers
   */
  private filterCheckers(options: DoctorOptions): BaseHealthChecker[] {
    if (options.category) {
      return this.getCheckersByCategory(options.category);
    }

    return this.checkers;
  }
}
