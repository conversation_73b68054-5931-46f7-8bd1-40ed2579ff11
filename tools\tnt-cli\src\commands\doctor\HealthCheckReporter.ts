/**
 * Health Check Reporter - <PERSON>les reporting and summarizing health check results
 *
 * This class is responsible for:
 * - Formatting and displaying health check results
 * - Creating health check summaries
 * - Providing detailed reporting options
 *
 * @since 2025-07-02 - Extracted from doctor.ts during refactoring
 */

import chalk from 'chalk';
import type { CheckResult, HealthCheckSummary, HealthCheckConfig } from './types.js';

export class HealthCheckReporter {
  private results: Array<{ config: HealthCheckConfig; result: CheckResult }> = [];

  /**
   * Adds a health check result
   *
   * @param config - Health check configuration
   * @param result - Health check result
   */
  addResult(config: HealthCheckConfig, result: CheckResult): void {
    this.results.push({ config, result });
  }

  /**
   * Displays all health check results
   */
  displayResults(): void {
    console.log(chalk.blue('🔍 TNT Workspace Health Check\n'));

    for (const { result } of this.results) {
      if (result.passed) {
        console.log(chalk.green(`✅ ${result.message}`));
      } else {
        console.log(chalk.red(`❌ ${result.message}`));
        if (result.suggestion) {
          console.log(chalk.yellow(`   💡 ${result.suggestion}`));
        }
      }
    }
  }

  /**
   * Displays health check summary
   */
  displaySummary(): void {
    const summary = this.generateSummary();

    console.log(`\n📊 Health Score: ${summary.passedChecks}/${summary.totalChecks} checks passed`);

    if (summary.overallHealth === 'healthy') {
      console.log(chalk.green('🎉 Your TNT workspace is healthy!'));
    } else if (summary.overallHealth === 'warning') {
      console.log(chalk.yellow('⚠️  Some issues found. Please review the suggestions above.'));
    } else {
      console.log(chalk.red('🚨 Critical issues found. Please address them before proceeding.'));
    }
  }

  /**
   * Displays detailed summary with categories
   */
  displayDetailedSummary(): void {
    const summary = this.generateSummary();

    this.displaySummary();

    console.log('\n📋 Summary by Category:');
    for (const [category, stats] of Object.entries(summary.categories)) {
      const percentage = Math.round((stats.passed / stats.total) * 100);
      const icon = percentage === 100 ? '✅' : percentage >= 50 ? '⚠️' : '❌';
      console.log(`  ${icon} ${category}: ${stats.passed}/${stats.total} (${percentage}%)`);
    }
  }

  /**
   * Generates health check summary
   *
   * @returns Health check summary
   */
  generateSummary(): HealthCheckSummary {
    const totalChecks = this.results.length;
    const passedChecks = this.results.filter(r => r.result.passed).length;
    const failedChecks = totalChecks - passedChecks;

    // Calculate overall health
    let overallHealth: 'healthy' | 'warning' | 'unhealthy';
    const healthPercentage = (passedChecks / totalChecks) * 100;

    if (healthPercentage === 100) {
      overallHealth = 'healthy';
    } else if (healthPercentage >= 50) {
      overallHealth = 'warning';
    } else {
      overallHealth = 'unhealthy';
    }

    // Generate category statistics
    const categories: Record<string, { passed: number; total: number }> = {};

    for (const { config, result } of this.results) {
      const category = config.category;
      if (!categories[category]) {
        categories[category] = { passed: 0, total: 0 };
      }
      categories[category].total++;
      if (result.passed) {
        categories[category].passed++;
      }
    }

    return {
      totalChecks,
      passedChecks,
      failedChecks,
      warningChecks: 0, // Could be extended for warning-level results
      overallHealth,
      categories,
    };
  }

  /**
   * Gets all results
   *
   * @returns Array of all results with their configurations
   */
  getResults(): Array<{ config: HealthCheckConfig; result: CheckResult }> {
    return [...this.results];
  }

  /**
   * Clears all results
   */
  clearResults(): void {
    this.results = [];
  }
}
