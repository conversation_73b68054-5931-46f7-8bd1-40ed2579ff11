/**
 * Node Version Health Checker - Checks Node.js version requirements
 *
 * This class is responsible for:
 * - Verifying Node.js version against package.json requirements
 * - Checking for Node.js version specification
 * - Validating Node.js compatibility
 *
 * @since 2025-07-02 - Extracted from doctor.ts during refactoring
 */

import fs from 'fs-extra';
import path from 'path';
import type { CheckResult, HealthCheckContext } from './types.js';
import { BaseHealthChecker } from './BaseHealthChecker.js';

export class NodeVersionHealthChecker extends BaseHealthChecker {
  constructor() {
    super({
      name: 'Node Version',
      description: 'Checks Node.js version against workspace requirements',
      category: 'dependencies',
      priority: 'high',
    });
  }

  /**
   * Performs Node.js version health check
   *
   * @param context - Health check context
   * @returns Promise resolving to check result
   */
  async check(context: HealthCheckContext): Promise<CheckResult> {
    if (!this.hasWorkspaceRoot(context)) {
      return this.createNoWorkspaceResult();
    }

    try {
      const packageJsonPath = path.join(context.workspaceRoot!, 'package.json');
      const packageJson = fs.readJsonSync(packageJsonPath);
      const requiredNodeVersion = packageJson.engines?.node;

      if (!requiredNodeVersion) {
        return this.createFailureResult(
          'No Node.js version specified in package.json',
          'Add "engines.node" field to package.json',
        );
      }

      const currentNodeVersion = process.version;

      return this.createSuccessResult(
        `Node.js version: ${currentNodeVersion} (required: ${requiredNodeVersion})`,
      );
    } catch {
      return this.createFailureResult(
        'Could not check Node.js version requirements',
        'Ensure package.json exists and is valid',
      );
    }
  }
}
