/**
 * Package Manager Health Checker - Checks package manager configuration
 *
 * This class is responsible for:
 * - Verifying package manager type (pnpm recommended)
 * - Checking package manager configuration
 * - Validating workspace package setup
 *
 * @since 2025-07-02 - Extracted from doctor.ts during refactoring
 */

import type { CheckResult, HealthCheckContext } from './types.js';
import { BaseHealthChecker } from './BaseHealthChecker.js';
import { getWorkspaceInfo } from '../../utils.js';

export class PackageManagerHealthChecker extends BaseHealthChecker {
  constructor() {
    super({
      name: 'Package Manager',
      description: 'Checks package manager configuration and type',
      category: 'dependencies',
      priority: 'high',
    });
  }

  /**
   * Performs package manager health check
   *
   * @param context - Health check context
   * @returns Promise resolving to check result
   */
  async check(context: HealthCheckContext): Promise<CheckResult> {
    if (!this.hasWorkspaceRoot(context)) {
      return this.createNoWorkspaceResult();
    }

    const workspaceInfo = getWorkspaceInfo(context.workspaceRoot!);

    if (workspaceInfo.packageManager === 'pnpm') {
      return this.createSuccessResult(`Package manager: ${workspaceInfo.packageManager} (recommended)`);
    }

    return this.createFailureResult(
      `Package manager: ${workspaceInfo.packageManager}`,
      'Consider using pnpm for better workspace support and performance',
    );
  }
}
