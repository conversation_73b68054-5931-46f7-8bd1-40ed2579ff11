/**
 * Package Structure Health Checker - Checks workspace package structure
 *
 * This class is responsible for:
 * - Verifying expected workspace directories exist
 * - Checking package organization
 * - Validating monorepo structure
 *
 * @since 2025-07-02 - Extracted from doctor.ts during refactoring
 */

import fs from 'fs-extra';
import path from 'path';
import type { CheckResult, HealthCheckContext } from './types.js';
import { BaseHealthChecker } from './BaseHealthChecker.js';

export class PackageStructureHealthChecker extends BaseHealthChecker {
  private readonly expectedDirs = ['libraries', 'servers', 'websites', 'tools', 'config-packages'];

  constructor() {
    super({
      name: 'Package Structure',
      description: 'Checks workspace package directory structure',
      category: 'structure',
      priority: 'medium',
    });
  }

  /**
   * Performs package structure health check
   *
   * @param context - Health check context
   * @returns Promise resolving to check result
   */
  async check(context: HealthCheckContext): Promise<CheckResult> {
    if (!this.hasWorkspaceRoot(context)) {
      return this.createNoWorkspaceResult();
    }

    const existingDirs = this.getExistingDirectories(context.workspaceRoot!);

    if (existingDirs.length >= 3) {
      return this.createSuccessResult(
        `Package structure looks good (${existingDirs.length}/${this.expectedDirs.length} directories found)`,
      );
    }

    const missingDirs = this.getMissingDirectories(existingDirs);
    return this.createFailureResult(
      `Missing workspace directories (${existingDirs.length}/${this.expectedDirs.length} found)`,
      `Create missing directories: ${missingDirs.join(', ')}`,
    );
  }

  /**
   * Gets existing directories from the expected list
   *
   * @param workspaceRoot - Workspace root path
   * @returns Array of existing directory names
   */
  private getExistingDirectories(workspaceRoot: string): string[] {
    return this.expectedDirs.filter(dir => fs.existsSync(path.join(workspaceRoot, dir)));
  }

  /**
   * Gets missing directories from the expected list
   *
   * @param existingDirs - Array of existing directory names
   * @returns Array of missing directory names
   */
  private getMissingDirectories(existingDirs: string[]): string[] {
    return this.expectedDirs.filter(dir => !existingDirs.includes(dir));
  }
}
