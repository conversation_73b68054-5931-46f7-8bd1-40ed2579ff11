/**
 * Templates Health Checker - Checks template availability and structure
 *
 * This class is responsible for:
 * - Verifying template directory exists
 * - Checking available templates
 * - Validating template structure
 *
 * @since 2025-07-02 - Extracted from doctor.ts during refactoring
 */

import fs from 'fs-extra';
import path from 'path';
import type { CheckResult, HealthCheckContext } from './types.js';
import { BaseHealthChecker } from './BaseHealthChecker.js';

export class Templates<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends BaseHealthChecker {
  constructor() {
    super({
      name: 'Templates',
      description: 'Checks template availability and configuration',
      category: 'configuration',
      priority: 'low',
    });
  }

  /**
   * Performs templates health check
   *
   * @param context - Health check context
   * @returns Promise resolving to check result
   */
  async check(context: HealthCheckContext): Promise<CheckResult> {
    if (!this.hasWorkspaceRoot(context)) {
      return this.createNoWorkspaceResult();
    }

    const templatesDir = path.join(context.workspaceRoot!, 'tools', 'templates');

    if (!fs.existsSync(templatesDir)) {
      return this.createFailureResult(
        'Templates directory not found',
        'Run "tnt create" to set up default templates',
      );
    }

    const templateDirs = this.getTemplateDirectories(templatesDir);

    if (templateDirs.length === 0) {
      return this.createFailureResult('No templates found', 'Run "tnt create" to set up default templates');
    }

    return this.createSuccessResult(`Found ${templateDirs.length} template(s)`);
  }

  /**
   * Gets template directories from the templates folder
   *
   * @param templatesDir - Path to templates directory
   * @returns Array of template directory entries
   */
  private getTemplateDirectories(templatesDir: string): fs.Dirent[] {
    try {
      return fs.readdirSync(templatesDir, { withFileTypes: true }).filter(dirent => dirent.isDirectory());
    } catch {
      return [];
    }
  }
}
