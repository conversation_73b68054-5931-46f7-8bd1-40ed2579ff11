/**
 * Turborepo Health Checker - Checks Turborepo configuration
 *
 * This class is responsible for:
 * - Verifying Turborepo configuration exists
 * - Checking turbo.json validity
 * - Validating build optimization setup
 *
 * @since 2025-07-02 - Extracted from doctor.ts during refactoring
 */

import type { CheckResult, HealthCheckContext } from './types.js';
import { BaseHealthChecker } from './BaseHealthChecker.js';
import { getWorkspaceInfo } from '../../utils.js';

export class TurborepoHealthChecker extends BaseHealthChecker {
  constructor() {
    super({
      name: 'Turborepo',
      description: 'Checks Turborepo configuration and setup',
      category: 'configuration',
      priority: 'medium',
    });
  }

  /**
   * Performs Turborepo health check
   *
   * @param context - Health check context
   * @returns Promise resolving to check result
   */
  async check(context: HealthCheckContext): Promise<CheckResult> {
    if (!this.hasWorkspaceRoot(context)) {
      return this.createNoWorkspaceResult();
    }

    const workspaceInfo = getWorkspaceInfo(context.workspaceRoot!);

    if (workspaceInfo.hasTurbo) {
      return this.createSuccessResult('Turborepo configuration found');
    }

    return this.createFailureResult(
      'Turborepo configuration missing',
      'Add turbo.json to enable build optimization and caching',
    );
  }
}
