/**
 * Workspace Health Checker - Checks workspace root and basic structure
 *
 * This class is responsible for:
 * - Verifying workspace root exists
 * - Checking basic workspace structure
 * - Validating workspace configuration files
 *
 * @since 2025-07-02 - Extracted from doctor.ts during refactoring
 */

import type { CheckResult, HealthCheckContext } from './types.js';
import { <PERSON>Health<PERSON>hecker } from './BaseHealthChecker.js';

export class WorkspaceHealthChecker extends BaseHealthChecker {
  constructor() {
    super({
      name: 'Workspace Root',
      description: 'Checks if running in a valid TNT workspace',
      category: 'workspace',
      priority: 'high',
    });
  }

  /**
   * Performs workspace root health check
   *
   * @param context - Health check context
   * @returns Promise resolving to check result
   */
  async check(context: HealthCheckContext): Promise<CheckResult> {
    if (!this.hasWorkspaceRoot(context)) {
      return this.createFailureResult(
        'Not in a TNT workspace',
        'Run this command from within a TNT monorepo directory',
      );
    }

    return this.createSuccessResult(`Workspace root found: ${context.workspaceRoot}`);
  }
}
