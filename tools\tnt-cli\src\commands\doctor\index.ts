/**
 * Doctor Command Module - Exports for the doctor command
 *
 * This module provides a clean interface for importing all doctor command
 * related classes and types. It serves as the main entry point for the
 * refactored doctor command functionality.
 *
 * @since 2025-07-02 - Created during doctor command refactoring
 */

// Types
export type { CheckResult, HealthCheckConfig, HealthCheckContext, HealthCheckSummary } from './types.js';

// Core classes
export { DoctorCommandManager, type DoctorOptions } from './DoctorCommandManager.js';
export { HealthCheckReporter } from './HealthCheckReporter.js';

// Base classes
export { BaseHealthChecker } from './BaseHealthChecker.js';

// Health checker implementations
export { WorkspaceHealthChecker } from './WorkspaceHealthChecker.js';
export { PackageManagerHealthChecker } from './PackageManagerHealthChecker.js';
export { NodeVersionHealthChecker } from './NodeVersionHealthChecker.js';
export { TurborepoHealthChecker } from './TurborepoHealthChecker.js';
export { PackageStructureHealthChecker } from './PackageStructureHealthChecker.js';
export { TemplatesHealthChecker } from './TemplatesHealthChecker.js';
