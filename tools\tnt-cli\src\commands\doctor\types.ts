import type { WorkspaceInfo } from '../../types.js';

/**
 * Types specific to the doctor command module
 *
 * This module contains interfaces and types used throughout the doctor command
 * refactoring to support better type safety and code organization.
 *
 * @since 2025-07-02 - Initial creation during doctor command refactoring
 */

/**
 * Result of a health check
 */
export interface CheckResult {
  passed: boolean;
  message: string;
  suggestion?: string;
}

/**
 * Configuration for a health check
 */
export interface HealthCheckConfig {
  name: string;
  description: string;
  category: 'workspace' | 'dependencies' | 'configuration' | 'structure';
  priority: 'high' | 'medium' | 'low';
}

/**
 * Summary of all health checks
 */
export interface HealthCheckSummary {
  totalChecks: number;
  passedChecks: number;
  failedChecks: number;
  warningChecks: number;
  overallHealth: 'healthy' | 'warning' | 'unhealthy';
  categories: Record<string, { passed: number; total: number }>;
}

/**
 * Context passed to health checkers
 */
export interface HealthCheckContext {
  workspaceRoot: string | null;
  workspaceInfo?: WorkspaceInfo;
  verbose?: boolean;
}
