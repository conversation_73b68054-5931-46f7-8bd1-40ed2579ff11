import type { CommandModule } from 'yargs';
import chalk from 'chalk';
import { BUILT_IN_TEMPLATES } from '../templates.js';

export const templatesCommand: CommandModule = {
  command: 'templates',
  describe: 'List available project templates',
  handler: async () => {
    try {
      const templates = BUILT_IN_TEMPLATES;

      console.log(chalk.green('\n📋 Available Templates:\n'));

      templates.forEach(template => {
        console.log(chalk.blue(`🔹 ${template.name}`));
        console.log(chalk.gray(`   ${template.description}`));
        console.log(chalk.gray(`   Type: ${template.type}`));

        const depKeys = Object.keys(template.dependencies || {});
        if (depKeys.length > 0) {
          console.log(chalk.gray(`   Dependencies: ${depKeys.join(', ')}`));
        }

        console.log('');
      });

      console.log(chalk.green(`Total: ${templates.length} template(s) available`));
      console.log(chalk.blue('\nUsage: tnt create <template-name> <project-name>'));
    } catch (error) {
      console.error(chalk.red('Error listing templates:'), error);
      process.exit(1);
    }
  },
};
