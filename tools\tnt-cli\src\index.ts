#!/usr/bin/env node

import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';
import chalk from 'chalk';
import { createCommand } from './commands/create.js';
import { templatesCommand } from './commands/templates.js';
import { doctorCommand } from './commands/doctor.js';

const cli = yargs(hideBin(process.argv))
  .scriptName('tnt')
  .usage('$0 <command> [options]')
  .version('1.0.0')
  .demandCommand(1, chalk.red('You must specify a command'))
  .help()
  .alias('h', 'help')
  .alias('v', 'version')
  .command(createCommand)
  .command(templatesCommand)
  .command(doctorCommand)
  .example('$0 create service my-api', 'Create a new service using fastify-server-lib')
  .example('$0 create service my-api --enable-auth --enable-grpc', 'Create service with auth and gRPC')
  .example(
    '$0 create service my-api --port 3000 --enable-database',
    'Create service with custom port and database',
  )
  .example('$0 templates', 'List available templates')
  .example('$0 doctor', 'Check workspace health')
  .epilogue('For more information, visit: https://github.com/your-org/tnt')
  .strict()
  .recommendCommands()
  .showHelpOnFail(false, 'Specify --help for available options');

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('Unhandled Rejection at:'), promise, chalk.red('reason:'), reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', error => {
  console.error(chalk.red('Uncaught Exception:'), error);
  process.exit(1);
});

cli.parse();
