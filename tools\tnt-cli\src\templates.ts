import type { TemplateConfig } from './types.js';

/**
 * Built-in templates for the TNT CLI
 * These templates don't require external files and generate projects programmatically
 */
export const BUILT_IN_TEMPLATES: TemplateConfig[] = [
  {
    name: 'library',
    description: 'A new TypeScript library with testing and build configuration',
    type: 'library',
    dependencies: {
      '@tnt/error-logger': 'workspace:*',
      '@tnt/zod-client-schemas': 'workspace:*',
    },
    devDependencies: {
      '@jest/globals': '^29.7.0',
      '@tnt/eslint-config': 'workspace:*',
      '@tnt/typescript-config': 'workspace:*',
      '@types/jest': '^29.5.14',
      '@types/node': '^22.8.7',
      'jest': '^29.7.0',
      'ts-jest': '^29.3.2',
      'typescript': '^5.6.3',
    },
    scripts: {
      build: 'tsc',
      dev: 'tsc --watch',
      lint: 'eslint .',
      test: 'jest ./src',
    },
    files: [],
    tsconfigPatch: {
      compilerOptions: {
        paths: {
          '@tnt/*': ['../../libraries/*/lib'],
          '@connectrpc/*': ['../../node_modules/@connectrpc/*'],
          '@bufbuild/*': ['../../node_modules/@bufbuild/*'],
        },
      },
    },
  },
  {
    name: 'server',
    description: 'A new TNT server with Fastify, ConnectRPC, and optional auth/grpc',
    type: 'service',
    dependencies: {
      '@tnt/fastify-server-lib': 'workspace:*',
      '@tnt/env-loader': 'workspace:*',
      '@tnt/error-logger': 'workspace:*',
      '@tnt/jest-presets': 'workspace:*',
      '@tnt/typescript-config': 'workspace:*',
      '@tnt/zod-client-schemas': 'workspace:*',
      '@tnt/zod-database-schemas': 'workspace:*',
      '@tnt/mongo-client': 'workspace:*',
      '@tnt/protos-gen': 'workspace:*',
      '@connectrpc/connect': '^2.0.2',
      '@connectrpc/connect-fastify': '^2.0.2',
      '@connectrpc/connect-node': '^2.0.2',
      '@connectrpc/connect-web': '^2.0.2',
      '@bufbuild/buf': '^1.0.0',
      '@bufbuild/protobuf': '^2.0.0',
      '@bufbuild/protoc-gen-es': '^2.0.0',
    },
    devDependencies: {
      '@jest/globals': '^29.7.0',
      '@tnt/create-env-file': 'workspace:*',
      '@tnt/eslint-config': 'workspace:*',
      '@tnt/jest-presets': 'workspace:*',
      '@tnt/typescript-config': 'workspace:*',
      '@types/jest': '^29.5.14',
      '@types/node': '^22.8.7',
      'jest': '^29.7.0',
      'nodemon': '^3.1.7',
      'pino-pretty': '^13.0.0',
      'ts-jest': '^29.3.2',
      'tsx': '^4.7.0',
      'typescript': '^5.6.3',
    },
    scripts: {
      build: 'tsc',
      createEnv: 'tsx ./scripts/createEnv.mjs',
      dev: 'nodemon',
      lint: 'eslint .',
      test: 'jest ./src',
      start: 'node dist/index.js',
    },
    files: [],
    tsconfigPatch: {
      compilerOptions: {
        paths: {
          '@tnt/*': ['../../libraries/*/lib'],
          '@connectrpc/*': ['../../node_modules/@connectrpc/*'],
          '@bufbuild/*': ['../../node_modules/@bufbuild/*'],
        },
      },
    },
  },
];
