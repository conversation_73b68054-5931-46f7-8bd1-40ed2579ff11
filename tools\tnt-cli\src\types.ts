export interface CreateOptions {
  template?: string;
  name?: string;
  directory?: string;
  force?: boolean;
  // Fastify Server Lib options
  useServerLib?: boolean;
  port?: number;
  host?: string;
  enableAuth?: boolean;
  enableDatabase?: boolean;
  enableGrpc?: boolean;
  logLevel?: 'debug' | 'info' | 'warn' | 'error';
  example?: string; // NEW: structured example name
}
export interface TemplateConfig {
  name: string;
  description: string;
  type: 'service' | 'website' | 'library' | 'worker';
  dependencies: Record<string, string>;
  devDependencies: Record<string, string>;
  scripts: Record<string, string>;
  files: string[];
  prompts?: TemplatePrompt[];
  tsconfigPatch?: Record<string, any>;
}

export interface TemplatePrompt {
  name: string;
  type: 'input' | 'confirm' | 'list' | 'checkbox';
  message: string;
  choices?: string[];
  default?: string | boolean;
  validate?: (input: string) => boolean | string;
}

export interface CreateOptions {
  template?: string;
  name?: string;
  directory?: string;
  force?: boolean;
  // Fastify Server Lib options
  useServerLib?: boolean;
  port?: number;
  host?: string;
  enableAuth?: boolean;
  enableDatabase?: boolean;
  enableGrpc?: boolean;
  logLevel?: 'debug' | 'info' | 'warn' | 'error';
  example?: string; // NEW: structured example name
}

export interface WorkspaceInfo {
  root: string;
  packageManager: 'pnpm' | 'npm' | 'yarn';
  hasTypescript: boolean;
  hasTurbo: boolean;
  packages: string[];
}
