// Recursively replace {{PORT}} in all files in a directory
export async function replacePortPlaceholders(targetDir: string, port: string | number) {
  const exts = [
    '.ts',
    '.js',
    '.json',
    '.env',
    '.md',
    '.yml',
    '.yaml',
    '.dockerfile',
    'Dockerfile',
    '.env.example',
    'env.example',
    '.env.local',
  ];
  async function processFile(filePath: string) {
    let content = await fs.readFile(filePath, 'utf8');
    if (content.includes('{{PORT}}')) {
      content = content.replace(/{{PORT}}/g, String(port));
      await fs.writeFile(filePath, content, 'utf8');
    }
  }
  async function walk(dir: string) {
    const entries = await fs.readdir(dir, { withFileTypes: true });
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      if (entry.isDirectory()) {
        await walk(fullPath);
      } else if (
        exts.some(ext => entry.name.endsWith(ext) || entry.name === ext) ||
        entry.name === '.env.example'
      ) {
        await processFile(fullPath);
      }
    }
  }
  await walk(targetDir);
}
import path from 'path';
import fs from 'fs-extra';
import { execSync } from 'child_process';
import type { WorkspaceInfo } from './types.js';

export function findWorkspaceRoot(startDir: string = process.cwd()): string | null {
  let currentDir = startDir;

  while (currentDir !== path.parse(currentDir).root) {
    const packageJsonPath = path.join(currentDir, 'package.json');
    const pnpmWorkspacePath = path.join(currentDir, 'pnpm-workspace.yaml');
    const turboJsonPath = path.join(currentDir, 'turbo.json');

    if (
      fs.existsSync(packageJsonPath) &&
      (fs.existsSync(pnpmWorkspacePath) || fs.existsSync(turboJsonPath))
    ) {
      return currentDir;
    }

    currentDir = path.dirname(currentDir);
  }

  return null;
}

export function getWorkspaceInfo(workspaceRoot: string): WorkspaceInfo {
  const packageManager = fs.existsSync(path.join(workspaceRoot, 'pnpm-lock.yaml'))
    ? 'pnpm'
    : fs.existsSync(path.join(workspaceRoot, 'yarn.lock'))
      ? 'yarn'
      : 'npm';

  const hasTypescript = fs.existsSync(path.join(workspaceRoot, 'tsconfig.json'));
  const hasTurbo = fs.existsSync(path.join(workspaceRoot, 'turbo.json'));

  // Get all packages in workspace
  const packages: string[] = [];
  const workspaceDirs = ['libraries', 'servers', 'websites', 'tools', 'config-packages'];

  for (const dir of workspaceDirs) {
    const dirPath = path.join(workspaceRoot, dir);
    if (fs.existsSync(dirPath)) {
      const subDirs = fs
        .readdirSync(dirPath, { withFileTypes: true })
        .filter(dirent => dirent.isDirectory())
        .map(dirent => path.join(dir, dirent.name));
      packages.push(...subDirs);
    }
  }

  return {
    root: workspaceRoot,
    packageManager,
    hasTypescript,
    hasTurbo,
    packages,
  };
}

export function validatePackageName(name: string): boolean | string {
  if (!name || name.length === 0) {
    return 'Package name is required';
  }

  if (!/^[a-z0-9-]+$/.test(name)) {
    return 'Package name must contain only lowercase letters, numbers, and hyphens';
  }

  if (name.startsWith('-') || name.endsWith('-')) {
    return 'Package name cannot start or end with a hyphen';
  }

  if (name.length > 50) {
    return 'Package name must be less than 50 characters';
  }

  return true;
}

export function runCommand(command: string, cwd?: string): void {
  execSync(command, {
    cwd,
    stdio: 'inherit',
    env: { ...process.env },
  });
}

export function kebabCase(str: string): string {
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .toLowerCase();
}

export function pascalCase(str: string): string {
  return str
    .split(/[-_\s]+/)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join('');
}

export function camelCase(str: string): string {
  const pascal = pascalCase(str);
  return pascal.charAt(0).toLowerCase() + pascal.slice(1);
}
